@echo off
chcp 65001 > nul
title Phone Doctor v2.0 - نظام إدارة محلات صيانة الهواتف المتطور

echo.
echo ========================================
echo   Phone Doctor v2.0
echo   نظام إدارة محلات صيانة الهواتف المتطور
echo   المطور: محمد الشوامرة - 0566000140
echo   جميع الحقوق محفوظة © 2024
echo ========================================
echo.

echo جاري تشغيل البرنامج...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات المطلوبة
echo التحقق من المكتبات المطلوبة...
python -c "import cryptography" >nul 2>&1
if errorlevel 1 (
    echo تثبيت مكتبة التشفير...
    pip install cryptography
)

REM تشغيل البرنامج
python phone_doctor_working.py

if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل البرنامج!
    echo يرجى التأكد من تثبيت Python وجميع المكتبات المطلوبة.
    echo.
    echo لتثبيت المكتبات المطلوبة، قم بتشغيل:
    echo pip install tkinter
    echo.
    pause
)

echo.
echo تم إغلاق البرنامج.
pause
