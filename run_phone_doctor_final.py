#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0 النهائي الشامل
Final Comprehensive Phone Doctor Launcher

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess
import time

def print_final_header():
    """طباعة الهيدر النهائي"""
    print("🏆" * 50)
    print("🎯 Phone Doctor v2.0 - Final Comprehensive Edition 🎯")
    print("🏆 جميع النسخ محدثة ومحسنة مع البيانات الحقيقية 🏆")
    print("💎 المطور: محمد الشوامرة - 0566000140 💎")
    print("🏆" * 50)

def check_system():
    """فحص النظام"""
    print("\n🔍 فحص النظام...")
    
    # فحص Python
    if sys.version_info.major < 3:
        print("❌ يتطلب Python 3.0 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص tkinter
    try:
        import tkinter
        print("✅ Tkinter متاح")
    except ImportError:
        print("❌ Tkinter غير متاح")
        return False
    
    # فحص sqlite3
    try:
        import sqlite3
        print("✅ SQLite3 متاح")
    except ImportError:
        print("❌ SQLite3 غير متاح")
        return False
    
    return True

def check_files():
    """فحص الملفات"""
    print("\n📁 فحص الملفات...")
    
    versions = {
        'النسخة المحسنة (مُوصى بها)': 'phone_doctor_enhanced.py',
        'النسخة النهائية': 'phone_doctor_final.py',
        'النسخة السحرية': 'phone_doctor_magic.py',
        'النسخة المبسطة': 'phone_doctor_no_icons.py'
    }
    
    available_versions = {}
    
    for name, file in versions.items():
        if os.path.exists(file):
            print(f"✅ {name}: {file}")
            available_versions[name] = file
        else:
            print(f"❌ {name}: {file}")
    
    # فحص قاعدة البيانات
    if os.path.exists('database/phone_doctor.db'):
        print("✅ قاعدة البيانات: database/phone_doctor.db")
        db_available = True
    else:
        print("❌ قاعدة البيانات: database/phone_doctor.db")
        db_available = False
    
    return available_versions, db_available

def check_data():
    """فحص البيانات"""
    print("\n🗄️ فحص البيانات...")
    
    try:
        import sqlite3
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ {len(tables)} جدول متاح")
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM repairs")
        repairs = cursor.fetchone()[0]
        print(f"🔧 {repairs} أمر صيانة")
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers = cursor.fetchone()[0]
        print(f"👥 {customers} عميل")
        
        cursor.execute("SELECT COUNT(*) FROM checks")
        checks = cursor.fetchone()[0]
        print(f"💳 {checks} شيك")
        
        # حساب الإحصائيات المالية
        cursor.execute("SELECT SUM(total_cost) FROM repairs WHERE total_cost > 0")
        revenue = cursor.fetchone()[0] or 0
        print(f"💰 {revenue:.0f} ₪ إجمالي الإيرادات")
        
        conn.close()
        return True, repairs, customers, checks, revenue
        
    except Exception as e:
        print(f"❌ خطأ في البيانات: {e}")
        return False, 0, 0, 0, 0

def launch_version(file_path, version_name):
    """تشغيل نسخة معينة"""
    print(f"\n🚀 تشغيل {version_name}...")
    
    try:
        process = subprocess.Popen([sys.executable, file_path])
        print(f"✅ تم تشغيل {version_name} بنجاح!")
        print("💎 ابحث عن النافذة على شاشتك")
        return True
    except Exception as e:
        print(f"❌ فشل في تشغيل {version_name}: {e}")
        return False

def show_login_info():
    """عرض معلومات تسجيل الدخول"""
    print("\n" + "🔐" * 40)
    print("معلومات تسجيل الدخول:")
    print("🔐" * 40)
    print("👑 المدير:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("   الصلاحيات: كاملة بدون قيود")
    print()
    print("💼 موظف المبيعات:")
    print("   اسم المستخدم: sales")
    print("   كلمة المرور: sales123")
    print("   الصلاحيات: محدودة (تحتاج ترخيص)")
    print("🔐" * 40)

def show_versions_info():
    """عرض معلومات النسخ"""
    print("\nمعلومات النسخ المتاحة:")
    print()
    print("🏆 النسخة المحسنة (مُوصى بها):")
    print("   ✨ نموذج تسجيل دخول محسن ومتطور")
    print("   📊 جميع البيانات الحقيقية متاحة")
    print("   🎨 تصميم احترافي مع تأثيرات تفاعلية")
    print("   🚀 أداء مستقر وسريع")
    print()
    print("💎 النسخة النهائية:")
    print("   📊 بيانات حقيقية شاملة")
    print("   🔧 50 أمر صيانة، 20 عميل، 25 شيك")
    print("   💰 إحصائيات مالية حية")
    print("   🏢 مناسبة للاستخدام التجاري")
    print()
    print("🔮 النسخة السحرية:")
    print("   ✨ تأثيرات سحرية متطورة")
    print("   🌟 ألوان ديناميكية ونبضات")
    print("   🎭 بوابة سحرية كاملة")
    print("   🎪 تجربة بصرية مميزة")
    print()
    print("🚀 النسخة المبسطة:")
    print("   💻 تعمل على جميع الأنظمة")
    print("   🔧 بدون تعقيدات")
    print("   ⚡ سريعة وموثوقة")

def main():
    """الدالة الرئيسية"""
    print_final_header()
    
    # فحص النظام
    if not check_system():
        print("\n❌ فشل فحص النظام")
        input("اضغط Enter للخروج...")
        return
    
    # فحص الملفات
    available_versions, db_available = check_files()
    
    if not available_versions:
        print("\n❌ لا توجد نسخ متاحة")
        input("اضغط Enter للخروج...")
        return
    
    # فحص البيانات
    data_ok, repairs, customers, checks, revenue = check_data()
    
    print("\n" + "=" * 60)
    print("اختيار النسخة للتشغيل:")
    print("=" * 60)
    
    # عرض النسخ المتاحة
    version_list = list(available_versions.items())
    for i, (name, file) in enumerate(version_list, 1):
        status = "✅ مع بيانات" if data_ok else "⚠️ بدون بيانات"
        if "المحسنة" in name:
            print(f"{i}. {name} {status} ⭐ مُوصى بها")
        else:
            print(f"{i}. {name} {status}")
    
    print(f"{len(version_list) + 1}. عرض معلومات النسخ")
    print(f"{len(version_list) + 2}. إلغاء")
    
    choice = input(f"\nاختر النسخة (1-{len(version_list) + 2}): ").strip()
    
    try:
        choice_num = int(choice)
        
        if 1 <= choice_num <= len(version_list):
            version_name, file_path = version_list[choice_num - 1]
            
            if launch_version(file_path, version_name):
                show_login_info()
                
                if data_ok:
                    print(f"\n📊 البيانات المتاحة:")
                    print(f"   🔧 {repairs} أمر صيانة")
                    print(f"   👥 {customers} عميل")
                    print(f"   💳 {checks} شيك")
                    print(f"   💰 {revenue:.0f} ₪ إيرادات")
                
                print(f"\n🎉 تم تشغيل {version_name} بنجاح!")
                print("💎 استمتع بالتجربة الاحترافية!")
            
        elif choice_num == len(version_list) + 1:
            show_versions_info()
            
        elif choice_num == len(version_list) + 2:
            print("تم الإلغاء")
            
        else:
            print("❌ اختيار غير صحيح")
            
    except ValueError:
        print("❌ يرجى إدخال رقم صحيح")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
