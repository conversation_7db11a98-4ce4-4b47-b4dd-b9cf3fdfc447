@echo off
chcp 65001 > nul
title اختبار متطلبات النظام - Phone Doctor

echo.
echo ========================================
echo   🧪 اختبار متطلبات النظام
echo   Phone Doctor Modern
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...
echo.

:: فحص نظام التشغيل
echo 💻 نظام التشغيل:
ver
echo.

:: فحص الذاكرة
echo 🧠 معلومات الذاكرة:
wmic computersystem get TotalPhysicalMemory /format:value | find "TotalPhysicalMemory"
echo.

:: فحص المساحة المتاحة
echo 💾 المساحة المتاحة:
dir | find "bytes free"
echo.

:: فحص صلاحيات الكتابة
echo 📝 اختبار صلاحيات الكتابة...
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo ✅ صلاحيات الكتابة متاحة
    del test_write.tmp
) else (
    echo ❌ صلاحيات الكتابة غير متاحة
)
echo.

echo ========================================
echo 🎯 النتيجة: النظام جاهز لتشغيل Phone Doctor
echo ========================================
echo.

pause
