#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0 Enhanced Edition
Enhanced Edition Launcher with Improved Login

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess

def print_enhanced_header():
    """طباعة الهيدر المحسن"""
    print("💎" * 35)
    print("✨ Phone Doctor v2.0 Enhanced Edition ✨")
    print("💎 نموذج تسجيل دخول محسن ومتطور 💎")
    print("🎨 المطور: محمد الشوامرة - 0566000140 🎨")
    print("💎" * 35)

def check_enhanced_requirements():
    """فحص متطلبات النسخة المحسنة"""
    print("\n🔍 فحص المتطلبات المحسنة...")
    
    # فحص Python
    if sys.version_info.major < 3:
        print("❌ يتطلب Python 3.0 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص tkinter
    try:
        import tkinter
        print("✅ Tkinter متاح")
    except ImportError:
        print("❌ Tkinter غير متاح")
        return False
    
    # فحص sqlite3
    try:
        import sqlite3
        print("✅ SQLite3 متاح")
    except ImportError:
        print("❌ SQLite3 غير متاح")
        return False
    
    return True

def check_enhanced_files():
    """فحص الملفات المحسنة"""
    print("\n📁 فحص الملفات المحسنة...")
    
    required_files = [
        'phone_doctor_enhanced.py',
        'database/phone_doctor.db'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            return False
    
    return True

def launch_enhanced():
    """تشغيل النسخة المحسنة"""
    print("\n🚀 تشغيل النسخة المحسنة...")
    
    try:
        process = subprocess.Popen([
            sys.executable, 
            'phone_doctor_enhanced.py'
        ])
        
        print("✨ تم تشغيل النسخة المحسنة بنجاح!")
        print("💎 ابحث عن نموذج تسجيل الدخول المحسن على شاشتك")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في تشغيل النسخة المحسنة: {e}")
        return False

def show_enhanced_info():
    """عرض معلومات النسخة المحسنة"""
    print("\n" + "🌟" * 50)
    print("معلومات تسجيل الدخول المحسن:")
    print("🌟" * 50)
    print("👑 المدير:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("   الصلاحيات: كاملة بدون قيود")
    print()
    print("💼 موظف المبيعات:")
    print("   اسم المستخدم: sales")
    print("   كلمة المرور: sales123")
    print("   الصلاحيات: محدودة (تحتاج ترخيص)")
    print("🌟" * 50)

def show_enhanced_features():
    """عرض الميزات المحسنة"""
    print("\nالميزات المحسنة في النسخة الجديدة:")
    print()
    print("✨ نموذج تسجيل الدخول المحسن:")
    print("   💎 واجهة احترافية عصرية")
    print("   🎨 ألوان متناسقة ومريحة للعين")
    print("   📱 أيقونات جميلة ومعبرة")
    print("   ⚡ تأثيرات focus للحقول")
    print("   🔄 تأثيرات تحميل متطورة")
    print("   📍 توسيط تلقائي للنوافذ")
    print()
    print("📊 البيانات الحقيقية المتاحة:")
    print("   🔧 50 أمر صيانة مع تفاصيل كاملة")
    print("   👥 20 عميل مع حسابات مالية")
    print("   💳 25 شيك بحالات مختلفة")
    print("   💰 إحصائيات مالية حية")
    print()
    print("🎯 الصفحات المتاحة:")
    print("   🏠 لوحة التحكم مع إحصائيات حية")
    print("   🔧 إدارة الصيانة مع جدول البيانات")
    print("   👥 إدارة العملاء مع الحسابات المالية")
    print("   💳 إدارة الشيكات مع الإحصائيات")
    print()
    print("🎉 التجربة المحسنة:")
    print("   🎭 تأثيرات بصرية جميلة")
    print("   💫 انتقالات سلسة")
    print("   🎪 تفاعل مريح وسهل")
    print("   🏆 أداء سريع ومستقر")
    print("   📱 جداول تفاعلية مع التمرير")

def main():
    """الدالة الرئيسية المحسنة"""
    print_enhanced_header()
    
    # فحص المتطلبات
    if not check_enhanced_requirements():
        print("\n❌ فشل فحص المتطلبات المحسنة")
        input("اضغط Enter للخروج...")
        return
    
    # فحص الملفات
    if not check_enhanced_files():
        print("\n❌ الملفات المحسنة مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع الفحوصات المحسنة نجحت!")
    
    # تشغيل النسخة المحسنة
    if launch_enhanced():
        show_enhanced_info()
        show_enhanced_features()
        print("\n🎉 تم تشغيل Phone Doctor Enhanced Edition بنجاح!")
        print("✨ استمتع بنموذج تسجيل الدخول المحسن!")
        print("💎 جرب الأزرار السريعة والتأثيرات التفاعلية!")
    else:
        print("\n❌ فشل في تشغيل النسخة المحسنة")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
