#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للعملاء - Phone Doctor v2.0
Add Sample Customers Data

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import random
from datetime import datetime, timedelta

def add_sample_customers():
    """إضافة عملاء تجريبيين مع معاملات مالية"""
    
    print("👥 إضافة بيانات تجريبية للعملاء...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # إنشاء الجداول إذا لم تكن موجودة
        customers_table = '''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT NOT NULL UNIQUE,
                email TEXT,
                address TEXT,
                customer_type TEXT DEFAULT 'عميل عادي',
                total_purchases REAL DEFAULT 0,
                total_paid REAL DEFAULT 0,
                total_debt REAL DEFAULT 0,
                net_profit REAL DEFAULT 0,
                last_purchase_date TIMESTAMP,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                is_vip BOOLEAN DEFAULT 0,
                credit_limit REAL DEFAULT 0,
                created_by TEXT,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        '''
        
        transactions_table = '''
            CREATE TABLE IF NOT EXISTS customer_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reference_id INTEGER,
                reference_type TEXT,
                created_by TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        '''
        
        cursor.execute(customers_table)
        cursor.execute(transactions_table)
        
        # حذف البيانات الموجودة
        cursor.execute("DELETE FROM customer_transactions")
        cursor.execute("DELETE FROM customers")
        
        # بيانات العملاء التجريبية
        customers_data = [
            # عملاء عاديين
            ("أحمد محمد علي", "0591234567", "<EMAIL>", "غزة - الرمال", "عميل عادي", 0),
            ("فاطمة حسن محمود", "0592345678", "<EMAIL>", "غزة - الشجاعية", "عميل عادي", 0),
            ("محمد عبد الله سالم", "0593456789", "<EMAIL>", "غزة - النصر", "عميل عادي", 0),
            ("نور الدين أحمد", "0594567890", "<EMAIL>", "غزة - الزيتون", "عميل عادي", 0),
            ("سارة محمد يوسف", "0595678901", "<EMAIL>", "غزة - الدرج", "عميل عادي", 0),
            
            # عملاء VIP
            ("عبد الرحمن الشوامرة", "0596789012", "<EMAIL>", "غزة - الرمال الشمالي", "عميل VIP", 1),
            ("ليلى أحمد الخالدي", "0597890123", "<EMAIL>", "غزة - تل الهوا", "عميل VIP", 1),
            ("يوسف محمد النجار", "0598901234", "<EMAIL>", "غزة - الصبرة", "عميل VIP", 1),
            
            # عملاء تجاريين
            ("شركة الاتصالات المتقدمة", "0599012345", "<EMAIL>", "غزة - الشارع العام", "عميل تجاري", 0),
            ("مؤسسة التكنولوجيا الحديثة", "0590123456", "<EMAIL>", "غزة - الكرامة", "عميل مؤسسي", 0),
            
            # عملاء إضافيين
            ("خالد عبد الله زايد", "0591357924", "<EMAIL>", "غزة - البلد القديم", "عميل عادي", 0),
            ("منى سعيد الأغا", "0592468135", "<EMAIL>", "غزة - الصفطاوي", "عميل عادي", 0),
            ("عمر حسام الدين", "0593579246", "<EMAIL>", "غزة - الشيخ عجلين", "عميل عادي", 0),
            ("رنا محمود عاشور", "0594680357", "<EMAIL>", "غزة - جباليا", "عميل عادي", 0),
            ("طارق فايز النحال", "0595791468", "<EMAIL>", "غزة - بيت حانون", "عميل عادي", 0),
            
            # عملاء مع ديون
            ("سمير عبد الفتاح", "0596802579", "<EMAIL>", "غزة - خان يونس", "عميل عادي", 0),
            ("هدى محمد الكرد", "0597913680", "<EMAIL>", "غزة - رفح", "عميل عادي", 0),
            ("باسم أحمد قديح", "0598024791", "<EMAIL>", "غزة - دير البلح", "عميل عادي", 0),
            ("ريم سالم الهور", "0599135802", "<EMAIL>", "غزة - المغازي", "عميل عادي", 0),
            ("نادر محمد شراب", "0590246913", "<EMAIL>", "غزة - النصيرات", "عميل عادي", 0),
        ]
        
        print(f"📝 إضافة {len(customers_data)} عميل...")
        
        # إضافة العملاء
        customer_ids = []
        for i, (name, phone, email, address, customer_type, is_vip) in enumerate(customers_data):
            # تاريخ تسجيل عشوائي في آخر 6 أشهر
            days_ago = random.randint(1, 180)
            registration_date = datetime.now() - timedelta(days=days_ago)
            
            cursor.execute('''
                INSERT INTO customers (name, phone, email, address, customer_type, is_vip, registration_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, phone, email, address, customer_type, is_vip, registration_date, 'admin'))
            
            customer_ids.append(cursor.lastrowid)
        
        print("💰 إضافة المعاملات المالية...")
        
        # إضافة معاملات مالية تجريبية
        transaction_types = ['purchase', 'payment']
        descriptions = [
            'إصلاح شاشة iPhone',
            'استبدال بطارية Samsung',
            'إصلاح مشكلة شحن',
            'تغيير زجاج خلفي',
            'إصلاح مشكلة صوت',
            'استبدال كاميرا',
            'إصلاح مشكلة واي فاي',
            'تحديث نظام التشغيل',
            'إصلاح أزرار',
            'تنظيف داخلي للجهاز'
        ]
        
        for customer_id in customer_ids:
            # عدد المعاملات العشوائي لكل عميل (1-8)
            num_transactions = random.randint(1, 8)
            
            total_purchases = 0
            total_payments = 0
            
            for _ in range(num_transactions):
                # نوع المعاملة
                transaction_type = random.choice(transaction_types)
                
                # مبلغ المعاملة
                if transaction_type == 'purchase':
                    amount = random.uniform(50, 800)  # مشتريات بين 50-800 شيكل
                    total_purchases += amount
                else:  # payment
                    amount = random.uniform(30, 600)  # مدفوعات بين 30-600 شيكل
                    total_payments += amount
                
                # وصف المعاملة
                description = random.choice(descriptions)
                
                # تاريخ المعاملة
                days_ago = random.randint(1, 150)
                transaction_date = datetime.now() - timedelta(days=days_ago)
                
                cursor.execute('''
                    INSERT INTO customer_transactions 
                    (customer_id, transaction_type, amount, description, transaction_date, created_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (customer_id, transaction_type, amount, description, transaction_date, 'admin'))
            
            # تحديث إجماليات العميل
            total_debt = max(0, total_purchases - total_payments)
            net_profit = total_payments * 0.3  # هامش ربح 30%
            
            cursor.execute('''
                UPDATE customers 
                SET total_purchases = ?, total_paid = ?, total_debt = ?, net_profit = ?,
                    last_purchase_date = ?, updated_date = ?
                WHERE id = ?
            ''', (total_purchases, total_payments, total_debt, net_profit, 
                  datetime.now(), datetime.now(), customer_id))
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        print(f"📊 تم إضافة {len(customers_data)} عميل مع معاملات مالية متنوعة")
        
        # عرض إحصائيات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM customer_transactions")
        transactions_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(total_debt) FROM customers WHERE total_debt > 0")
        total_debt = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(total_paid) FROM customers")
        total_paid = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(net_profit) FROM customers")
        total_profit = cursor.fetchone()[0] or 0
        
        conn.close()
        
        print("\n📈 إحصائيات البيانات:")
        print(f"  👥 عدد العملاء: {customers_count}")
        print(f"  💳 عدد المعاملات: {transactions_count}")
        print(f"  💰 إجمالي الديون: {total_debt:.2f} ₪")
        print(f"  💵 إجمالي المدفوع: {total_paid:.2f} ₪")
        print(f"  ⭐ صافي الربح: {total_profit:.2f} ₪")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_sample_customers()
