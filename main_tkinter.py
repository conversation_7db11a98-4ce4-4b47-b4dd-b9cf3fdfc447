#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة محلات صيانة الهواتف - نسخة Tkinter
Phone Doctor - Professional Phone Repair Shop Management System

المطور: محمد الشوامرة
الهاتف: 0566000140
جميع الحقوق محفوظة © 2024
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os
from datetime import datetime
import threading
import time

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from utils.config import Config
from ui.repair_dialogs import NewRepairDialog, UpdateRepairStatusDialog
from ui.inventory_dialogs import NewInventoryItemDialog, InventoryManagementWindow
from ui.supplier_dialogs import NewSupplierDialog, SuppliersManagementWindow

class PhoneDoctorApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Phone Doctor - نظام إدارة محلات صيانة الهواتف")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # تهيئة قاعدة البيانات والإعدادات
        self.db_manager = DatabaseManager()
        self.config = Config()
        
        # تهيئة قاعدة البيانات
        if not self.db_manager.initialize_database():
            messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
            return
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_styles()
        self.load_dashboard_data()
        
        # بدء مؤقت تحديث الوقت
        self.update_time()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="📱 Phone Doctor", 
                               font=("Arial", 24, "bold"))
        title_label.pack(side=tk.LEFT)
        
        self.time_label = ttk.Label(title_frame, text="", 
                                   font=("Arial", 12))
        self.time_label.pack(side=tk.RIGHT)
        
        # دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء التبويبات
        self.create_dashboard_tab()
        self.create_repairs_tab()
        self.create_inventory_tab()
        self.create_suppliers_tab()
        self.create_sales_tab()
        self.create_financial_tab()
        self.create_reports_tab()
        self.create_settings_tab()
        
        # شريط الحالة
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, 
                                     text="جميع الحقوق محفوظة لـ محمد الشوامرة - 📞 0566000140",
                                     font=("Arial", 9))
        self.status_label.pack(side=tk.LEFT)
        
        db_status = ttk.Label(status_frame, text="قاعدة البيانات: متصلة ✅", 
                             font=("Arial", 9))
        db_status.pack(side=tk.RIGHT)
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="🏠 لوحة التحكم")
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(dashboard_frame, text="الإحصائيات السريعة", padding=20)
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # إنشاء بطاقات الإحصائيات
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # المبيعات اليومية
        sales_frame = ttk.LabelFrame(stats_grid, text="💰 المبيعات اليوم", padding=10)
        sales_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        self.daily_sales_label = ttk.Label(sales_frame, text="0.00 ₪", 
                                          font=("Arial", 16, "bold"))
        self.daily_sales_label.pack()
        
        # الصيانات الجارية
        repairs_frame = ttk.LabelFrame(stats_grid, text="🔧 صيانات جارية", padding=10)
        repairs_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.active_repairs_label = ttk.Label(repairs_frame, text="0", 
                                             font=("Arial", 16, "bold"))
        self.active_repairs_label.pack()
        
        # تنبيهات المخزون
        inventory_frame = ttk.LabelFrame(stats_grid, text="📦 تنبيهات المخزون", padding=10)
        inventory_frame.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        self.low_stock_label = ttk.Label(inventory_frame, text="0", 
                                        font=("Arial", 16, "bold"))
        self.low_stock_label.pack()
        
        # الشيكات المستحقة
        checks_frame = ttk.LabelFrame(stats_grid, text="💳 شيكات مستحقة", padding=10)
        checks_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        self.due_checks_label = ttk.Label(checks_frame, text="0", 
                                         font=("Arial", 16, "bold"))
        self.due_checks_label.pack()
        
        # العملاء الجدد
        customers_frame = ttk.LabelFrame(stats_grid, text="👥 عملاء جدد", padding=10)
        customers_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        self.new_customers_label = ttk.Label(customers_frame, text="0", 
                                            font=("Arial", 16, "bold"))
        self.new_customers_label.pack()
        
        # الأرباح الشهرية
        profit_frame = ttk.LabelFrame(stats_grid, text="📈 الأرباح الشهرية", padding=10)
        profit_frame.grid(row=1, column=2, padx=5, pady=5, sticky="ew")
        self.monthly_profit_label = ttk.Label(profit_frame, text="0.00 ₪", 
                                             font=("Arial", 16, "bold"))
        self.monthly_profit_label.pack()
        
        # تكوين الشبكة
        for i in range(3):
            stats_grid.columnconfigure(i, weight=1)
        
        # الأعمال السريعة
        actions_frame = ttk.LabelFrame(dashboard_frame, text="الأعمال السريعة", padding=20)
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        actions_grid = ttk.Frame(actions_frame)
        actions_grid.pack()
        
        # أزرار الأعمال السريعة
        ttk.Button(actions_grid, text="🔧 إضافة جهاز للصيانة", 
                  command=self.new_repair).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(actions_grid, text="💰 بيع قطعة", 
                  command=self.new_sale).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(actions_grid, text="📦 إضافة قطعة للمخزون", 
                  command=self.new_inventory_item).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(actions_grid, text="🏪 إضافة مورد", 
                  command=self.new_supplier).grid(row=1, column=0, padx=5, pady=5)
        ttk.Button(actions_grid, text="📊 عرض التقارير", 
                  command=lambda: self.notebook.select(6)).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(actions_grid, text="💾 نسخ احتياطي", 
                  command=self.backup_database).grid(row=1, column=2, padx=5, pady=5)
    
    def create_repairs_tab(self):
        """إنشاء تبويب الصيانة"""
        repairs_frame = ttk.Frame(self.notebook)
        self.notebook.add(repairs_frame, text="🔧 الصيانة")
        
        # أدوات التحكم
        controls_frame = ttk.Frame(repairs_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(controls_frame, text="إضافة جهاز جديد", 
                  command=self.new_repair).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تحديث الحالة", 
                  command=self.update_repair_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تحديث", 
                  command=self.refresh_repairs).pack(side=tk.RIGHT, padx=5)
        
        # جدول الصيانات
        columns = ("ID", "العميل", "نوع الجهاز", "المشكلة", "الحالة", "التاريخ")
        self.repairs_tree = ttk.Treeview(repairs_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.repairs_tree.heading(col, text=col)
            self.repairs_tree.column(col, width=150)
        
        # شريط التمرير
        repairs_scrollbar = ttk.Scrollbar(repairs_frame, orient=tk.VERTICAL, command=self.repairs_tree.yview)
        self.repairs_tree.configure(yscrollcommand=repairs_scrollbar.set)
        
        self.repairs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        repairs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
    
    def create_inventory_tab(self):
        """إنشاء تبويب المخزون"""
        inventory_frame = ttk.Frame(self.notebook)
        self.notebook.add(inventory_frame, text="📦 المخزون")

        # أدوات التحكم
        controls_frame = ttk.Frame(inventory_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(controls_frame, text="إضافة قطعة جديدة",
                  command=self.new_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="إدارة المخزون",
                  command=self.open_inventory_management).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تحديث",
                  command=self.refresh_inventory_summary).pack(side=tk.RIGHT, padx=5)

        # ملخص المخزون
        summary_frame = ttk.LabelFrame(inventory_frame, text="ملخص المخزون", padding=15)
        summary_frame.pack(fill=tk.X, padx=10, pady=10)

        # إحصائيات سريعة
        stats_frame = ttk.Frame(summary_frame)
        stats_frame.pack(fill=tk.X)

        # إجمالي القطع
        total_frame = ttk.LabelFrame(stats_frame, text="إجمالي القطع", padding=10)
        total_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        self.total_items_label = ttk.Label(total_frame, text="0", font=("Arial", 14, "bold"))
        self.total_items_label.pack()

        # القطع المنخفضة
        low_stock_frame = ttk.LabelFrame(stats_frame, text="مخزون منخفض", padding=10)
        low_stock_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.low_stock_items_label = ttk.Label(low_stock_frame, text="0", font=("Arial", 14, "bold"), foreground="red")
        self.low_stock_items_label.pack()

        # قيمة المخزون
        value_frame = ttk.LabelFrame(stats_frame, text="قيمة المخزون", padding=10)
        value_frame.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        self.inventory_value_label = ttk.Label(value_frame, text="0.00 ₪", font=("Arial", 14, "bold"))
        self.inventory_value_label.pack()

        # تكوين الشبكة
        for i in range(3):
            stats_frame.columnconfigure(i, weight=1)

        # قائمة القطع المنخفضة المخزون
        low_stock_list_frame = ttk.LabelFrame(inventory_frame, text="تنبيهات المخزون المنخفض", padding=10)
        low_stock_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # جدول التنبيهات
        columns = ("اسم القطعة", "الكمية الحالية", "الحد الأدنى", "المطلوب")
        self.low_stock_tree = ttk.Treeview(low_stock_list_frame, columns=columns, show="headings", height=10)

        for col in columns:
            self.low_stock_tree.heading(col, text=col)
            self.low_stock_tree.column(col, width=150)

        # شريط التمرير للتنبيهات
        low_stock_scrollbar = ttk.Scrollbar(low_stock_list_frame, orient=tk.VERTICAL, command=self.low_stock_tree.yview)
        self.low_stock_tree.configure(yscrollcommand=low_stock_scrollbar.set)

        self.low_stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        low_stock_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل البيانات
        self.refresh_inventory_summary()
    
    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين"""
        suppliers_frame = ttk.Frame(self.notebook)
        self.notebook.add(suppliers_frame, text="🏪 الموردين")

        # أدوات التحكم
        controls_frame = ttk.Frame(suppliers_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(controls_frame, text="إضافة مورد جديد",
                  command=self.new_supplier).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="إدارة الموردين",
                  command=self.open_suppliers_management).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تحديث",
                  command=self.refresh_suppliers_summary).pack(side=tk.RIGHT, padx=5)

        # ملخص الموردين
        summary_frame = ttk.LabelFrame(suppliers_frame, text="ملخص الموردين", padding=15)
        summary_frame.pack(fill=tk.X, padx=10, pady=10)

        # إحصائيات سريعة
        stats_frame = ttk.Frame(summary_frame)
        stats_frame.pack(fill=tk.X)

        # إجمالي الموردين
        total_suppliers_frame = ttk.LabelFrame(stats_frame, text="إجمالي الموردين", padding=10)
        total_suppliers_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        self.total_suppliers_label = ttk.Label(total_suppliers_frame, text="0", font=("Arial", 14, "bold"))
        self.total_suppliers_label.pack()

        # إجمالي الأرصدة
        total_balance_frame = ttk.LabelFrame(stats_frame, text="إجمالي الأرصدة", padding=10)
        total_balance_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.total_balance_label = ttk.Label(total_balance_frame, text="0.00 ₪", font=("Arial", 14, "bold"))
        self.total_balance_label.pack()

        # تكوين الشبكة
        for i in range(2):
            stats_frame.columnconfigure(i, weight=1)

        # قائمة الموردين
        suppliers_list_frame = ttk.LabelFrame(suppliers_frame, text="قائمة الموردين", padding=10)
        suppliers_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # جدول الموردين
        columns = ("اسم المورد", "الشركة", "الهاتف", "الرصيد")
        self.suppliers_tree = ttk.Treeview(suppliers_list_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            if col == "اسم المورد":
                self.suppliers_tree.column(col, width=200)
            elif col == "الشركة":
                self.suppliers_tree.column(col, width=200)
            elif col == "الرصيد":
                self.suppliers_tree.column(col, width=100)
            else:
                self.suppliers_tree.column(col, width=150)

        # شريط التمرير للموردين
        suppliers_scrollbar = ttk.Scrollbar(suppliers_list_frame, orient=tk.VERTICAL, command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=suppliers_scrollbar.set)

        self.suppliers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        suppliers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل البيانات
        self.refresh_suppliers_summary()
    
    def create_sales_tab(self):
        """إنشاء تبويب المبيعات"""
        sales_frame = ttk.Frame(self.notebook)
        self.notebook.add(sales_frame, text="💰 المبيعات")
        
        ttk.Label(sales_frame, text="نافذة المبيعات - قيد التطوير", 
                 font=("Arial", 16)).pack(expand=True)
    
    def create_financial_tab(self):
        """إنشاء تبويب الشؤون المالية"""
        financial_frame = ttk.Frame(self.notebook)
        self.notebook.add(financial_frame, text="💳 الشؤون المالية")
        
        ttk.Label(financial_frame, text="النوافذ المالية - قيد التطوير", 
                 font=("Arial", 16)).pack(expand=True)
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="📊 التقارير")
        
        ttk.Label(reports_frame, text="نافذة التقارير - قيد التطوير", 
                 font=("Arial", 16)).pack(expand=True)
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ الإعدادات")
        
        # إعدادات المحل
        shop_frame = ttk.LabelFrame(settings_frame, text="إعدادات المحل", padding=20)
        shop_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(shop_frame, text="اسم المحل:").grid(row=0, column=0, sticky="w", pady=5)
        self.shop_name_var = tk.StringVar(value=self.config.get('shop.name', ''))
        shop_name_entry = ttk.Entry(shop_frame, textvariable=self.shop_name_var, width=40)
        shop_name_entry.grid(row=0, column=1, padx=10, pady=5)
        
        ttk.Button(shop_frame, text="حفظ", 
                  command=self.save_shop_settings).grid(row=0, column=2, padx=10, pady=5)
        
        # إعدادات قاعدة البيانات
        db_frame = ttk.LabelFrame(settings_frame, text="إعدادات قاعدة البيانات", padding=20)
        db_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(db_frame, text="إنشاء نسخة احتياطية", 
                  command=self.backup_database).pack(side=tk.LEFT, padx=5)
        ttk.Button(db_frame, text="استعادة نسخة احتياطية", 
                  command=self.restore_database).pack(side=tk.LEFT, padx=5)
    
    def setup_styles(self):
        """إعداد الأنماط"""
        style = ttk.Style()
        
        # تطبيق ثيم حديث
        available_themes = style.theme_names()
        if 'clam' in available_themes:
            style.theme_use('clam')
        elif 'alt' in available_themes:
            style.theme_use('alt')
    
    def update_time(self):
        """تحديث الوقت"""
        now = datetime.now()
        time_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=time_str)
        
        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            if not self.db_manager.connect():
                return
            
            # المبيعات اليومية
            daily_sales = self.get_daily_sales()
            self.daily_sales_label.config(text=f"{daily_sales:.2f} ₪")
            
            # الصيانات الجارية
            active_repairs = self.get_active_repairs()
            self.active_repairs_label.config(text=str(active_repairs))
            
            # تنبيهات المخزون
            low_stock = self.get_low_stock_count()
            self.low_stock_label.config(text=str(low_stock))
            
            # الشيكات المستحقة
            due_checks = self.get_due_checks()
            self.due_checks_label.config(text=str(due_checks))
            
            # العملاء الجدد
            new_customers = self.get_new_customers()
            self.new_customers_label.config(text=str(new_customers))
            
            # الأرباح الشهرية
            monthly_profit = self.get_monthly_profit()
            self.monthly_profit_label.config(text=f"{monthly_profit:.2f} ₪")
            
            # تحديث جدول الصيانات
            self.refresh_repairs()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def get_daily_sales(self):
        """الحصول على المبيعات اليومية"""
        query = "SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE DATE(sale_date) = DATE('now')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_active_repairs(self):
        """الحصول على عدد الصيانات الجارية"""
        query = "SELECT COUNT(*) FROM repairs WHERE repair_status IN ('قيد الانتظار', 'جاري الإصلاح')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_low_stock_count(self):
        """الحصول على عدد القطع منخفضة المخزون"""
        query = "SELECT COUNT(*) FROM inventory WHERE quantity <= min_quantity"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_due_checks(self):
        """الحصول على عدد الشيكات المستحقة"""
        query = "SELECT COUNT(*) FROM checks WHERE due_date <= DATE('now') AND status = 'معلق'"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_new_customers(self):
        """الحصول على عدد العملاء الجدد هذا الشهر"""
        query = "SELECT COUNT(*) FROM customers WHERE DATE(created_at) >= DATE('now', 'start of month')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_monthly_profit(self):
        """الحصول على الأرباح الشهرية"""
        query = "SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE DATE(sale_date) >= DATE('now', 'start of month')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def refresh_repairs(self):
        """تحديث جدول الصيانات"""
        # مسح البيانات الحالية
        for item in self.repairs_tree.get_children():
            self.repairs_tree.delete(item)
        
        # جلب البيانات الجديدة
        query = """
        SELECT r.id, c.name, r.device_type, r.problem_description, 
               r.repair_status, r.date_received
        FROM repairs r
        LEFT JOIN customers c ON r.customer_id = c.id
        ORDER BY r.date_received DESC
        """
        
        repairs = self.db_manager.fetch_query(query)
        for repair in repairs:
            self.repairs_tree.insert("", "end", values=(
                repair['id'],
                repair['name'] or 'غير محدد',
                repair['device_type'],
                repair['problem_description'][:50] + '...' if len(repair['problem_description']) > 50 else repair['problem_description'],
                repair['repair_status'],
                repair['date_received']
            ))
    
    def new_repair(self):
        """إضافة جهاز جديد للصيانة"""
        dialog = NewRepairDialog(self.root, self.db_manager)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
    
    def new_sale(self):
        """إضافة بيع جديد"""
        messagebox.showinfo("قيد التطوير", "نافذة البيع الجديد قيد التطوير")
    
    def new_inventory_item(self):
        """إضافة قطعة جديدة للمخزون"""
        dialog = NewInventoryItemDialog(self.root, self.db_manager)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
            self.refresh_inventory_summary()  # تحديث ملخص المخزون
    
    def new_supplier(self):
        """إضافة مورد جديد"""
        dialog = NewSupplierDialog(self.root, self.db_manager)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
            self.refresh_suppliers_summary()  # تحديث ملخص الموردين
    
    def update_repair_status(self):
        """تحديث حالة الصيانة"""
        selected_item = self.repairs_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صيانة من القائمة!")
            return

        # الحصول على معرف الصيانة
        repair_id = self.repairs_tree.item(selected_item[0])['values'][0]

        dialog = UpdateRepairStatusDialog(self.root, self.db_manager, repair_id)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
    
    def save_shop_settings(self):
        """حفظ إعدادات المحل"""
        shop_name = self.shop_name_var.get().strip()
        if shop_name:
            self.config.set('shop.name', shop_name)
            messagebox.showinfo("تم الحفظ", "تم حفظ إعدادات المحل بنجاح!")
        else:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المحل!")
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ النسخة الاحتياطية",
            defaultextension=".db",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.db_manager.backup_database(file_path):
                messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية!")
    
    def restore_database(self):
        """استعادة نسخة احتياطية"""
        file_path = filedialog.askopenfilename(
            title="اختيار النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if file_path:
            result = messagebox.askyesno(
                "تأكيد", 
                "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية!"
            )
            
            if result:
                if self.db_manager.restore_database(file_path):
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح!")
                    self.load_dashboard_data()
                else:
                    messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية!")
    
    def run(self):
        """تشغيل التطبيق"""
        # إعداد إغلاق التطبيق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # تشغيل الحلقة الرئيسية
        self.root.mainloop()
    
    def open_inventory_management(self):
        """فتح نافذة إدارة المخزون"""
        inventory_window = InventoryManagementWindow(self.root, self.db_manager)

    def refresh_inventory_summary(self):
        """تحديث ملخص المخزون"""
        try:
            # إجمالي القطع
            total_query = "SELECT COUNT(*) as count FROM inventory"
            total_result = self.db_manager.fetch_query(total_query)
            total_items = total_result[0]['count'] if total_result else 0
            self.total_items_label.config(text=str(total_items))

            # القطع المنخفضة المخزون
            low_stock_query = "SELECT COUNT(*) as count FROM inventory WHERE quantity <= min_quantity"
            low_stock_result = self.db_manager.fetch_query(low_stock_query)
            low_stock_count = low_stock_result[0]['count'] if low_stock_result else 0
            self.low_stock_items_label.config(text=str(low_stock_count))

            # قيمة المخزون
            value_query = "SELECT SUM(quantity * purchase_price) as total_value FROM inventory"
            value_result = self.db_manager.fetch_query(value_query)
            total_value = value_result[0]['total_value'] if value_result and value_result[0]['total_value'] else 0
            self.inventory_value_label.config(text=f"{total_value:.2f} ₪")

            # تحديث قائمة التنبيهات
            self.refresh_low_stock_alerts()

        except Exception as e:
            print(f"خطأ في تحديث ملخص المخزون: {e}")

    def refresh_low_stock_alerts(self):
        """تحديث تنبيهات المخزون المنخفض"""
        # مسح البيانات الحالية
        for item in self.low_stock_tree.get_children():
            self.low_stock_tree.delete(item)

        try:
            # جلب القطع المنخفضة المخزون
            query = """
            SELECT item_name, quantity, min_quantity, (min_quantity - quantity + 5) as needed
            FROM inventory
            WHERE quantity <= min_quantity
            ORDER BY (quantity - min_quantity) ASC
            """

            low_stock_items = self.db_manager.fetch_query(query)
            for item in low_stock_items:
                needed = max(0, item['needed'])
                self.low_stock_tree.insert("", "end", values=(
                    item['item_name'],
                    item['quantity'],
                    item['min_quantity'],
                    needed
                ))

        except Exception as e:
            print(f"خطأ في تحديث تنبيهات المخزون: {e}")

    def open_suppliers_management(self):
        """فتح نافذة إدارة الموردين"""
        suppliers_window = SuppliersManagementWindow(self.root, self.db_manager)

    def refresh_suppliers_summary(self):
        """تحديث ملخص الموردين"""
        try:
            # إجمالي الموردين
            total_query = "SELECT COUNT(*) as count FROM suppliers"
            total_result = self.db_manager.fetch_query(total_query)
            total_suppliers = total_result[0]['count'] if total_result else 0
            self.total_suppliers_label.config(text=str(total_suppliers))

            # إجمالي الأرصدة
            balance_query = "SELECT SUM(balance) as total_balance FROM suppliers"
            balance_result = self.db_manager.fetch_query(balance_query)
            total_balance = balance_result[0]['total_balance'] if balance_result and balance_result[0]['total_balance'] else 0
            self.total_balance_label.config(text=f"{total_balance:.2f} ₪")

            # تحديث قائمة الموردين
            self.refresh_suppliers_list()

        except Exception as e:
            print(f"خطأ في تحديث ملخص الموردين: {e}")

    def refresh_suppliers_list(self):
        """تحديث قائمة الموردين"""
        # مسح البيانات الحالية
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)

        try:
            # جلب الموردين
            query = """
            SELECT name, company, phone, balance
            FROM suppliers
            ORDER BY name
            """

            suppliers = self.db_manager.fetch_query(query)
            for supplier in suppliers:
                self.suppliers_tree.insert("", "end", values=(
                    supplier['name'],
                    supplier['company'] or '',
                    supplier['phone'] or '',
                    f"{supplier['balance']:.2f}"
                ))

        except Exception as e:
            print(f"خطأ في تحديث قائمة الموردين: {e}")

    def on_closing(self):
        """معالجة إغلاق التطبيق"""
        if messagebox.askokcancel("خروج", "هل تريد إغلاق البرنامج؟"):
            self.db_manager.disconnect()
            self.root.destroy()

def main():
    """الدالة الرئيسية"""
    try:
        app = PhoneDoctorApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")

if __name__ == "__main__":
    main()
