#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ حوار الموردين - Supplier Dialogs
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class NewSupplierDialog:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة مورد جديد")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إضافة مورد جديد", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إطار بيانات المورد
        supplier_frame = ttk.LabelFrame(main_frame, text="بيانات المورد", padding=15)
        supplier_frame.pack(fill=tk.X, pady=(0, 15))
        
        # اسم المورد
        ttk.Label(supplier_frame, text="اسم المورد:").grid(row=0, column=0, sticky="w", pady=5)
        self.supplier_name_var = tk.StringVar()
        ttk.Entry(supplier_frame, textvariable=self.supplier_name_var, width=40).grid(row=0, column=1, sticky="ew", pady=5)
        
        # اسم الشركة
        ttk.Label(supplier_frame, text="اسم الشركة:").grid(row=1, column=0, sticky="w", pady=5)
        self.company_name_var = tk.StringVar()
        ttk.Entry(supplier_frame, textvariable=self.company_name_var, width=40).grid(row=1, column=1, sticky="ew", pady=5)
        
        # رقم الهاتف
        ttk.Label(supplier_frame, text="رقم الهاتف:").grid(row=2, column=0, sticky="w", pady=5)
        self.phone_var = tk.StringVar()
        ttk.Entry(supplier_frame, textvariable=self.phone_var, width=40).grid(row=2, column=1, sticky="ew", pady=5)
        
        # البريد الإلكتروني
        ttk.Label(supplier_frame, text="البريد الإلكتروني:").grid(row=3, column=0, sticky="w", pady=5)
        self.email_var = tk.StringVar()
        ttk.Entry(supplier_frame, textvariable=self.email_var, width=40).grid(row=3, column=1, sticky="ew", pady=5)
        
        # العنوان
        ttk.Label(supplier_frame, text="العنوان:").grid(row=4, column=0, sticky="nw", pady=5)
        self.address_text = tk.Text(supplier_frame, width=40, height=3)
        self.address_text.grid(row=4, column=1, sticky="ew", pady=5)
        
        # الرصيد الابتدائي
        ttk.Label(supplier_frame, text="الرصيد الابتدائي (₪):").grid(row=5, column=0, sticky="w", pady=5)
        self.balance_var = tk.StringVar(value="0.00")
        ttk.Entry(supplier_frame, textvariable=self.balance_var, width=40).grid(row=5, column=1, sticky="ew", pady=5)
        
        # تكوين الأعمدة
        supplier_frame.columnconfigure(1, weight=1)
        
        # ملاحظات
        notes_frame = ttk.LabelFrame(main_frame, text="ملاحظات", padding=15)
        notes_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.notes_text = tk.Text(notes_frame, width=50, height=4)
        self.notes_text.pack(fill=tk.X)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="حفظ", 
                  command=self.save).pack(side=tk.RIGHT)
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.supplier_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المورد!")
            return False
        
        # التحقق من الرصيد
        try:
            float(self.balance_var.get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح!")
            return False
        
        return True
    
    def save(self):
        """حفظ البيانات"""
        if not self.validate_data():
            return
        
        try:
            # إضافة المورد
            supplier_query = """
            INSERT INTO suppliers (name, company, phone, email, address, balance, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            if self.db_manager.execute_query(supplier_query, (
                self.supplier_name_var.get().strip(),
                self.company_name_var.get().strip() or None,
                self.phone_var.get().strip() or None,
                self.email_var.get().strip() or None,
                self.address_text.get("1.0", tk.END).strip() or None,
                float(self.balance_var.get()),
                self.notes_text.get("1.0", tk.END).strip() or None
            )):
                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح!")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المورد!")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class SuppliersManagementWindow:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة الموردين")
        self.window.geometry("1000x600")
        self.window.transient(parent)
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.refresh_suppliers()
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان وأدوات التحكم
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="إدارة الموردين", 
                               font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # أزرار التحكم
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side=tk.RIGHT)
        
        ttk.Button(controls_frame, text="إضافة مورد", 
                  command=self.add_supplier).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تعديل", 
                  command=self.edit_supplier).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="حذف", 
                  command=self.delete_supplier).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تحديث", 
                  command=self.refresh_suppliers).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = ttk.LabelFrame(main_frame, text="البحث", padding=10)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # جدول الموردين
        columns = ("ID", "اسم المورد", "الشركة", "الهاتف", "البريد الإلكتروني", 
                  "الرصيد", "تاريخ الإضافة")
        
        self.suppliers_tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            if col == "اسم المورد":
                self.suppliers_tree.column(col, width=150)
            elif col == "الشركة":
                self.suppliers_tree.column(col, width=150)
            elif col == "البريد الإلكتروني":
                self.suppliers_tree.column(col, width=200)
            elif col == "الرصيد":
                self.suppliers_tree.column(col, width=100)
            else:
                self.suppliers_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.suppliers_tree.yview)
        scrollbar_h = ttk.Scrollbar(main_frame, orient=tk.HORIZONTAL, command=self.suppliers_tree.xview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # إطار للجدول
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        self.suppliers_tree.grid(row=0, column=0, sticky="nsew", in_=table_frame)
        scrollbar_v.grid(row=0, column=1, sticky="ns", in_=table_frame)
        scrollbar_h.grid(row=1, column=0, sticky="ew", in_=table_frame)
        
        # ربط النقر المزدوج
        self.suppliers_tree.bind('<Double-1>', lambda e: self.edit_supplier())
    
    def refresh_suppliers(self):
        """تحديث قائمة الموردين"""
        # مسح البيانات الحالية
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)
        
        # جلب البيانات الجديدة
        query = """
        SELECT id, name, company, phone, email, balance, 
               DATE(created_at) as created_date
        FROM suppliers
        ORDER BY name
        """
        
        try:
            suppliers = self.db_manager.fetch_query(query)
            for supplier in suppliers:
                self.suppliers_tree.insert("", "end", values=(
                    supplier['id'],
                    supplier['name'],
                    supplier['company'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    f"{supplier['balance']:.2f}",
                    supplier['created_date']
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {e}")
    
    def on_search(self, event=None):
        """البحث في الموردين"""
        search_term = self.search_var.get().lower()
        
        # إخفاء جميع العناصر أولاً
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.detach(item)
        
        # إعادة إظهار العناصر المطابقة للبحث
        for item in self.suppliers_tree.get_children():
            values = self.suppliers_tree.item(item)['values']
            
            # البحث في اسم المورد والشركة
            if (search_term in values[1].lower() or 
                search_term in values[2].lower()):
                self.suppliers_tree.reattach(item, '', 'end')
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = NewSupplierDialog(self.window, self.db_manager)
        if dialog.show():
            self.refresh_suppliers()
    
    def edit_supplier(self):
        """تعديل مورد"""
        selected_item = self.suppliers_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد من القائمة!")
            return
        
        messagebox.showinfo("قيد التطوير", "نافذة تعديل المورد قيد التطوير")
    
    def delete_supplier(self):
        """حذف مورد"""
        selected_item = self.suppliers_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد من القائمة!")
            return
        
        # التحقق من وجود قطع مرتبطة بالمورد
        supplier_id = self.suppliers_tree.item(selected_item[0])['values'][0]
        
        try:
            check_query = "SELECT COUNT(*) as count FROM inventory WHERE supplier_id = ?"
            result = self.db_manager.fetch_query(check_query, (supplier_id,))
            linked_items = result[0]['count'] if result else 0
            
            if linked_items > 0:
                messagebox.showwarning("تحذير", 
                    f"لا يمكن حذف هذا المورد لأنه مرتبط بـ {linked_items} قطعة في المخزون!")
                return
            
            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المورد؟"):
                if self.db_manager.execute_query("DELETE FROM suppliers WHERE id = ?", (supplier_id,)):
                    messagebox.showinfo("نجح", "تم حذف المورد بنجاح!")
                    self.refresh_suppliers()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المورد!")
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def show(self):
        """عرض النافذة"""
        self.window.mainloop()
