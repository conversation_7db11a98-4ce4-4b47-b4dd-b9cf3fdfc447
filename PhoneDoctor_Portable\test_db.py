#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قاعدة البيانات
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from utils.config import Config

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔧 اختبار قاعدة البيانات...")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager("test_phone_doctor.db")
    
    # إنشاء قاعدة البيانات
    if db_manager.initialize_database():
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في إنشاء قاعدة البيانات!")
        return False
    
    # اختبار إدراج بيانات تجريبية
    print("\n📝 إدراج بيانات تجريبية...")
    
    # إضافة عميل تجريبي
    customer_query = """
    INSERT INTO customers (name, phone, address) 
    VALUES (?, ?, ?)
    """
    if db_manager.execute_query(customer_query, ("أحمد محمد", "0599123456", "رام الله")):
        print("✅ تم إضافة عميل تجريبي")
    
    # إضافة مورد تجريبي
    supplier_query = """
    INSERT INTO suppliers (name, company, phone) 
    VALUES (?, ?, ?)
    """
    if db_manager.execute_query(supplier_query, ("شركة قطع الغيار", "Mobile Parts Co.", "0598765432")):
        print("✅ تم إضافة مورد تجريبي")
    
    # إضافة قطعة للمخزون
    inventory_query = """
    INSERT INTO inventory (item_name, barcode, purchase_price, selling_price, quantity, supplier_id) 
    VALUES (?, ?, ?, ?, ?, ?)
    """
    if db_manager.execute_query(inventory_query, ("شاشة iPhone 12", "*********", 150.0, 200.0, 10, 1)):
        print("✅ تم إضافة قطعة للمخزون")
    
    # إضافة جهاز للصيانة
    repair_query = """
    INSERT INTO repairs (customer_id, device_type, device_model, problem_description, estimated_cost) 
    VALUES (?, ?, ?, ?, ?)
    """
    if db_manager.execute_query(repair_query, (1, "iPhone", "iPhone 12", "شاشة مكسورة", 200.0)):
        print("✅ تم إضافة جهاز للصيانة")
    
    # اختبار استعلامات البيانات
    print("\n📊 اختبار استعلامات البيانات...")
    
    # عرض العملاء
    customers = db_manager.fetch_query("SELECT * FROM customers")
    print(f"عدد العملاء: {len(customers)}")
    for customer in customers:
        print(f"  - {customer['name']} ({customer['phone']})")
    
    # عرض المخزون
    inventory = db_manager.fetch_query("SELECT * FROM inventory")
    print(f"عدد القطع في المخزون: {len(inventory)}")
    for item in inventory:
        print(f"  - {item['item_name']}: {item['quantity']} قطعة")
    
    # عرض الصيانات
    repairs = db_manager.fetch_query("""
        SELECT r.*, c.name as customer_name 
        FROM repairs r 
        JOIN customers c ON r.customer_id = c.id
    """)
    print(f"عدد الصيانات: {len(repairs)}")
    for repair in repairs:
        print(f"  - {repair['device_type']} للعميل {repair['customer_name']}: {repair['problem_description']}")
    
    # إغلاق الاتصال
    db_manager.disconnect()
    print("\n✅ اكتمل اختبار قاعدة البيانات بنجاح!")
    return True

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    config = Config("test_config.json")
    
    # اختبار قراءة الإعدادات
    app_name = config.get('app.name')
    print(f"اسم التطبيق: {app_name}")
    
    # اختبار تغيير الإعدادات
    config.set('shop.name', 'محل الشوامرة للهواتف')
    shop_name = config.get('shop.name')
    print(f"اسم المحل: {shop_name}")
    
    # اختبار الثيمات
    themes = config.get_available_themes()
    print(f"الثيمات المتاحة: {', '.join(themes)}")
    
    # اختبار ألوان الثيم
    blue_colors = config.get_theme_colors('blue')
    print(f"ألوان الثيم الأزرق: {blue_colors['primary']}")
    
    print("✅ اكتمل اختبار الإعدادات بنجاح!")
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام Phone Doctor")
    print("=" * 50)
    
    try:
        # اختبار قاعدة البيانات
        if not test_database():
            return False
        
        # اختبار الإعدادات
        if not test_config():
            return False
        
        print("\n" + "=" * 50)
        print("🎉 تم اختبار جميع المكونات بنجاح!")
        print("📱 Phone Doctor جاهز للاستخدام")
        print("👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
