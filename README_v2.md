# Phone Doctor v2.0 - نظام إدارة محلات صيانة الهواتف المتطور

![Phone Doctor](https://img.shields.io/badge/Phone%20Doctor-v2.0-blue)
![Python](https://img.shields.io/badge/Python-3.8+-green)
![License](https://img.shields.io/badge/License-Proprietary-red)

## 📱 نظرة عامة

Phone Doctor v2.0 هو نظام شامل ومتطور لإدارة محلات صيانة الهواتف المحمولة. يوفر النظام واجهة عصرية وسهلة الاستخدام مع ميزات متقدمة لإدارة جميع جوانب العمل.

**المطور:** محمد الشوامرة  
**الهاتف:** **********  
**جميع الحقوق محفوظة © 2024**

## ✨ الميزات الجديدة في الإصدار 2.0

### 🎨 التصميم العصري
- واجهة مستخدم حديثة مع تدرجات لونية عصرية
- شاشة ترحيب تفاعلية مع إحصائيات مباشرة
- تأثيرات hover وانتقالات سلسة
- تصميم متجاوب يتكيف مع أحجام الشاشات المختلفة

### 🔧 إدارة الصيانة المتطورة
- **عمليات CRUD كاملة**: إضافة، تعديل، حذف أوامر الصيانة
- **نظام حالات متقدم**: قيد الانتظار، قيد الإصلاح، تم الإصلاح، تم التسليم، ملغي
- **نظام أولويات**: عادي، مهم، عاجل، طارئ
- **البحث والفلترة**: بحث متقدم حسب العميل، الجهاز، الحالة، الأولوية
- **تتبع التكلفة**: تكلفة مقدرة وفعلية
- **ملاحظات الفني**: تسجيل تفصيلي لحالة الإصلاح

### 📊 نظام التقارير الشامل
- **لوحة تحكم تفاعلية** مع إحصائيات مباشرة
- **تقارير المبيعات** التفصيلية مع فلترة زمنية
- **تحليل الصيانة** مع إحصائيات الحالات
- **تقارير المخزون** مع تحليل المنتجات الأكثر مبيعاً
- **مؤشرات الأداء الرئيسية (KPI)**
- **توصيات الأعمال** الذكية
- **رسوم بيانية** لتتبع الأداء

### ⚙️ إعدادات النظام المتقدمة
- **إدارة المستخدمين**: إضافة، تعديل، حذف المستخدمين
- **نظام الصلاحيات**: مدير ومستخدم عادي
- **حماية الإعدادات**: الوصول مقتصر على المديرين فقط
- **إعدادات المحل**: اسم المحل، العنوان، معلومات المالك
- **إعدادات النظام**: العملة، اللغة، المنطقة الزمنية
- **النسخ الاحتياطي**: إنشاء واستعادة النسخ الاحتياطية

### 🏠 شاشة الترحيب التفاعلية
- **ترحيب شخصي** حسب الوقت والمستخدم
- **إحصائيات سريعة** مباشرة
- **الأنشطة الحديثة** في الوقت الفعلي
- **اختصارات سريعة** للعمليات الشائعة
- **معلومات النظام** والمطور

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- Windows 10/11
- Python 3.8 أو أحدث
- ذاكرة وصول عشوائي: 4 جيجابايت على الأقل
- مساحة تخزين: 500 ميجابايت

### المكتبات المطلوبة
```bash
pip install tkinter
pip install sqlite3
pip install datetime
pip install hashlib
pip install calendar
```

## 🚀 التثبيت والتشغيل

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/phone-doctor-v2.git
cd phone-doctor-v2
```

### 2. تشغيل البرنامج
```bash
python phone_doctor_working.py
```

### 3. تسجيل الدخول الأولي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📋 دليل الاستخدام

### تسجيل الدخول
1. أدخل اسم المستخدم وكلمة المرور
2. اختر "تذكرني" للبقاء مسجلاً
3. انقر على "تسجيل الدخول"

### إدارة الصيانة
1. انتقل إلى "إدارة الصيانة"
2. انقر على "صيانة جديدة" لإضافة أمر جديد
3. املأ معلومات العميل والجهاز
4. حدد الحالة والأولوية
5. احفظ الأمر

### عرض التقارير
1. انتقل إلى "التقارير"
2. اختر الفترة الزمنية المطلوبة
3. استعرض الإحصائيات والرسوم البيانية
4. اطبع التقرير إذا لزم الأمر

### إدارة الإعدادات
1. انتقل إلى "الإعدادات" (للمديرين فقط)
2. حدث معلومات المحل
3. أضف أو عدل المستخدمين
4. احفظ التغييرات

## 🔐 نظام الأمان

### المصادقة
- تشفير كلمات المرور باستخدام SHA-256
- جلسات آمنة مع انتهاء صلاحية
- حماية من محاولات الدخول المتكررة

### الصلاحيات
- **المدير**: وصول كامل لجميع الميزات
- **المستخدم العادي**: وصول محدود (بدون إعدادات)

## 📁 هيكل المشروع

```
phone-doctor-v2/
├── phone_doctor_working.py      # الملف الرئيسي
├── ui/
│   ├── welcome_screen.py        # شاشة الترحيب
│   ├── repairs_manager.py       # إدارة الصيانة
│   ├── reports_manager.py       # التقارير
│   ├── settings_manager.py      # الإعدادات
│   ├── sales_manager.py         # إدارة المبيعات
│   ├── customers_manager.py     # إدارة العملاء
│   └── inventory_manager.py     # إدارة المخزون
├── database/
│   └── phone_doctor.db          # قاعدة البيانات
└── README.md                    # هذا الملف
```

## 🎯 الميزات المخططة للإصدارات القادمة

- [ ] تطبيق ويب متجاوب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] إشعارات SMS للعملاء
- [ ] تقارير متقدمة مع الذكاء الاصطناعي
- [ ] نظام إدارة المواعيد
- [ ] تكامل مع منصات التجارة الإلكترونية

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يرجى:
1. التحقق من قائمة المشاكل المعروفة
2. إنشاء تقرير مشكلة جديد مع التفاصيل
3. تضمين لقطات شاشة إذا أمكن

## 👨‍💻 المطور

**محمد الشوامرة**
- 📞 الهاتف: **********
- 📧 البريد الإلكتروني: [البريد الإلكتروني]
- 🌐 الموقع الإلكتروني: [الموقع]

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمختبرين الذين ساعدوا في تطوير هذا النظام.

---

© 2024 Phone Doctor v2.0 - جميع الحقوق محفوظة
