#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح قاعدة البيانات
Fix Database Structure

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import os

def fix_database():
    """إصلاح هيكل قاعدة البيانات"""
    db_path = 'database/phone_doctor.db'
    
    print("🔧 إصلاح قاعدة البيانات...")
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من هيكل الجدول الحالي
        cursor.execute("PRAGMA table_info(repairs)")
        columns = cursor.fetchall()
        
        print("📋 الأعمدة الحالية:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # التحقق من وجود البيانات
        cursor.execute("SELECT COUNT(*) FROM repairs")
        count = cursor.fetchone()[0]
        print(f"\n📊 عدد السجلات: {count}")
        
        if count > 0:
            # عرض عينة من البيانات
            cursor.execute("SELECT * FROM repairs LIMIT 1")
            sample = cursor.fetchone()
            if sample:
                print("\n📝 عينة من البيانات:")
                for i, col in enumerate(columns):
                    print(f"  {col[1]}: {sample[i] if i < len(sample) else 'NULL'}")
        
        # إنشاء جدول جديد بالهيكل الصحيح
        print("\n🔨 إنشاء جدول جديد...")
        
        # إنشاء جدول مؤقت
        cursor.execute("DROP TABLE IF EXISTS repairs_new")
        
        new_table = '''
            CREATE TABLE repairs_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                repair_number TEXT UNIQUE,
                customer_name TEXT NOT NULL,
                customer_phone TEXT NOT NULL,
                customer_email TEXT,
                customer_address TEXT,
                device_type TEXT NOT NULL,
                device_brand TEXT,
                device_model TEXT,
                device_color TEXT,
                device_imei TEXT,
                device_serial TEXT,
                problem_description TEXT NOT NULL,
                problem_category TEXT,
                status TEXT DEFAULT 'pending',
                priority TEXT DEFAULT 'normal',
                technician_name TEXT,
                assigned_date TIMESTAMP,
                estimated_cost REAL DEFAULT 0,
                actual_cost REAL DEFAULT 0,
                parts_cost REAL DEFAULT 0,
                labor_cost REAL DEFAULT 0,
                total_cost REAL DEFAULT 0,
                parts_needed TEXT,
                parts_used TEXT,
                warranty_period INTEGER DEFAULT 0,
                warranty_start_date TIMESTAMP,
                warranty_end_date TIMESTAMP,
                technician_notes TEXT,
                repair_notes TEXT,
                customer_notes TEXT,
                internal_notes TEXT,
                images_path TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_date TIMESTAMP,
                delivered_date TIMESTAMP,
                created_by TEXT,
                updated_by TEXT
            )
        '''
        
        cursor.execute(new_table)
        print("✅ تم إنشاء الجدول الجديد")
        
        # نسخ البيانات إذا كانت موجودة
        if count > 0:
            print("📋 نسخ البيانات...")
            
            # نسخ البيانات الموجودة
            copy_query = '''
                INSERT INTO repairs_new (
                    id, repair_number, customer_name, customer_phone, customer_email, customer_address,
                    device_type, device_brand, device_model, device_color, device_imei, device_serial,
                    problem_description, problem_category, status, priority, technician_name,
                    assigned_date, estimated_cost, actual_cost, parts_cost, labor_cost, total_cost,
                    parts_needed, parts_used, warranty_period, warranty_start_date, warranty_end_date,
                    technician_notes, repair_notes, customer_notes, internal_notes, images_path,
                    created_date, updated_date, completed_date, delivered_date, created_by, updated_by
                )
                SELECT 
                    id, repair_number, customer_name, customer_phone, customer_email, customer_address,
                    device_type, device_brand, device_model, device_color, device_imei, device_serial,
                    problem_description, problem_category, status, priority, technician_name,
                    assigned_date, estimated_cost, actual_cost, parts_cost, labor_cost, total_cost,
                    parts_needed, parts_used, warranty_period, warranty_start_date, warranty_end_date,
                    technician_notes, repair_notes, customer_notes, internal_notes, images_path,
                    created_date, updated_date, completed_date, delivered_date, created_by, updated_by
                FROM repairs
            '''
            
            try:
                cursor.execute(copy_query)
                print("✅ تم نسخ البيانات بنجاح")
            except Exception as e:
                print(f"⚠️ خطأ في نسخ البيانات: {e}")
                print("سيتم إنشاء جدول فارغ")
        
        # حذف الجدول القديم وإعادة تسمية الجديد
        cursor.execute("DROP TABLE repairs")
        cursor.execute("ALTER TABLE repairs_new RENAME TO repairs")
        
        print("✅ تم إصلاح هيكل الجدول")
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM repairs")
        final_count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات النهائي: {final_count}")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_database()
