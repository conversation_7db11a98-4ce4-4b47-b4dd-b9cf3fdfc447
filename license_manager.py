#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التراخيص المتطور
Advanced License Management System

المطور: محمد الشوامرة - **********
"""

import hashlib
import datetime
import json
import os
import uuid
import base64
from cryptography.fernet import Fernet
import tkinter as tk
from tkinter import messagebox

class LicenseManager:
    """مدير التراخيص المتطور"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.license_file = "license.dat"
        self.trial_days = 15
        self.setup_license_table()
    
    def setup_license_table(self):
        """إعداد جدول التراخيص"""
        try:
            license_table = '''
                CREATE TABLE IF NOT EXISTS license_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shop_name TEXT NOT NULL,
                    owner_name TEXT NOT NULL,
                    phone TEXT NOT NULL,
                    email TEXT,
                    address TEXT,
                    license_key TEXT UNIQUE,
                    license_type TEXT DEFAULT 'trial',
                    activation_date TIMESTAMP,
                    expiry_date TIMESTAMP,
                    is_active INTEGER DEFAULT 1,
                    machine_id TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''
            self.db_manager.execute_query(license_table)
        except Exception as e:
            print(f"خطأ في إنشاء جدول التراخيص: {e}")
    
    def get_machine_id(self):
        """الحصول على معرف الجهاز الفريد"""
        try:
            import platform
            import socket
            
            # معلومات الجهاز
            hostname = socket.gethostname()
            system = platform.system()
            processor = platform.processor()
            
            # إنشاء معرف فريد
            machine_string = f"{hostname}-{system}-{processor}"
            machine_id = hashlib.sha256(machine_string.encode()).hexdigest()[:16]
            return machine_id
        except:
            return "DEFAULT_MACHINE"
    
    def generate_license_key(self, shop_name, owner_name, license_type="full"):
        """توليد مفتاح ترخيص"""
        try:
            # بيانات الترخيص
            license_data = {
                'shop_name': shop_name,
                'owner_name': owner_name,
                'license_type': license_type,
                'machine_id': self.get_machine_id(),
                'generated_date': datetime.datetime.now().isoformat()
            }
            
            # تحويل البيانات إلى نص
            license_string = json.dumps(license_data)
            
            # تشفير البيانات
            encoded = base64.b64encode(license_string.encode()).decode()
            
            # إنشاء مفتاح الترخيص
            license_key = f"PD-{encoded[:8]}-{encoded[8:16]}-{encoded[16:24]}-{encoded[24:32]}"
            
            return license_key, license_data
        except Exception as e:
            print(f"خطأ في توليد مفتاح الترخيص: {e}")
            return None, None
    
    def validate_license_key(self, license_key):
        """التحقق من صحة مفتاح الترخيص"""
        try:
            # إزالة البادئة والشرطات
            if not license_key.startswith("PD-"):
                return False
            
            clean_key = license_key.replace("PD-", "").replace("-", "")
            
            # فك التشفير
            decoded = base64.b64decode(clean_key.encode()).decode()
            license_data = json.loads(decoded)
            
            # التحقق من معرف الجهاز
            if license_data.get('machine_id') != self.get_machine_id():
                return False
            
            return True, license_data
        except:
            return False, None
    
    def start_trial(self, shop_name, owner_name, phone, email="", address=""):
        """بدء الفترة التجريبية"""
        try:
            # التحقق من وجود ترخيص سابق
            existing = self.db_manager.fetch_one("SELECT * FROM license_info WHERE machine_id = ?", (self.get_machine_id(),))
            if existing:
                return False, "يوجد ترخيص مسجل مسبقاً لهذا الجهاز"
            
            # توليد مفتاح تجريبي
            license_key, license_data = self.generate_license_key(shop_name, owner_name, "trial")
            
            if not license_key:
                return False, "فشل في توليد مفتاح الترخيص"
            
            # حساب تاريخ انتهاء الفترة التجريبية
            activation_date = datetime.datetime.now()
            expiry_date = activation_date + datetime.timedelta(days=self.trial_days)
            
            # حفظ معلومات الترخيص
            query = """
                INSERT INTO license_info (shop_name, owner_name, phone, email, address, 
                                        license_key, license_type, activation_date, expiry_date, 
                                        machine_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (shop_name, owner_name, phone, email, address, license_key, 
                     "trial", activation_date, expiry_date, self.get_machine_id())
            
            self.db_manager.execute_query(query, params)
            
            # حفظ ملف الترخيص
            self.save_license_file(license_key, license_data, expiry_date)
            
            return True, f"تم تفعيل الفترة التجريبية لمدة {self.trial_days} يوم"
            
        except Exception as e:
            print(f"خطأ في بدء الفترة التجريبية: {e}")
            return False, "فشل في تفعيل الفترة التجريبية"
    
    def activate_license(self, license_key):
        """تفعيل الترخيص الكامل"""
        try:
            # التحقق من صحة المفتاح
            is_valid, license_data = self.validate_license_key(license_key)
            if not is_valid:
                return False, "مفتاح الترخيص غير صحيح"
            
            # البحث عن الترخيص في قاعدة البيانات
            existing = self.db_manager.fetch_one("SELECT * FROM license_info WHERE machine_id = ?", (self.get_machine_id(),))
            
            if existing:
                # تحديث الترخيص الموجود
                query = """
                    UPDATE license_info 
                    SET license_key = ?, license_type = 'full', expiry_date = NULL, is_active = 1
                    WHERE machine_id = ?
                """
                self.db_manager.execute_query(query, (license_key, self.get_machine_id()))
            else:
                return False, "لم يتم العثور على معلومات الترخيص"
            
            # حفظ ملف الترخيص
            self.save_license_file(license_key, license_data, None)
            
            return True, "تم تفعيل الترخيص الكامل بنجاح"
            
        except Exception as e:
            print(f"خطأ في تفعيل الترخيص: {e}")
            return False, "فشل في تفعيل الترخيص"
    
    def save_license_file(self, license_key, license_data, expiry_date):
        """حفظ ملف الترخيص"""
        try:
            license_info = {
                'license_key': license_key,
                'license_data': license_data,
                'expiry_date': expiry_date.isoformat() if expiry_date else None,
                'machine_id': self.get_machine_id()
            }
            
            # تشفير البيانات
            key = Fernet.generate_key()
            f = Fernet(key)
            encrypted_data = f.encrypt(json.dumps(license_info).encode())
            
            # حفظ الملف
            with open(self.license_file, 'wb') as file:
                file.write(key + b'|||' + encrypted_data)
                
        except Exception as e:
            print(f"خطأ في حفظ ملف الترخيص: {e}")
    
    def load_license_file(self):
        """تحميل ملف الترخيص"""
        try:
            if not os.path.exists(self.license_file):
                return None
            
            with open(self.license_file, 'rb') as file:
                content = file.read()
                
            # فصل المفتاح والبيانات
            key, encrypted_data = content.split(b'|||', 1)
            
            # فك التشفير
            f = Fernet(key)
            decrypted_data = f.decrypt(encrypted_data)
            license_info = json.loads(decrypted_data.decode())
            
            return license_info
            
        except Exception as e:
            print(f"خطأ في تحميل ملف الترخيص: {e}")
            return None
    
    def check_license_status(self):
        """فحص حالة الترخيص"""
        try:
            # تحميل ملف الترخيص
            license_info = self.load_license_file()
            if not license_info:
                return "no_license", "لا يوجد ترخيص"
            
            # التحقق من معرف الجهاز
            if license_info.get('machine_id') != self.get_machine_id():
                return "invalid", "الترخيص غير صالح لهذا الجهاز"
            
            # التحقق من انتهاء الصلاحية
            expiry_date_str = license_info.get('expiry_date')
            if expiry_date_str:
                expiry_date = datetime.datetime.fromisoformat(expiry_date_str)
                if datetime.datetime.now() > expiry_date:
                    return "expired", "انتهت صلاحية الفترة التجريبية"
                
                # حساب الأيام المتبقية
                remaining_days = (expiry_date - datetime.datetime.now()).days
                return "trial", f"الفترة التجريبية - متبقي {remaining_days} يوم"
            else:
                return "full", "ترخيص كامل"
                
        except Exception as e:
            print(f"خطأ في فحص حالة الترخيص: {e}")
            return "error", "خطأ في فحص الترخيص"
    
    def get_license_info(self):
        """الحصول على معلومات الترخيص"""
        try:
            license_record = self.db_manager.fetch_one(
                "SELECT * FROM license_info WHERE machine_id = ?", 
                (self.get_machine_id(),)
            )
            return license_record
        except Exception as e:
            print(f"خطأ في جلب معلومات الترخيص: {e}")
            return None
