#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية - Main Window
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QStackedWidget, QFrame, QLabel, QPushButton,
                             QScrollArea, QGridLayout, QMessageBox, QMenuBar,
                             QStatusBar, QToolBar, QAction)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

from .dashboard import Dashboard
from .sidebar import Sidebar
from .repairs_window import RepairsWindow
from .inventory_window import InventoryWindow
from .suppliers_window import SuppliersWindow
from .sales_window import SalesWindow
from .financial_window import FinancialWindow
from .reports_window import ReportsWindow
from .settings_window import SettingsWindow

class MainWindow(QMainWindow):
    def __init__(self, db_manager, config):
        super().__init__()
        self.db_manager = db_manager
        self.config = config
        self.current_theme = self.config.get('ui.theme', 'blue')
        
        self.init_ui()
        self.apply_theme()
        self.setup_connections()
        
    def init_ui(self):
        """إعداد الواجهة الرئيسية"""
        self.setWindowTitle("Phone Doctor - نظام إدارة محلات صيانة الهواتف")
        self.setMinimumSize(1200, 800)
        
        # إعداد الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.sidebar = Sidebar(self.config)
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        self.content_area = QStackedWidget()
        main_layout.addWidget(self.content_area, 1)
        
        # إنشاء النوافذ
        self.create_windows()
        
        # إعداد شريط القوائم
        self.create_menu_bar()
        
        # إعداد شريط الحالة
        self.create_status_bar()
        
        # إعداد شريط الأدوات
        self.create_toolbar()
        
    def create_windows(self):
        """إنشاء نوافذ التطبيق"""
        # لوحة التحكم
        self.dashboard = Dashboard(self.db_manager, self.config)
        self.content_area.addWidget(self.dashboard)
        
        # نافذة الصيانة
        self.repairs_window = RepairsWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.repairs_window)
        
        # نافذة المخزون
        self.inventory_window = InventoryWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.inventory_window)
        
        # نافذة الموردين
        self.suppliers_window = SuppliersWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.suppliers_window)
        
        # نافذة المبيعات
        self.sales_window = SalesWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.sales_window)
        
        # النوافذ المالية
        self.financial_window = FinancialWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.financial_window)
        
        # نافذة التقارير
        self.reports_window = ReportsWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.reports_window)
        
        # نافذة الإعدادات
        self.settings_window = SettingsWindow(self.db_manager, self.config)
        self.content_area.addWidget(self.settings_window)
        
        # تعيين لوحة التحكم كنافذة افتراضية
        self.content_area.setCurrentWidget(self.dashboard)
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        new_action = QAction('جديد', self)
        new_action.setShortcut('Ctrl+N')
        file_menu.addAction(new_action)
        
        backup_action = QAction('نسخ احتياطي', self)
        backup_action.triggered.connect(self.backup_database)
        file_menu.addAction(backup_action)
        
        restore_action = QAction('استعادة', self)
        restore_action.triggered.connect(self.restore_database)
        file_menu.addAction(restore_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu('عرض')
        
        theme_menu = view_menu.addMenu('الثيم')
        themes = self.config.get_available_themes()
        for theme in themes:
            theme_action = QAction(theme.title(), self)
            theme_action.triggered.connect(lambda checked, t=theme: self.change_theme(t))
            theme_menu.addAction(theme_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # معلومات المطور
        developer_label = QLabel("جميع الحقوق محفوظة لـ محمد الشوامرة - 📞 0566000140")
        developer_label.setStyleSheet("color: #666; font-size: 10px;")
        self.status_bar.addPermanentWidget(developer_label)
        
        # حالة قاعدة البيانات
        self.db_status_label = QLabel("قاعدة البيانات: متصلة")
        self.db_status_label.setStyleSheet("color: green; font-weight: bold;")
        self.status_bar.addWidget(self.db_status_label)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        self.addToolBar(toolbar)
        
        # أزرار سريعة
        dashboard_action = QAction('🏠', self)
        dashboard_action.setText('الرئيسية')
        dashboard_action.triggered.connect(lambda: self.show_window('dashboard'))
        toolbar.addAction(dashboard_action)
        
        repairs_action = QAction('🔧', self)
        repairs_action.setText('الصيانة')
        repairs_action.triggered.connect(lambda: self.show_window('repairs'))
        toolbar.addAction(repairs_action)
        
        inventory_action = QAction('📦', self)
        inventory_action.setText('المخزون')
        inventory_action.triggered.connect(lambda: self.show_window('inventory'))
        toolbar.addAction(inventory_action)
        
        sales_action = QAction('💰', self)
        sales_action.setText('المبيعات')
        sales_action.triggered.connect(lambda: self.show_window('sales'))
        toolbar.addAction(sales_action)
        
    def setup_connections(self):
        """إعداد الاتصالات بين العناصر"""
        # اتصالات الشريط الجانبي
        self.sidebar.menu_clicked.connect(self.show_window)
        
        # اتصالات نافذة الإعدادات
        self.settings_window.theme_changed.connect(self.change_theme)
        
    def show_window(self, window_name):
        """عرض نافذة معينة"""
        windows = {
            'dashboard': self.dashboard,
            'repairs': self.repairs_window,
            'inventory': self.inventory_window,
            'suppliers': self.suppliers_window,
            'sales': self.sales_window,
            'financial': self.financial_window,
            'reports': self.reports_window,
            'settings': self.settings_window
        }
        
        if window_name in windows:
            self.content_area.setCurrentWidget(windows[window_name])
            # تحديث البيانات عند عرض النافذة
            if hasattr(windows[window_name], 'refresh_data'):
                windows[window_name].refresh_data()
    
    def change_theme(self, theme_name):
        """تغيير ثيم التطبيق"""
        self.current_theme = theme_name
        self.config.set('ui.theme', theme_name)
        self.apply_theme()
        
        # إشعار جميع النوافذ بتغيير الثيم
        for i in range(self.content_area.count()):
            widget = self.content_area.widget(i)
            if hasattr(widget, 'apply_theme'):
                widget.apply_theme()
        
        self.sidebar.apply_theme()
    
    def apply_theme(self):
        """تطبيق الثيم الحالي"""
        colors = self.config.get_theme_colors(self.current_theme)
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {colors['background']};
                color: {colors['text']};
            }}
            QMenuBar {{
                background-color: {colors['surface']};
                color: {colors['text']};
                border-bottom: 1px solid {colors['primary']};
            }}
            QMenuBar::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            QStatusBar {{
                background-color: {colors['surface']};
                color: {colors['text']};
                border-top: 1px solid {colors['primary']};
            }}
            QToolBar {{
                background-color: {colors['surface']};
                border-bottom: 1px solid {colors['primary']};
            }}
        """)
    
    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        from PyQt5.QtWidgets import QFileDialog
        from datetime import datetime
        
        default_name = f"phone_doctor_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ النسخة الاحتياطية", default_name, "Database Files (*.db)"
        )
        
        if file_path:
            if self.db_manager.backup_database(file_path):
                QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية!")
    
    def restore_database(self):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار النسخة الاحتياطية", "", "Database Files (*.db)"
        )
        
        if file_path:
            reply = QMessageBox.question(
                self, "تأكيد", 
                "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية!",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if self.db_manager.restore_database(file_path):
                    QMessageBox.information(self, "نجح", "تم استعادة النسخة الاحتياطية بنجاح!")
                    # تحديث جميع النوافذ
                    self.refresh_all_windows()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في استعادة النسخة الاحتياطية!")
    
    def refresh_all_windows(self):
        """تحديث جميع النوافذ"""
        for i in range(self.content_area.count()):
            widget = self.content_area.widget(i)
            if hasattr(widget, 'refresh_data'):
                widget.refresh_data()
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = f"""
        <h2>Phone Doctor</h2>
        <p><b>نظام إدارة محلات صيانة الهواتف الاحترافي</b></p>
        <p>الإصدار: {self.config.get('app.version')}</p>
        <p>المطور: {self.config.get('app.developer')}</p>
        <p>الهاتف: {self.config.get('app.phone')}</p>
        <p>جميع الحقوق محفوظة © 2024</p>
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def closeEvent(self, event):
        """معالجة إغلاق التطبيق"""
        reply = QMessageBox.question(
            self, "تأكيد الخروج", 
            "هل أنت متأكد من الخروج من البرنامج؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # قطع الاتصال بقاعدة البيانات
            self.db_manager.disconnect()
            event.accept()
        else:
            event.ignore()
