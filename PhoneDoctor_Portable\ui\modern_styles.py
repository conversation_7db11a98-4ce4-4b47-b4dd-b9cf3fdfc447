#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الأنماط العصرية - Modern Styles
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk

class ModernStyles:
    """فئة الأنماط العصرية"""
    
    def __init__(self):
        self.colors = {
            # الألوان الأساسية
            'primary': '#2563eb',      # أزرق عصري
            'primary_dark': '#1d4ed8', # أزرق داكن
            'primary_light': '#3b82f6', # أزرق فاتح
            'secondary': '#64748b',     # رمادي أزرق
            'accent': '#06b6d4',       # سماوي
            'success': '#10b981',      # أخضر
            'warning': '#f59e0b',      # برتقالي
            'danger': '#ef4444',       # أحمر
            'info': '#3b82f6',         # أزرق معلومات
            
            # ألوان الخلفية
            'bg_primary': '#ffffff',    # أبيض
            'bg_secondary': '#f8fafc',  # رمادي فاتح جداً
            'bg_tertiary': '#f1f5f9',   # رمادي فاتح
            'bg_dark': '#0f172a',       # أسود مزرق
            'bg_sidebar': '#1e293b',    # رمادي داكن للشريط الجانبي
            'bg_card': '#ffffff',       # خلفية البطاقات
            'bg_hover': '#f1f5f9',      # خلفية عند التمرير
            'bg_active': '#e2e8f0',     # خلفية عند النشاط
            
            # ألوان النصوص
            'text_primary': '#0f172a',   # نص أساسي
            'text_secondary': '#475569', # نص ثانوي
            'text_muted': '#94a3b8',     # نص خافت
            'text_white': '#ffffff',     # نص أبيض
            'text_light': '#e2e8f0',     # نص فاتح
            
            # ألوان الحدود
            'border_light': '#e2e8f0',   # حدود فاتحة
            'border_medium': '#cbd5e1',  # حدود متوسطة
            'border_dark': '#64748b',    # حدود داكنة
            
            # ألوان الظلال
            'shadow_light': 'rgba(0, 0, 0, 0.05)',
            'shadow_medium': 'rgba(0, 0, 0, 0.1)',
            'shadow_dark': 'rgba(0, 0, 0, 0.25)',
        }
        
        self.fonts = {
            'heading_large': ('Segoe UI', 24, 'bold'),
            'heading_medium': ('Segoe UI', 18, 'bold'),
            'heading_small': ('Segoe UI', 14, 'bold'),
            'body_large': ('Segoe UI', 12, 'normal'),
            'body_medium': ('Segoe UI', 11, 'normal'),
            'body_small': ('Segoe UI', 10, 'normal'),
            'caption': ('Segoe UI', 9, 'normal'),
            'button': ('Segoe UI', 11, 'bold'),
            'sidebar': ('Segoe UI', 12, 'normal'),
        }
        
        self.spacing = {
            'xs': 4,
            'sm': 8,
            'md': 16,
            'lg': 24,
            'xl': 32,
            'xxl': 48,
        }
        
        self.border_radius = {
            'sm': 4,
            'md': 8,
            'lg': 12,
            'xl': 16,
            'full': 50,
        }
    
    def configure_ttk_styles(self):
        """تكوين أنماط ttk"""
        style = ttk.Style()
        
        # تعيين الثيم الأساسي
        style.theme_use('clam')
        
        # أنماط الأزرار
        style.configure('Modern.TButton',
                       background=self.colors['primary'],
                       foreground=self.colors['text_white'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(16, 8),
                       font=self.fonts['button'])
        
        style.map('Modern.TButton',
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', self.colors['primary_dark'])])
        
        # أنماط الأزرار الثانوية
        style.configure('Secondary.TButton',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       relief='solid',
                       focuscolor='none',
                       padding=(16, 8),
                       font=self.fonts['button'])
        
        style.map('Secondary.TButton',
                 background=[('active', self.colors['bg_hover']),
                           ('pressed', self.colors['bg_active'])])
        
        # أنماط الأزرار الخطرة
        style.configure('Danger.TButton',
                       background=self.colors['danger'],
                       foreground=self.colors['text_white'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(16, 8),
                       font=self.fonts['button'])
        
        style.map('Danger.TButton',
                 background=[('active', '#dc2626'),
                           ('pressed', '#b91c1c')])
        
        # أنماط الأزرار الناجحة
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground=self.colors['text_white'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(16, 8),
                       font=self.fonts['button'])
        
        style.map('Success.TButton',
                 background=[('active', '#059669'),
                           ('pressed', '#047857')])
        
        # أنماط الإطارات
        style.configure('Card.TFrame',
                       background=self.colors['bg_card'],
                       relief='flat',
                       borderwidth=1)
        
        style.configure('Sidebar.TFrame',
                       background=self.colors['bg_sidebar'],
                       relief='flat',
                       borderwidth=0)
        
        # أنماط التسميات
        style.configure('Heading.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['heading_medium'])
        
        style.configure('Subheading.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['heading_small'])
        
        style.configure('Body.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['body_medium'])
        
        style.configure('Caption.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_muted'],
                       font=self.fonts['caption'])
        
        style.configure('Sidebar.TLabel',
                       background=self.colors['bg_sidebar'],
                       foreground=self.colors['text_light'],
                       font=self.fonts['sidebar'])
        
        # أنماط حقول الإدخال
        style.configure('Modern.TEntry',
                       fieldbackground=self.colors['bg_primary'],
                       borderwidth=1,
                       relief='solid',
                       padding=(12, 8),
                       font=self.fonts['body_medium'])
        
        style.map('Modern.TEntry',
                 focuscolor=[('focus', self.colors['primary'])])
        
        # أنماط القوائم المنسدلة
        style.configure('Modern.TCombobox',
                       fieldbackground=self.colors['bg_primary'],
                       borderwidth=1,
                       relief='solid',
                       padding=(12, 8),
                       font=self.fonts['body_medium'])
        
        # أنماط الجداول
        style.configure('Modern.Treeview',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_primary'],
                       borderwidth=1,
                       relief='solid',
                       font=self.fonts['body_medium'])
        
        style.configure('Modern.Treeview.Heading',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       relief='solid',
                       font=self.fonts['heading_small'])
        
        style.map('Modern.Treeview',
                 background=[('selected', self.colors['primary']),
                           ('focus', self.colors['bg_hover'])])
        
        # أنماط دفتر الملاحظات
        style.configure('Modern.TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=0,
                       tabmargins=[0, 0, 0, 0])
        
        style.configure('Modern.TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_secondary'],
                       padding=(20, 12),
                       font=self.fonts['body_medium'],
                       borderwidth=0)
        
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', self.colors['bg_primary']),
                           ('active', self.colors['bg_hover'])],
                 foreground=[('selected', self.colors['primary']),
                           ('active', self.colors['text_primary'])])
        
        # أنماط أشرطة التقدم
        style.configure('Modern.TProgressbar',
                       background=self.colors['primary'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])
        
        # أنماط أشرطة التمرير
        style.configure('Modern.Vertical.TScrollbar',
                       background=self.colors['bg_secondary'],
                       borderwidth=0,
                       arrowcolor=self.colors['text_muted'],
                       troughcolor=self.colors['bg_secondary'])
        
        style.configure('Modern.Horizontal.TScrollbar',
                       background=self.colors['bg_secondary'],
                       borderwidth=0,
                       arrowcolor=self.colors['text_muted'],
                       troughcolor=self.colors['bg_secondary'])
    
    def get_card_style(self):
        """الحصول على نمط البطاقة"""
        return {
            'bg': self.colors['bg_card'],
            'relief': 'flat',
            'bd': 1,
            'highlightbackground': self.colors['border_light'],
            'highlightthickness': 1,
            'padx': self.spacing['lg'],
            'pady': self.spacing['lg']
        }
    
    def get_sidebar_style(self):
        """الحصول على نمط الشريط الجانبي"""
        return {
            'bg': self.colors['bg_sidebar'],
            'relief': 'flat',
            'bd': 0,
            'padx': 0,
            'pady': 0
        }
    
    def get_button_style(self, variant='primary'):
        """الحصول على نمط الزر"""
        styles = {
            'primary': {
                'bg': self.colors['primary'],
                'fg': self.colors['text_white'],
                'activebackground': self.colors['primary_dark'],
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'font': self.fonts['button'],
                'cursor': 'hand2',
                'padx': self.spacing['md'],
                'pady': self.spacing['sm']
            },
            'secondary': {
                'bg': self.colors['bg_secondary'],
                'fg': self.colors['text_primary'],
                'activebackground': self.colors['bg_hover'],
                'activeforeground': self.colors['text_primary'],
                'relief': 'flat',
                'bd': 1,
                'font': self.fonts['button'],
                'cursor': 'hand2',
                'padx': self.spacing['md'],
                'pady': self.spacing['sm']
            },
            'danger': {
                'bg': self.colors['danger'],
                'fg': self.colors['text_white'],
                'activebackground': '#dc2626',
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'font': self.fonts['button'],
                'cursor': 'hand2',
                'padx': self.spacing['md'],
                'pady': self.spacing['sm']
            },
            'success': {
                'bg': self.colors['success'],
                'fg': self.colors['text_white'],
                'activebackground': '#059669',
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'font': self.fonts['button'],
                'cursor': 'hand2',
                'padx': self.spacing['md'],
                'pady': self.spacing['sm']
            }
        }
        return styles.get(variant, styles['primary'])
    
    def get_entry_style(self):
        """الحصول على نمط حقل الإدخال"""
        return {
            'bg': self.colors['bg_primary'],
            'fg': self.colors['text_primary'],
            'relief': 'solid',
            'bd': 1,
            'font': self.fonts['body_medium'],
            'insertbackground': self.colors['primary'],
            'selectbackground': self.colors['primary'],
            'selectforeground': self.colors['text_white']
        }
    
    def get_label_style(self, variant='body'):
        """الحصول على نمط التسمية"""
        styles = {
            'heading': {
                'bg': self.colors['bg_primary'],
                'fg': self.colors['text_primary'],
                'font': self.fonts['heading_medium']
            },
            'subheading': {
                'bg': self.colors['bg_primary'],
                'fg': self.colors['text_secondary'],
                'font': self.fonts['heading_small']
            },
            'body': {
                'bg': self.colors['bg_primary'],
                'fg': self.colors['text_primary'],
                'font': self.fonts['body_medium']
            },
            'caption': {
                'bg': self.colors['bg_primary'],
                'fg': self.colors['text_muted'],
                'font': self.fonts['caption']
            },
            'sidebar': {
                'bg': self.colors['bg_sidebar'],
                'fg': self.colors['text_light'],
                'font': self.fonts['sidebar']
            }
        }
        return styles.get(variant, styles['body'])
