#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor - النسخة الكاملة مع جميع الميزات
Complete Phone Doctor Application

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database.db_manager import DatabaseManager
    from utils.config import Config
    from utils.sample_data_generator import SampleDataGenerator
    from auth.auth_manager import AuthManager
    from ui.modern_styles import ModernStyles
    from ui.modern_sidebar import ModernSidebar
    from ui.modern_header import ModernHeader, ModernPageHeader, ModernCard
    from ui.login_window import LoginWindow
    print("✅ تم تحميل جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    messagebox.showerror("خطأ", f"خطأ في تحميل الوحدات:\n{e}")

class PhoneDoctorCompleteApp:
    """تطبيق Phone Doctor الكامل مع جميع الميزات"""
    
    def __init__(self):
        # إخفاء النافذة الرئيسية مؤقتاً
        self.root = tk.Tk()
        self.root.withdraw()
        
        # تهيئة المكونات الأساسية
        self.setup_core_components()
        
        # عرض نافذة تسجيل الدخول
        self.show_login()
    
    def setup_core_components(self):
        """إعداد المكونات الأساسية"""
        try:
            # تهيئة قاعدة البيانات والإعدادات
            self.db_manager = DatabaseManager()
            self.config = Config()
            self.auth_manager = AuthManager()
            
            if not self.db_manager.initialize_database():
                messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
                sys.exit(1)
            
            # تهيئة الأنماط العصرية
            self.styles = ModernStyles()
            self.styles.configure_ttk_styles()
            
            # مولد البيانات التجريبية
            self.sample_generator = SampleDataGenerator()
            
            print("✅ تم إعداد المكونات الأساسية")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد المكونات: {e}")
            messagebox.showerror("خطأ", f"خطأ في إعداد التطبيق:\n{e}")
            sys.exit(1)
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = LoginWindow(self.auth_manager, self.on_login_success)
        login_window.show()
    
    def on_login_success(self):
        """معالجة نجاح تسجيل الدخول"""
        # إظهار النافذة الرئيسية
        self.root.deiconify()
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # توليد البيانات التجريبية إذا لم تكن موجودة
        self.generate_sample_data_if_needed()
        
        # إعداد الواجهة العصرية
        self.setup_modern_ui()
        
        # تحميل البيانات
        self.load_dashboard_data()
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("Phone Doctor - نظام إدارة محلات صيانة الهواتف")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        self.root.state('zoomed')  # تكبير النافذة
        
        # تكوين النافذة
        self.root.configure(bg=self.styles.colors['bg_primary'])
        
        # متغيرات التطبيق
        self.current_page = "dashboard"
        self.page_titles = {
            "dashboard": ("لوحة التحكم", "🏠"),
            "repairs": ("إدارة الصيانة", "🔧"),
            "inventory": ("إدارة المخزون", "📦"),
            "suppliers": ("إدارة الموردين", "🏪"),
            "sales": ("إدارة المبيعات", "💰"),
            "financial": ("الشؤون المالية", "💳"),
            "reports": ("التقارير والإحصائيات", "📊"),
            "settings": ("الإعدادات والتخصيص", "⚙️")
        }
        
        # معالجة إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def generate_sample_data_if_needed(self):
        """توليد البيانات التجريبية إذا لم تكن موجودة"""
        try:
            # التحقق من وجود بيانات
            conn = self.db_manager.connect()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM customers")
                customer_count = cursor.fetchone()[0]
                conn.close()
                
                if customer_count == 0:
                    print("📊 توليد البيانات التجريبية...")
                    self.sample_generator.generate_all_sample_data()
                    messagebox.showinfo("معلومات", "تم توليد البيانات التجريبية بنجاح!\nيمكنك الآن استكشاف جميع ميزات البرنامج.")
        
        except Exception as e:
            print(f"⚠️ تحذير في توليد البيانات التجريبية: {e}")
    
    def setup_modern_ui(self):
        """إعداد الواجهة العصرية"""
        try:
            # الإطار الرئيسي
            main_container = tk.Frame(
                self.root,
                bg=self.styles.colors['bg_primary'],
                relief='flat',
                bd=0
            )
            main_container.pack(fill=tk.BOTH, expand=True)
            
            # المنطقة الرئيسية للمحتوى (على اليسار)
            self.content_area = tk.Frame(
                main_container,
                bg=self.styles.colors['bg_secondary'],
                relief='flat',
                bd=0
            )
            self.content_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            
            # الشريط الجانبي العصري (على اليمين)
            self.sidebar = ModernSidebar(
                main_container,
                on_menu_select=self.on_page_change
            )
            self.sidebar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # رأس الصفحة العصري
            self.header = ModernHeader(self.content_area)
            self.header.pack(fill=tk.X)
            
            # منطقة المحتوى القابلة للتمرير
            self.setup_content_area()
            
            # تحديث رأس الصفحة للصفحة الافتراضية
            self.update_page_header("dashboard")
            
            print("✅ تم إعداد الواجهة العصرية بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد الواجهة: {e}")
            messagebox.showerror("خطأ", f"خطأ في إعداد الواجهة العصرية:\n{e}")
    
    def setup_content_area(self):
        """إعداد منطقة المحتوى"""
        # إطار التمرير
        self.scroll_frame = tk.Frame(
            self.content_area,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        self.scroll_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas للتمرير
        self.canvas = tk.Canvas(
            self.scroll_frame,
            bg=self.styles.colors['bg_secondary'],
            highlightthickness=0,
            relief='flat',
            bd=0
        )
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(
            self.scroll_frame,
            orient=tk.VERTICAL,
            command=self.canvas.yview,
            style='Modern.Vertical.TScrollbar'
        )
        
        # ربط Canvas بشريط التمرير
        self.canvas.configure(yscrollcommand=v_scrollbar.set)
        
        # إطار المحتوى الداخلي
        self.scrollable_frame = tk.Frame(
            self.canvas,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        
        # إضافة إطار المحتوى إلى Canvas
        self.canvas_frame = self.canvas.create_window(
            (0, 0),
            window=self.scrollable_frame,
            anchor="nw"
        )
        
        # تخطيط العناصر
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط أحداث التمرير
        self.scrollable_frame.bind('<Configure>', self.on_frame_configure)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.canvas.bind_all('<MouseWheel>', self.on_mousewheel)
        
        # إنشاء صفحات المحتوى
        self.create_pages()
    
    def on_frame_configure(self, event=None):
        """تحديث منطقة التمرير عند تغيير حجم الإطار"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def on_canvas_configure(self, event=None):
        """تحديث عرض الإطار الداخلي عند تغيير حجم Canvas"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_frame, width=canvas_width)
    
    def on_mousewheel(self, event):
        """معالجة التمرير بالماوس"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def on_page_change(self, page_id):
        """معالجة تغيير الصفحة"""
        # تحديث النشاط
        self.auth_manager.update_activity()
        
        # التحقق من الصلاحيات
        if not self.auth_manager.has_permission(page_id):
            messagebox.showerror("خطأ", "ليس لديك صلاحية للوصول إلى هذه الصفحة")
            return
        
        self.current_page = page_id
        self.update_page_header(page_id)
        self.show_page_content(page_id)
    
    def update_page_header(self, page_id):
        """تحديث رأس الصفحة"""
        if page_id in self.page_titles:
            title, icon = self.page_titles[page_id]
            self.header.update_page_info(title, icon)
    
    def show_page_content(self, page_id):
        """عرض محتوى الصفحة"""
        # إخفاء جميع الصفحات
        for page_frame in self.pages.values():
            page_frame.pack_forget()
        
        # عرض الصفحة المطلوبة
        if page_id in self.pages:
            self.pages[page_id].pack(fill=tk.BOTH, expand=True, padx=24, pady=24)
            
            # تحديث البيانات حسب الصفحة
            if page_id == "dashboard":
                self.load_dashboard_data()
    
    def create_pages(self):
        """إنشاء صفحات المحتوى"""
        self.pages = {}
        
        # إنشاء الصفحات
        self.pages["dashboard"] = self.create_modern_dashboard()
        
        # عرض لوحة التحكم افتراضياً
        self.show_page_content("dashboard")
    
    def on_closing(self):
        """معالجة إغلاق التطبيق"""
        if messagebox.askyesno("تأكيد", "هل تريد الخروج من التطبيق؟"):
            self.auth_manager.logout()
            self.root.quit()
            sys.exit()
    
    def create_modern_dashboard(self):
        """إنشاء لوحة التحكم العصرية الكاملة"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        user_name = self.auth_manager.current_user['full_name'] if self.auth_manager.current_user else "المستخدم"
        page_header = ModernPageHeader(
            page_frame,
            title=f"مرحباً {user_name}",
            subtitle="نظرة شاملة على أداء محلك وإحصائيات اليوم",
            actions=[
                {
                    'text': '🔄 تحديث البيانات',
                    'command': self.load_dashboard_data,
                    'variant': 'secondary'
                },
                {
                    'text': '🚪 تسجيل خروج',
                    'command': self.logout,
                    'variant': 'danger'
                }
            ]
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # شبكة البطاقات الإحصائية
        stats_grid = tk.Frame(
            page_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        stats_grid.pack(fill=tk.X, pady=(0, 24))

        # الصف الأول من البطاقات
        row1_frame = tk.Frame(
            stats_grid,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        row1_frame.pack(fill=tk.X, pady=(0, 16))

        # بطاقة المبيعات اليومية
        self.daily_sales_card = self.create_stat_card(
            row1_frame,
            "💰",
            "المبيعات اليوم",
            "0.00 ₪",
            self.styles.colors['success'],
            "لا توجد مبيعات اليوم"
        )
        self.daily_sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # بطاقة الصيانات الجارية
        self.active_repairs_card = self.create_stat_card(
            row1_frame,
            "🔧",
            "صيانات جارية",
            "0",
            self.styles.colors['info'],
            "لا توجد صيانات جارية"
        )
        self.active_repairs_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=4)

        # بطاقة تنبيهات المخزون
        self.low_stock_card = self.create_stat_card(
            row1_frame,
            "📦",
            "تنبيهات المخزون",
            "0",
            self.styles.colors['warning'],
            "المخزون جيد"
        )
        self.low_stock_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # الصف الثاني من البطاقات
        row2_frame = tk.Frame(
            stats_grid,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        row2_frame.pack(fill=tk.X)

        # بطاقة إجمالي العملاء
        self.total_customers_card = self.create_stat_card(
            row2_frame,
            "👥",
            "إجمالي العملاء",
            "0",
            self.styles.colors['accent'],
            "عملاء مسجلين"
        )
        self.total_customers_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # بطاقة إجمالي المنتجات
        self.total_products_card = self.create_stat_card(
            row2_frame,
            "📱",
            "إجمالي المنتجات",
            "0",
            self.styles.colors['primary'],
            "منتج في المخزون"
        )
        self.total_products_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=4)

        # بطاقة الأرباح الشهرية
        self.monthly_profit_card = self.create_stat_card(
            row2_frame,
            "📈",
            "الأرباح الشهرية",
            "0.00 ₪",
            self.styles.colors['success'],
            "صافي الأرباح"
        )
        self.monthly_profit_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # قسم الجداول والبيانات
        tables_section = tk.Frame(
            page_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        tables_section.pack(fill=tk.BOTH, expand=True)

        # الصيانات الأخيرة
        repairs_card = ModernCard(tables_section, title="الصيانات الأخيرة")
        repairs_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 12))

        self.create_recent_repairs_table(repairs_card.get_content_frame())

        # المبيعات الأخيرة
        sales_card = ModernCard(tables_section, title="المبيعات الأخيرة")
        sales_card.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(12, 0))

        self.create_recent_sales_table(sales_card.get_content_frame())

        return page_frame

    def create_stat_card(self, parent, icon, title, value, color, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(
            parent,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )

        # إطار المحتوى
        content_frame = tk.Frame(
            card,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الصف العلوي - الأيقونة والقيمة
        top_frame = tk.Frame(
            content_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        top_frame.pack(fill=tk.X, pady=(0, 12))

        # الأيقونة مع خلفية ملونة
        icon_bg = tk.Frame(
            top_frame,
            bg=color,
            width=48,
            height=48,
            relief='flat',
            bd=0
        )
        icon_bg.pack(side=tk.LEFT)
        icon_bg.pack_propagate(False)

        icon_label = tk.Label(
            icon_bg,
            text=icon,
            bg=color,
            fg=self.styles.colors['text_white'],
            font=('Segoe UI Emoji', 18),
            anchor='center'
        )
        icon_label.pack(expand=True)

        # القيمة
        value_label = tk.Label(
            top_frame,
            text=value,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=('Segoe UI', 24, 'bold'),
            anchor='e'
        )
        value_label.pack(side=tk.RIGHT)

        # العنوان
        title_label = tk.Label(
            content_frame,
            text=title,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small'],
            anchor='w'
        )
        title_label.pack(fill=tk.X, pady=(0, 4))

        # العنوان الفرعي
        subtitle_label = tk.Label(
            content_frame,
            text=subtitle,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['caption'],
            anchor='w'
        )
        subtitle_label.pack(fill=tk.X)

        # حفظ مراجع للتحديث
        card.value_label = value_label
        card.subtitle_label = subtitle_label

        return card

    def logout(self):
        """تسجيل خروج المستخدم"""
        if messagebox.askyesno("تأكيد", "هل تريد تسجيل الخروج؟"):
            self.auth_manager.logout()
            self.root.withdraw()
            self.show_login()

    def create_recent_repairs_table(self, parent):
        """إنشاء جدول الصيانات الأخيرة"""
        # إطار الجدول
        table_frame = tk.Frame(
            parent,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        table_frame.pack(fill=tk.BOTH, expand=True)

        # جدول الصيانات
        columns = ("العميل", "نوع الجهاز", "المشكلة", "الحالة")
        self.repairs_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=8,
            style='Modern.Treeview'
        )

        for col in columns:
            self.repairs_tree.heading(col, text=col)
            self.repairs_tree.column(col, width=120)

        # شريط التمرير للجدول
        repairs_scrollbar = ttk.Scrollbar(
            table_frame,
            orient=tk.VERTICAL,
            command=self.repairs_tree.yview,
            style='Modern.Vertical.TScrollbar'
        )
        self.repairs_tree.configure(yscrollcommand=repairs_scrollbar.set)

        self.repairs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        repairs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_recent_sales_table(self, parent):
        """إنشاء جدول المبيعات الأخيرة"""
        # إطار الجدول
        table_frame = tk.Frame(
            parent,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        table_frame.pack(fill=tk.BOTH, expand=True)

        # جدول المبيعات
        columns = ("العميل", "المنتج", "الكمية", "المبلغ")
        self.sales_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=8,
            style='Modern.Treeview'
        )

        for col in columns:
            self.sales_tree.heading(col, text=col)
            self.sales_tree.column(col, width=120)

        # شريط التمرير للجدول
        sales_scrollbar = ttk.Scrollbar(
            table_frame,
            orient=tk.VERTICAL,
            command=self.sales_tree.yview,
            style='Modern.Vertical.TScrollbar'
        )
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)

        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            if not self.db_manager.connect():
                return

            conn = self.db_manager.connect()
            cursor = conn.cursor()

            # المبيعات اليومية
            cursor.execute('''
                SELECT COALESCE(SUM(total_amount), 0)
                FROM sales
                WHERE DATE(sale_date) = DATE('now')
            ''')
            daily_sales = cursor.fetchone()[0]
            self.update_stat_card(self.daily_sales_card, f"{daily_sales:.2f} ₪",
                                f"مبيعات اليوم")

            # الصيانات الجارية
            cursor.execute('''
                SELECT COUNT(*)
                FROM repairs
                WHERE status NOT IN ('مكتمل', 'ملغي')
            ''')
            active_repairs = cursor.fetchone()[0]
            self.update_stat_card(self.active_repairs_card, str(active_repairs),
                                f"{active_repairs} صيانة جارية")

            # تنبيهات المخزون
            cursor.execute('''
                SELECT COUNT(*)
                FROM inventory
                WHERE quantity <= min_quantity
            ''')
            low_stock = cursor.fetchone()[0]
            self.update_stat_card(self.low_stock_card, str(low_stock),
                                f"{'تحتاج إعادة تموين' if low_stock > 0 else 'المخزون جيد'}")

            # إجمالي العملاء
            cursor.execute('SELECT COUNT(*) FROM customers')
            total_customers = cursor.fetchone()[0]
            self.update_stat_card(self.total_customers_card, str(total_customers),
                                f"{total_customers} عميل مسجل")

            # إجمالي المنتجات
            cursor.execute('SELECT COUNT(*) FROM inventory')
            total_products = cursor.fetchone()[0]
            self.update_stat_card(self.total_products_card, str(total_products),
                                f"{total_products} منتج في المخزون")

            # الأرباح الشهرية
            cursor.execute('''
                SELECT COALESCE(SUM(total_amount), 0)
                FROM sales
                WHERE strftime('%Y-%m', sale_date) = strftime('%Y-%m', 'now')
            ''')
            monthly_sales = cursor.fetchone()[0]
            self.update_stat_card(self.monthly_profit_card, f"{monthly_sales:.2f} ₪",
                                f"مبيعات هذا الشهر")

            # تحديث جدول الصيانات الأخيرة
            self.load_recent_repairs()

            # تحديث جدول المبيعات الأخيرة
            self.load_recent_sales()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def load_recent_repairs(self):
        """تحميل الصيانات الأخيرة"""
        try:
            # مسح البيانات الحالية
            for item in self.repairs_tree.get_children():
                self.repairs_tree.delete(item)

            conn = self.db_manager.connect()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT c.name, r.device_type, r.issue_description, r.status
                FROM repairs r
                JOIN customers c ON r.customer_id = c.id
                ORDER BY r.repair_date DESC
                LIMIT 10
            ''')

            repairs = cursor.fetchall()

            for repair in repairs:
                self.repairs_tree.insert('', 'end', values=repair)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الصيانات الأخيرة: {e}")

    def load_recent_sales(self):
        """تحميل المبيعات الأخيرة"""
        try:
            # مسح البيانات الحالية
            for item in self.sales_tree.get_children():
                self.sales_tree.delete(item)

            conn = self.db_manager.connect()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT c.name, i.name, s.quantity, s.total_amount
                FROM sales s
                JOIN customers c ON s.customer_id = c.id
                JOIN inventory i ON s.item_id = i.id
                ORDER BY s.sale_date DESC
                LIMIT 10
            ''')

            sales = cursor.fetchall()

            for sale in sales:
                customer, product, quantity, amount = sale
                self.sales_tree.insert('', 'end', values=(customer, product, quantity, f"{amount:.2f} ₪"))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل المبيعات الأخيرة: {e}")

    def update_stat_card(self, card, value, subtitle):
        """تحديث بطاقة إحصائية"""
        card.value_label.configure(text=str(value))
        card.subtitle_label.configure(text=subtitle)

    def run(self):
        """تشغيل التطبيق"""
        try:
            print("🚀 تشغيل Phone Doctor الكامل...")
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")

def main():
    """الدالة الرئيسية"""
    try:
        print("🎨 Phone Doctor - النسخة الكاملة مع جميع الميزات")
        print("=" * 60)
        app = PhoneDoctorCompleteApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")

if __name__ == "__main__":
    main()
