#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_styles():
    """اختبار الأنماط العصرية"""
    try:
        from ui.modern_styles import ModernStyles
        styles = ModernStyles()
        print("✅ تم تحميل ModernStyles")
        return True
    except Exception as e:
        print(f"❌ خطأ في ModernStyles: {e}")
        return False

def test_modern_sidebar():
    """اختبار الشريط الجانبي"""
    try:
        from ui.modern_sidebar import ModernSidebar
        print("✅ تم تحميل ModernSidebar")
        return True
    except Exception as e:
        print(f"❌ خطأ في ModernSidebar: {e}")
        return False

def test_modern_header():
    """اختبار رأس الصفحة"""
    try:
        from ui.modern_header import ModernHeader
        print("✅ تم تحميل ModernHeader")
        return True
    except Exception as e:
        print(f"❌ خطأ في ModernHeader: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        from database.db_manager import DatabaseManager
        db = DatabaseManager()
        print("✅ تم تحميل DatabaseManager")
        return True
    except Exception as e:
        print(f"❌ خطأ في DatabaseManager: {e}")
        return False

def create_simple_modern_window():
    """إنشاء نافذة عصرية بسيطة للاختبار"""
    root = tk.Tk()
    root.title("Phone Doctor - اختبار التصميم العصري")
    root.geometry("800x600")
    
    try:
        # تحميل الأنماط
        from ui.modern_styles import ModernStyles
        styles = ModernStyles()
        styles.configure_ttk_styles()
        
        # تكوين النافذة
        root.configure(bg=styles.colors['bg_primary'])
        
        # إطار رئيسي
        main_frame = tk.Frame(
            root,
            bg=styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(
            main_frame,
            text="🎨 اختبار التصميم العصري",
            bg=styles.colors['bg_primary'],
            fg=styles.colors['text_primary'],
            font=styles.fonts['heading_large']
        )
        title_label.pack(pady=(0, 20))
        
        # بطاقة اختبار
        card_frame = tk.Frame(
            main_frame,
            bg=styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=styles.colors['border_light'],
            highlightthickness=1
        )
        card_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # محتوى البطاقة
        content_frame = tk.Frame(
            card_frame,
            bg=styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # نص الاختبار
        test_label = tk.Label(
            content_frame,
            text="✅ التصميم العصري يعمل بشكل صحيح!\n\n🎨 الألوان والخطوط محملة\n📱 الواجهة جاهزة للاستخدام",
            bg=styles.colors['bg_card'],
            fg=styles.colors['text_primary'],
            font=styles.fonts['body_large'],
            justify=tk.CENTER
        )
        test_label.pack(expand=True)
        
        # أزرار اختبار
        buttons_frame = tk.Frame(
            content_frame,
            bg=styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # زر أساسي
        primary_btn = tk.Button(
            buttons_frame,
            text="زر أساسي",
            **styles.get_button_style('primary')
        )
        primary_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر ثانوي
        secondary_btn = tk.Button(
            buttons_frame,
            text="زر ثانوي",
            **styles.get_button_style('secondary')
        )
        secondary_btn.pack(side=tk.LEFT, padx=5)
        
        # زر نجاح
        success_btn = tk.Button(
            buttons_frame,
            text="زر نجاح",
            **styles.get_button_style('success')
        )
        success_btn.pack(side=tk.LEFT, padx=5)
        
        # معلومات المطور
        dev_label = tk.Label(
            main_frame,
            text="المطور: محمد الشوامرة - 📞 0566000140",
            bg=styles.colors['bg_primary'],
            fg=styles.colors['text_muted'],
            font=styles.fonts['caption']
        )
        dev_label.pack(pady=(20, 0))
        
        print("✅ تم إنشاء النافذة العصرية بنجاح!")
        return root
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        # إنشاء نافذة بسيطة في حالة الخطأ
        label = tk.Label(root, text=f"خطأ في التصميم العصري:\n{e}")
        label.pack(expand=True)
        return root

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مكونات Phone Doctor العصرية")
    print("=" * 50)
    
    # اختبار المكونات
    tests = [
        ("الأنماط العصرية", test_modern_styles),
        ("الشريط الجانبي", test_modern_sidebar),
        ("رأس الصفحة", test_modern_header),
        ("قاعدة البيانات", test_database),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"🔍 اختبار {test_name}...")
        if test_func():
            passed += 1
    
    print(f"\n📊 النتائج: {passed}/{len(tests)} اختبارات نجحت")
    
    if passed == len(tests):
        print("🎉 جميع الاختبارات نجحت! إنشاء نافذة عصرية...")
        root = create_simple_modern_window()
        root.mainloop()
    else:
        print("❌ بعض الاختبارات فشلت")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
