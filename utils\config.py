#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق - Configuration Manager
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import json
import os
from typing import Dict, Any

class Config:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.settings = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        default_config = {
            "app": {
                "name": "Phone Doctor",
                "version": "1.0.0",
                "developer": "محمد الشوامرة",
                "phone": "0566000140"
            },
            "ui": {
                "theme": "blue",
                "dark_mode": False,
                "language": "ar",
                "font_size": 10,
                "window_size": [1200, 800]
            },
            "shop": {
                "name": "محل صيانة الهواتف",
                "address": "",
                "phone": "",
                "email": ""
            },
            "database": {
                "path": "phone_doctor.db",
                "backup_interval": 24  # hours
            },
            "themes": {
                "blue": {
                    "primary": "#2196F3",
                    "secondary": "#1976D2",
                    "accent": "#03DAC6",
                    "background": "#FAFAFA",
                    "surface": "#FFFFFF",
                    "text": "#212121"
                },
                "green": {
                    "primary": "#4CAF50",
                    "secondary": "#388E3C",
                    "accent": "#FF5722",
                    "background": "#F1F8E9",
                    "surface": "#FFFFFF",
                    "text": "#1B5E20"
                },
                "purple": {
                    "primary": "#9C27B0",
                    "secondary": "#7B1FA2",
                    "accent": "#FF9800",
                    "background": "#F3E5F5",
                    "surface": "#FFFFFF",
                    "text": "#4A148C"
                },
                "orange": {
                    "primary": "#FF9800",
                    "secondary": "#F57C00",
                    "accent": "#2196F3",
                    "background": "#FFF3E0",
                    "surface": "#FFFFFF",
                    "text": "#E65100"
                },
                "dark": {
                    "primary": "#212121",
                    "secondary": "#424242",
                    "accent": "#BB86FC",
                    "background": "#121212",
                    "surface": "#1E1E1E",
                    "text": "#FFFFFF"
                },
                "cyan": {
                    "primary": "#00BCD4",
                    "secondary": "#0097A7",
                    "accent": "#FF5722",
                    "background": "#E0F2F1",
                    "surface": "#FFFFFF",
                    "text": "#006064"
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # دمج الإعدادات المحملة مع الافتراضية
                    self.merge_configs(default_config, loaded_config)
                    return default_config
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")
                return default_config
        else:
            # إنشاء ملف الإعدادات الافتراضي
            self.save_config(default_config)
            return default_config
    
    def merge_configs(self, default: Dict, loaded: Dict):
        """دمج الإعدادات المحملة مع الافتراضية"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self.merge_configs(default[key], value)
                else:
                    default[key] = value
    
    def save_config(self, config: Dict[str, Any] = None):
        """حفظ الإعدادات في الملف"""
        try:
            config_to_save = config or self.settings
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key: str, default=None):
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.settings
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        current = self.settings
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
        self.save_config()
    
    def get_theme_colors(self, theme_name: str = None) -> Dict[str, str]:
        """الحصول على ألوان الثيم"""
        if not theme_name:
            theme_name = self.get('ui.theme', 'blue')
        
        return self.get(f'themes.{theme_name}', self.get('themes.blue'))
    
    def get_available_themes(self) -> list:
        """الحصول على قائمة الثيمات المتاحة"""
        return list(self.get('themes', {}).keys())
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        if os.path.exists(self.config_file):
            os.remove(self.config_file)
        self.settings = self.load_config()
