#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدخال أوامر الصيانة
New Repair Order Window

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime
import sys
import os

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.modern_styles import ModernStyles

class NewRepairWindow:
    """نافذة إدخال أوامر الصيانة العصرية"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.styles = ModernStyles()
        
        self.window = tk.Toplevel(parent)
        self.window.title("إدخال أمر صيانة جديد - Phone Doctor")
        self.window.geometry("700x800")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        self.setup_ui()
        self.load_customers()
        
        # التركيز على حقل العميل
        self.customer_combo.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة إدخال أوامر الصيانة"""
        # تكوين النافذة
        self.window.configure(bg=self.styles.colors['bg_primary'])
        
        # الإطار الرئيسي مع التمرير
        canvas = tk.Canvas(
            self.window,
            bg=self.styles.colors['bg_primary'],
            highlightthickness=0
        )
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.styles.colors['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المحتوى الرئيسي
        main_frame = tk.Frame(
            scrollable_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رأس النافذة
        header_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(
            header_frame,
            text="🔧 إدخال أمر صيانة جديد",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            font=('Segoe UI', 20, 'bold')
        )
        title_label.pack()
        
        # بيانات العميل
        customer_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        customer_frame.pack(fill=tk.X, pady=(0, 15))
        
        customer_content = tk.Frame(
            customer_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        customer_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(
            customer_content,
            text="👤 بيانات العميل",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small']
        ).pack(anchor='w', pady=(0, 10))
        
        # اختيار العميل
        customer_select_frame = tk.Frame(
            customer_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        customer_select_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            customer_select_frame,
            text="العميل:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.customer_combo = ttk.Combobox(
            customer_select_frame,
            font=self.styles.fonts['body_medium'],
            style='Modern.TCombobox',
            state='readonly'
        )
        self.customer_combo.pack(fill=tk.X)
        
        # بيانات الجهاز
        device_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        device_frame.pack(fill=tk.X, pady=(0, 15))
        
        device_content = tk.Frame(
            device_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        device_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(
            device_content,
            text="📱 بيانات الجهاز",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small']
        ).pack(anchor='w', pady=(0, 10))
        
        # نوع الجهاز
        device_type_frame = tk.Frame(
            device_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        device_type_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            device_type_frame,
            text="نوع الجهاز:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.device_type_combo = ttk.Combobox(
            device_type_frame,
            font=self.styles.fonts['body_medium'],
            style='Modern.TCombobox',
            values=[
                "iPhone 14 Pro", "iPhone 14", "iPhone 13 Pro", "iPhone 13", "iPhone 12",
                "Samsung Galaxy S23", "Samsung Galaxy S22", "Samsung Galaxy A54",
                "Huawei P50", "Xiaomi 13", "OnePlus 11", "Google Pixel 7",
                "Oppo Find X5", "Vivo X90", "Realme GT3", "Nothing Phone 2"
            ]
        )
        self.device_type_combo.pack(fill=tk.X)
        
        # الرقم التسلسلي
        serial_frame = tk.Frame(
            device_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        serial_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            serial_frame,
            text="الرقم التسلسلي (اختياري):",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.serial_entry = tk.Entry(
            serial_frame,
            **self.styles.get_entry_style(),
            font=self.styles.fonts['body_medium']
        )
        self.serial_entry.pack(fill=tk.X)
        
        # وصف المشكلة
        problem_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        problem_frame.pack(fill=tk.X, pady=(0, 15))
        
        problem_content = tk.Frame(
            problem_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        problem_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(
            problem_content,
            text="🔍 وصف المشكلة",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small']
        ).pack(anchor='w', pady=(0, 10))
        
        # المشاكل الشائعة
        common_problems_frame = tk.Frame(
            problem_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        common_problems_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            common_problems_frame,
            text="المشاكل الشائعة:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.problem_combo = ttk.Combobox(
            common_problems_frame,
            font=self.styles.fonts['body_medium'],
            style='Modern.TCombobox',
            values=[
                "شاشة مكسورة", "بطارية تالفة", "مشكلة في الشحن", "مشكلة في الصوت",
                "كاميرا لا تعمل", "مشكلة في اللمس", "جهاز لا يشتغل", "مشكلة في الواي فاي",
                "مشكلة في البلوتوث", "بطء في الأداء", "مشكلة في السماعة", "مشكلة في الميكروفون",
                "مشكلة في الأزرار", "مشكلة في الكاميرا الأمامية", "مشكلة في الفلاش"
            ]
        )
        self.problem_combo.pack(fill=tk.X)
        
        # وصف تفصيلي
        description_frame = tk.Frame(
            problem_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        description_frame.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(
            description_frame,
            text="وصف تفصيلي للمشكلة:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.description_text = tk.Text(
            description_frame,
            height=4,
            font=self.styles.fonts['body_medium'],
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        self.description_text.pack(fill=tk.X)
        
        # التكلفة المتوقعة
        cost_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        cost_frame.pack(fill=tk.X, pady=(0, 15))
        
        cost_content = tk.Frame(
            cost_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        cost_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(
            cost_content,
            text="💰 التكلفة المتوقعة (اختياري):",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.cost_entry = tk.Entry(
            cost_content,
            **self.styles.get_entry_style(),
            font=self.styles.fonts['body_medium']
        )
        self.cost_entry.pack(fill=tk.X)
        
        # أزرار العمل
        buttons_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            **self.styles.get_button_style('secondary'),
            font=self.styles.fonts['body_medium']
        )
        cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ أمر الصيانة",
            command=self.save_repair,
            **self.styles.get_button_style('success'),
            font=self.styles.fonts['body_medium']
        )
        save_btn.pack(side=tk.RIGHT)

    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            conn = self.db_manager.connect()
            if not conn:
                return

            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM customers ORDER BY name")
            customers = cursor.fetchall()

            customer_list = [f"{customer[0]} - {customer[1]}" for customer in customers]
            self.customer_combo['values'] = customer_list

            if customer_list:
                self.customer_combo.current(0)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل العملاء: {e}")

    def save_repair(self):
        """حفظ أمر الصيانة في قاعدة البيانات"""
        # التحقق من البيانات المطلوبة
        if not self.customer_combo.get():
            messagebox.showwarning("تحذير", "يرجى اختيار عميل")
            return

        if not self.device_type_combo.get():
            messagebox.showwarning("تحذير", "يرجى إدخال نوع الجهاز")
            return

        if not self.problem_combo.get() and not self.description_text.get("1.0", tk.END).strip():
            messagebox.showwarning("تحذير", "يرجى اختيار مشكلة شائعة أو إدخال وصف تفصيلي")
            return

        try:
            # الحصول على معرف العميل
            customer_text = self.customer_combo.get()
            customer_id = int(customer_text.split(' - ')[0])

            # جمع البيانات
            device_type = self.device_type_combo.get()
            serial_number = self.serial_entry.get().strip()

            # تجميع وصف المشكلة
            problem_description = ""
            if self.problem_combo.get():
                problem_description = self.problem_combo.get()

            detailed_description = self.description_text.get("1.0", tk.END).strip()
            if detailed_description:
                if problem_description:
                    problem_description += f"\n\nتفاصيل إضافية:\n{detailed_description}"
                else:
                    problem_description = detailed_description

            # التكلفة المتوقعة
            estimated_cost = 0.0
            cost_text = self.cost_entry.get().strip()
            if cost_text:
                try:
                    estimated_cost = float(cost_text)
                except ValueError:
                    messagebox.showwarning("تحذير", "التكلفة المتوقعة يجب أن تكون رقماً")
                    return

            conn = self.db_manager.connect()
            if not conn:
                return

            cursor = conn.cursor()

            # إدراج أمر الصيانة
            cursor.execute('''
                INSERT INTO repairs (
                    customer_id, device_type, serial_number, issue_description,
                    status, cost, repair_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                customer_id,
                device_type,
                serial_number if serial_number else None,
                problem_description,
                "جديد",  # الحالة الافتراضية
                estimated_cost,
                datetime.now()
            ))

            # الحصول على رقم أمر الصيانة
            repair_id = cursor.lastrowid

            # تسجيل النشاط
            if self.auth_manager.current_user:
                self.auth_manager.log_activity(
                    self.auth_manager.current_user['id'],
                    "إضافة أمر صيانة",
                    f"أمر صيانة رقم {repair_id} للعميل {customer_id} - {device_type}"
                )

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم حفظ أمر الصيانة بنجاح!\nرقم الأمر: {repair_id}")

            # مسح النموذج
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ أمر الصيانة: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.device_type_combo.set("")
        self.serial_entry.delete(0, tk.END)
        self.problem_combo.set("")
        self.description_text.delete("1.0", tk.END)
        self.cost_entry.delete(0, tk.END)
        self.customer_combo.focus()

    def show(self):
        """عرض النافذة"""
        self.window.grab_set()  # جعل النافذة modal
        self.window.wait_window()  # انتظار إغلاق النافذة
