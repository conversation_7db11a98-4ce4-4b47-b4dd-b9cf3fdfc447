#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0
Phone Doctor v2.0 Launcher

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        messagebox.showerror("خطأ", f"يتطلب Python 3.8 أو أحدث\nالإصدار الحالي: {sys.version}")
        return False
    
    # فحص المكتبات المطلوبة
    required_modules = ['tkinter', 'sqlite3', 'hashlib', 'datetime']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    # فحص مكتبة التشفير
    try:
        import cryptography
        print("✅ مكتبة التشفير متوفرة")
    except ImportError:
        print("⚠️ مكتبة التشفير غير متوفرة، جاري التثبيت...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'cryptography'])
            print("✅ تم تثبيت مكتبة التشفير بنجاح")
        except subprocess.CalledProcessError:
            messagebox.showerror("خطأ", "فشل في تثبيت مكتبة التشفير\nيرجى تثبيتها يدوياً: pip install cryptography")
            return False
    
    if missing_modules:
        messagebox.showerror("خطأ", f"المكتبات التالية مفقودة:\n{', '.join(missing_modules)}")
        return False
    
    return True

def show_splash_screen():
    """عرض شاشة البداية"""
    splash = tk.Tk()
    splash.title("Phone Doctor v2.0")
    splash.geometry("500x300")
    splash.resizable(False, False)
    splash.configure(bg='#6366f1')
    
    # توسيط النافذة
    splash.update_idletasks()
    width = splash.winfo_width()
    height = splash.winfo_height()
    x = (splash.winfo_screenwidth() // 2) - (width // 2)
    y = (splash.winfo_screenheight() // 2) - (height // 2)
    splash.geometry(f"{width}x{height}+{x}+{y}")
    
    # إزالة شريط العنوان
    splash.overrideredirect(True)
    
    # المحتوى
    content_frame = tk.Frame(splash, bg='#6366f1')
    content_frame.pack(expand=True, fill=tk.BOTH, padx=30, pady=30)
    
    # الأيقونة
    icon_label = tk.Label(
        content_frame,
        text="📱",
        bg='#6366f1',
        fg='white',
        font=('Arial', 48)
    )
    icon_label.pack(pady=(20, 10))
    
    # العنوان
    title_label = tk.Label(
        content_frame,
        text="Phone Doctor v2.0",
        bg='#6366f1',
        fg='white',
        font=('Arial', 24, 'bold')
    )
    title_label.pack()
    
    # الوصف
    desc_label = tk.Label(
        content_frame,
        text="نظام إدارة محلات صيانة الهواتف المتطور",
        bg='#6366f1',
        fg='#e2e8f0',
        font=('Arial', 12)
    )
    desc_label.pack(pady=(5, 20))
    
    # معلومات المطور
    dev_label = tk.Label(
        content_frame,
        text="المطور: محمد الشوامرة - 0566000140",
        bg='#6366f1',
        fg='#e2e8f0',
        font=('Arial', 11, 'bold')
    )
    dev_label.pack()
    
    # حقوق الطبع
    copyright_label = tk.Label(
        content_frame,
        text="© 2024 جميع الحقوق محفوظة",
        bg='#6366f1',
        fg='#cbd5e1',
        font=('Arial', 9)
    )
    copyright_label.pack(pady=(10, 0))
    
    # شريط التحميل
    progress_frame = tk.Frame(content_frame, bg='#6366f1')
    progress_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
    
    progress_bg = tk.Frame(progress_frame, bg='#4f46e5', height=4)
    progress_bg.pack(fill=tk.X)
    
    progress_bar = tk.Frame(progress_bg, bg='white', height=4)
    progress_bar.pack(side=tk.LEFT)
    
    # تحريك شريط التحميل
    def animate_progress():
        for i in range(101):
            progress_bar.configure(width=int(450 * i / 100))
            splash.update()
            splash.after(20)
    
    splash.after(500, animate_progress)
    splash.after(3000, splash.destroy)
    
    splash.mainloop()

def launch_phone_doctor():
    """تشغيل Phone Doctor"""
    try:
        print("🚀 تشغيل Phone Doctor v2.0...")
        
        # التحقق من وجود الملف الرئيسي
        main_file = "phone_doctor_working.py"
        if not os.path.exists(main_file):
            messagebox.showerror("خطأ", f"لم يتم العثور على الملف الرئيسي:\n{main_file}")
            return False
        
        # تشغيل البرنامج
        import phone_doctor_working
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل البرنامج:\n{str(e)}")
        return False

def main():
    """الدالة الرئيسية للمشغل"""
    print("=" * 50)
    print("🚀 Phone Doctor v2.0 Launcher")
    print("نظام إدارة محلات صيانة الهواتف المتطور")
    print("المطور: محمد الشوامرة - 0566000140")
    print("=" * 50)
    
    try:
        # فحص المتطلبات
        if not check_requirements():
            print("❌ فشل في فحص المتطلبات")
            input("اضغط Enter للخروج...")
            return
        
        print("✅ جميع المتطلبات متوفرة")
        
        # عرض شاشة البداية
        show_splash_screen()
        
        # تشغيل البرنامج
        if launch_phone_doctor():
            print("✅ تم تشغيل البرنامج بنجاح")
        else:
            print("❌ فشل في تشغيل البرنامج")
            input("اضغط Enter للخروج...")
            
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء التشغيل بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
