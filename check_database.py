#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص قاعدة البيانات
Database Check

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import os

def check_database():
    """فحص قاعدة البيانات"""
    db_path = 'database/phone_doctor.db'
    
    print("🔍 فحص قاعدة البيانات...")
    print(f"📁 مسار قاعدة البيانات: {db_path}")
    
    # التحقق من وجود الملف
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return
    
    print("✅ ملف قاعدة البيانات موجود")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📋 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # التحقق من جدول الصيانة
        if ('repairs',) in tables:
            print("\n🔧 جدول الصيانة موجود")
            
            # عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM repairs")
            count = cursor.fetchone()[0]
            print(f"📊 عدد أوامر الصيانة: {count}")
            
            if count > 0:
                # عرض أول 5 سجلات
                cursor.execute("SELECT id, customer_name, device_type, status FROM repairs LIMIT 5")
                repairs = cursor.fetchall()
                
                print("\n📝 أول 5 أوامر صيانة:")
                for repair in repairs:
                    print(f"  - ID: {repair[0]}, العميل: {repair[1]}, الجهاز: {repair[2]}, الحالة: {repair[3]}")
            else:
                print("⚠️ جدول الصيانة فارغ")
        else:
            print("❌ جدول الصيانة غير موجود!")
            
            # إنشاء جدول الصيانة
            print("🔨 إنشاء جدول الصيانة...")
            create_repairs_table(cursor)
            conn.commit()
            print("✅ تم إنشاء جدول الصيانة")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def create_repairs_table(cursor):
    """إنشاء جدول الصيانة"""
    repairs_table = '''
        CREATE TABLE IF NOT EXISTS repairs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            repair_number TEXT UNIQUE,
            customer_name TEXT NOT NULL,
            customer_phone TEXT NOT NULL,
            customer_email TEXT,
            customer_address TEXT,
            device_type TEXT NOT NULL,
            device_brand TEXT,
            device_model TEXT,
            device_color TEXT,
            device_imei TEXT,
            device_serial TEXT,
            problem_description TEXT NOT NULL,
            problem_category TEXT,
            status TEXT DEFAULT 'pending',
            priority TEXT DEFAULT 'normal',
            technician_name TEXT,
            assigned_date TIMESTAMP,
            estimated_cost REAL DEFAULT 0,
            actual_cost REAL DEFAULT 0,
            parts_cost REAL DEFAULT 0,
            labor_cost REAL DEFAULT 0,
            total_cost REAL DEFAULT 0,
            parts_needed TEXT,
            parts_used TEXT,
            warranty_period INTEGER DEFAULT 0,
            warranty_start_date TIMESTAMP,
            warranty_end_date TIMESTAMP,
            technician_notes TEXT,
            repair_notes TEXT,
            customer_notes TEXT,
            internal_notes TEXT,
            images_path TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_date TIMESTAMP,
            delivered_date TIMESTAMP,
            created_by TEXT,
            updated_by TEXT
        )
    '''
    cursor.execute(repairs_table)

if __name__ == "__main__":
    check_database()
