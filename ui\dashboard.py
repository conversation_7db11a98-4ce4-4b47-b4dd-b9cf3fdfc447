#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحكم - Dashboard
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QFrame, QPushButton, QScrollArea, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient

class StatCard(QFrame):
    clicked = pyqtSignal(str)
    
    def __init__(self, title, value, icon, color, action_name=""):
        super().__init__()
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.action_name = action_name
        
        self.setFixedSize(200, 120)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setCursor(Qt.PointingHandCursor)
        
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة البطاقة"""
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # الصف العلوي - الأيقونة والقيمة
        top_layout = QHBoxLayout()
        
        # الأيقونة
        icon_label = QLabel(self.icon)
        icon_label.setFont(QFont("Arial", 24))
        icon_label.setAlignment(Qt.AlignLeft)
        
        # القيمة
        value_label = QLabel(str(self.value))
        value_label.setFont(QFont("Arial", 20, QFont.Bold))
        value_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 11))
        title_label.setAlignment(Qt.AlignLeft)
        title_label.setWordWrap(True)
        
        layout.addLayout(top_layout)
        layout.addWidget(title_label)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def mousePressEvent(self, event):
        """معالجة النقر على البطاقة"""
        if self.action_name:
            self.clicked.emit(self.action_name)
        super().mousePressEvent(event)
        
    def paintEvent(self, event):
        """رسم خلفية البطاقة بتدرج لوني"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إنشاء تدرج لوني
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(self.color))
        gradient.setColorAt(1, QColor(self.color).darker(120))
        
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(self.rect(), 10, 10)

class QuickActionButton(QPushButton):
    def __init__(self, text, icon, action_name):
        super().__init__()
        self.action_name = action_name
        self.setText(f"{icon} {text}")
        self.setFixedSize(150, 40)
        self.setCursor(Qt.PointingHandCursor)

class Dashboard(QWidget):
    action_requested = pyqtSignal(str)
    
    def __init__(self, db_manager, config):
        super().__init__()
        self.db_manager = db_manager
        self.config = config
        self.current_theme = self.config.get('ui.theme', 'blue')
        
        self.init_ui()
        self.setup_timer()
        self.apply_theme()
        self.refresh_data()
        
    def init_ui(self):
        """إعداد واجهة لوحة التحكم"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # العنوان الرئيسي
        title_layout = QHBoxLayout()
        
        title_label = QLabel("لوحة التحكم")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        
        # الوقت والتاريخ
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 12))
        self.datetime_label.setAlignment(Qt.AlignRight)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.datetime_label)
        
        main_layout.addLayout(title_layout)
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        # إنشاء البطاقات
        self.stat_cards = []
        
        # بطاقة المبيعات اليومية
        sales_card = StatCard("المبيعات اليوم", "0 ₪", "💰", "#4CAF50", "sales")
        self.stat_cards.append(sales_card)
        stats_layout.addWidget(sales_card, 0, 0)
        
        # بطاقة الصيانات الجارية
        repairs_card = StatCard("صيانات جارية", "0", "🔧", "#2196F3", "repairs")
        self.stat_cards.append(repairs_card)
        stats_layout.addWidget(repairs_card, 0, 1)
        
        # بطاقة المخزون المنخفض
        inventory_card = StatCard("تنبيهات المخزون", "0", "📦", "#FF9800", "inventory")
        self.stat_cards.append(inventory_card)
        stats_layout.addWidget(inventory_card, 0, 2)
        
        # بطاقة الشيكات المستحقة
        checks_card = StatCard("شيكات مستحقة", "0", "💳", "#F44336", "financial")
        self.stat_cards.append(checks_card)
        stats_layout.addWidget(checks_card, 1, 0)
        
        # بطاقة العملاء الجدد
        customers_card = StatCard("عملاء جدد", "0", "👥", "#9C27B0", "customers")
        self.stat_cards.append(customers_card)
        stats_layout.addWidget(customers_card, 1, 1)
        
        # بطاقة إجمالي الأرباح
        profit_card = StatCard("الأرباح الشهرية", "0 ₪", "📈", "#607D8B", "reports")
        self.stat_cards.append(profit_card)
        stats_layout.addWidget(profit_card, 1, 2)
        
        main_layout.addLayout(stats_layout)
        
        # الأعمال السريعة
        quick_actions_frame = QFrame()
        quick_actions_frame.setFrameStyle(QFrame.StyledPanel)
        
        quick_layout = QVBoxLayout(quick_actions_frame)
        quick_layout.setContentsMargins(15, 15, 15, 15)
        
        quick_title = QLabel("الأعمال السريعة")
        quick_title.setFont(QFont("Arial", 16, QFont.Bold))
        quick_layout.addWidget(quick_title)
        
        # أزرار الأعمال السريعة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        quick_buttons = [
            ("إضافة جهاز للصيانة", "🔧", "new_repair"),
            ("بيع قطعة", "💰", "new_sale"),
            ("إضافة قطعة للمخزون", "📦", "new_item"),
            ("إضافة مورد", "🏪", "new_supplier"),
            ("عرض التقارير", "📊", "reports")
        ]
        
        for text, icon, action in quick_buttons:
            button = QuickActionButton(text, icon, action)
            button.clicked.connect(lambda checked, a=action: self.action_requested.emit(a))
            buttons_layout.addWidget(button)
        
        buttons_layout.addStretch()
        quick_layout.addLayout(buttons_layout)
        
        main_layout.addWidget(quick_actions_frame)
        
        # الأنشطة الأخيرة
        activities_frame = QFrame()
        activities_frame.setFrameStyle(QFrame.StyledPanel)
        
        activities_layout = QVBoxLayout(activities_frame)
        activities_layout.setContentsMargins(15, 15, 15, 15)
        
        activities_title = QLabel("الأنشطة الأخيرة")
        activities_title.setFont(QFont("Arial", 16, QFont.Bold))
        activities_layout.addWidget(activities_title)
        
        # قائمة الأنشطة
        self.activities_scroll = QScrollArea()
        self.activities_scroll.setWidgetResizable(True)
        self.activities_scroll.setMaximumHeight(200)
        
        self.activities_widget = QWidget()
        self.activities_layout = QVBoxLayout(self.activities_widget)
        self.activities_scroll.setWidget(self.activities_widget)
        
        activities_layout.addWidget(self.activities_scroll)
        main_layout.addWidget(activities_frame)
        
        main_layout.addStretch()
        self.setLayout(main_layout)
        
        # ربط إشارات البطاقات
        for card in self.stat_cards:
            card.clicked.connect(self.action_requested.emit)
    
    def setup_timer(self):
        """إعداد مؤقت تحديث البيانات"""
        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_datetime)
        self.time_timer.start(1000)  # كل ثانية
        
        # مؤقت تحديث البيانات
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.refresh_data)
        self.data_timer.start(60000)  # كل دقيقة
        
        # تحديث الوقت فوراً
        self.update_datetime()
    
    def update_datetime(self):
        """تحديث الوقت والتاريخ"""
        from datetime import datetime
        now = datetime.now()
        
        # تنسيق التاريخ والوقت بالعربية
        weekdays = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
        
        weekday = weekdays[now.weekday()]
        month = months[now.month - 1]
        
        date_str = f"{weekday}, {now.day} {month} {now.year}"
        time_str = now.strftime("%H:%M:%S")
        
        self.datetime_label.setText(f"{date_str} - {time_str}")
    
    def refresh_data(self):
        """تحديث بيانات لوحة التحكم"""
        if not self.db_manager.connect():
            return
        
        try:
            # المبيعات اليومية
            today_sales = self.get_today_sales()
            self.stat_cards[0].value = f"{today_sales:.2f} ₪"
            
            # الصيانات الجارية
            active_repairs = self.get_active_repairs_count()
            self.stat_cards[1].value = str(active_repairs)
            
            # تنبيهات المخزون
            low_stock_count = self.get_low_stock_count()
            self.stat_cards[2].value = str(low_stock_count)
            
            # الشيكات المستحقة
            due_checks = self.get_due_checks_count()
            self.stat_cards[3].value = str(due_checks)
            
            # العملاء الجدد هذا الشهر
            new_customers = self.get_new_customers_count()
            self.stat_cards[4].value = str(new_customers)
            
            # الأرباح الشهرية
            monthly_profit = self.get_monthly_profit()
            self.stat_cards[5].value = f"{monthly_profit:.2f} ₪"
            
            # تحديث الأنشطة الأخيرة
            self.update_recent_activities()
            
        except Exception as e:
            print(f"خطأ في تحديث بيانات لوحة التحكم: {e}")
    
    def get_today_sales(self):
        """الحصول على مبيعات اليوم"""
        query = """
        SELECT COALESCE(SUM(total_amount), 0) as total
        FROM sales 
        WHERE DATE(sale_date) = DATE('now')
        """
        result = self.db_manager.fetch_query(query)
        return result[0]['total'] if result else 0
    
    def get_active_repairs_count(self):
        """الحصول على عدد الصيانات الجارية"""
        query = """
        SELECT COUNT(*) as count
        FROM repairs 
        WHERE repair_status IN ('قيد الانتظار', 'جاري الإصلاح')
        """
        result = self.db_manager.fetch_query(query)
        return result[0]['count'] if result else 0
    
    def get_low_stock_count(self):
        """الحصول على عدد القطع منخفضة المخزون"""
        query = """
        SELECT COUNT(*) as count
        FROM inventory 
        WHERE quantity <= min_quantity
        """
        result = self.db_manager.fetch_query(query)
        return result[0]['count'] if result else 0
    
    def get_due_checks_count(self):
        """الحصول على عدد الشيكات المستحقة"""
        query = """
        SELECT COUNT(*) as count
        FROM checks 
        WHERE due_date <= DATE('now') AND status = 'معلق'
        """
        result = self.db_manager.fetch_query(query)
        return result[0]['count'] if result else 0
    
    def get_new_customers_count(self):
        """الحصول على عدد العملاء الجدد هذا الشهر"""
        query = """
        SELECT COUNT(*) as count
        FROM customers 
        WHERE DATE(created_at) >= DATE('now', 'start of month')
        """
        result = self.db_manager.fetch_query(query)
        return result[0]['count'] if result else 0
    
    def get_monthly_profit(self):
        """الحصول على الأرباح الشهرية"""
        query = """
        SELECT COALESCE(SUM(total_amount), 0) as total
        FROM sales 
        WHERE DATE(sale_date) >= DATE('now', 'start of month')
        """
        result = self.db_manager.fetch_query(query)
        return result[0]['total'] if result else 0
    
    def update_recent_activities(self):
        """تحديث الأنشطة الأخيرة"""
        # مسح الأنشطة السابقة
        for i in reversed(range(self.activities_layout.count())):
            self.activities_layout.itemAt(i).widget().setParent(None)
        
        # الحصول على الأنشطة الأخيرة
        activities = self.get_recent_activities()
        
        if not activities:
            no_activity_label = QLabel("لا توجد أنشطة حديثة")
            no_activity_label.setAlignment(Qt.AlignCenter)
            no_activity_label.setStyleSheet("color: #666; font-style: italic;")
            self.activities_layout.addWidget(no_activity_label)
        else:
            for activity in activities:
                activity_label = QLabel(activity)
                activity_label.setWordWrap(True)
                activity_label.setStyleSheet("padding: 5px; border-bottom: 1px solid #eee;")
                self.activities_layout.addWidget(activity_label)
    
    def get_recent_activities(self):
        """الحصول على الأنشطة الأخيرة"""
        activities = []
        
        # أحدث المبيعات
        sales_query = """
        SELECT 'بيع: ' || COALESCE(i.item_name, 'قطعة') || ' - ' || s.total_amount || ' ₪' as activity,
               s.created_at
        FROM sales s
        LEFT JOIN inventory i ON s.item_id = i.id
        ORDER BY s.created_at DESC
        LIMIT 3
        """
        sales_results = self.db_manager.fetch_query(sales_query)
        activities.extend([row['activity'] for row in sales_results])
        
        # أحدث الصيانات
        repairs_query = """
        SELECT 'صيانة: ' || device_type || ' - ' || problem_description as activity,
               created_at
        FROM repairs
        ORDER BY created_at DESC
        LIMIT 3
        """
        repairs_results = self.db_manager.fetch_query(repairs_query)
        activities.extend([row['activity'] for row in repairs_results])
        
        return activities[:5]  # أحدث 5 أنشطة
    
    def apply_theme(self):
        """تطبيق الثيم الحالي"""
        colors = self.config.get_theme_colors(self.current_theme)
        
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {colors['background']};
                color: {colors['text']};
            }}
            QFrame {{
                background-color: {colors['surface']};
                border: 1px solid {colors['primary']};
                border-radius: 10px;
            }}
            QPushButton {{
                background-color: {colors['primary']};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {colors['secondary']};
            }}
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
        """)
