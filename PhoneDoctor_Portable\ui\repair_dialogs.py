#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ حوار الصيانة - Repair Dialogs
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date

class NewRepairDialog:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة جهاز جديد للصيانة")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل العملاء
        self.load_customers()
        
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إضافة جهاز جديد للصيانة", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إطار بيانات العميل
        customer_frame = ttk.LabelFrame(main_frame, text="بيانات العميل", padding=15)
        customer_frame.pack(fill=tk.X, pady=(0, 15))
        
        # اختيار العميل الحالي أو جديد
        self.customer_type = tk.StringVar(value="existing")
        
        ttk.Radiobutton(customer_frame, text="عميل موجود", 
                       variable=self.customer_type, value="existing",
                       command=self.toggle_customer_fields).grid(row=0, column=0, sticky="w", pady=5)
        
        ttk.Radiobutton(customer_frame, text="عميل جديد", 
                       variable=self.customer_type, value="new",
                       command=self.toggle_customer_fields).grid(row=0, column=1, sticky="w", pady=5)
        
        # قائمة العملاء الموجودين
        ttk.Label(customer_frame, text="العميل:").grid(row=1, column=0, sticky="w", pady=5)
        self.customer_combo = ttk.Combobox(customer_frame, width=30, state="readonly")
        self.customer_combo.grid(row=1, column=1, columnspan=2, sticky="ew", pady=5)
        
        # بيانات العميل الجديد
        ttk.Label(customer_frame, text="اسم العميل:").grid(row=2, column=0, sticky="w", pady=5)
        self.customer_name_var = tk.StringVar()
        self.customer_name_entry = ttk.Entry(customer_frame, textvariable=self.customer_name_var, width=30)
        self.customer_name_entry.grid(row=2, column=1, columnspan=2, sticky="ew", pady=5)
        
        ttk.Label(customer_frame, text="رقم الهاتف:").grid(row=3, column=0, sticky="w", pady=5)
        self.customer_phone_var = tk.StringVar()
        self.customer_phone_entry = ttk.Entry(customer_frame, textvariable=self.customer_phone_var, width=30)
        self.customer_phone_entry.grid(row=3, column=1, columnspan=2, sticky="ew", pady=5)
        
        ttk.Label(customer_frame, text="العنوان:").grid(row=4, column=0, sticky="w", pady=5)
        self.customer_address_var = tk.StringVar()
        self.customer_address_entry = ttk.Entry(customer_frame, textvariable=self.customer_address_var, width=30)
        self.customer_address_entry.grid(row=4, column=1, columnspan=2, sticky="ew", pady=5)
        
        # تكوين الأعمدة
        customer_frame.columnconfigure(1, weight=1)
        
        # إطار بيانات الجهاز
        device_frame = ttk.LabelFrame(main_frame, text="بيانات الجهاز", padding=15)
        device_frame.pack(fill=tk.X, pady=(0, 15))
        
        # نوع الجهاز
        ttk.Label(device_frame, text="نوع الجهاز:").grid(row=0, column=0, sticky="w", pady=5)
        self.device_type_var = tk.StringVar()
        device_type_combo = ttk.Combobox(device_frame, textvariable=self.device_type_var, width=30)
        device_type_combo['values'] = ('iPhone', 'Samsung', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 'OnePlus', 'أخرى')
        device_type_combo.grid(row=0, column=1, sticky="ew", pady=5)
        
        # موديل الجهاز
        ttk.Label(device_frame, text="الموديل:").grid(row=1, column=0, sticky="w", pady=5)
        self.device_model_var = tk.StringVar()
        ttk.Entry(device_frame, textvariable=self.device_model_var, width=30).grid(row=1, column=1, sticky="ew", pady=5)
        
        # رقم IMEI
        ttk.Label(device_frame, text="رقم IMEI:").grid(row=2, column=0, sticky="w", pady=5)
        self.device_imei_var = tk.StringVar()
        ttk.Entry(device_frame, textvariable=self.device_imei_var, width=30).grid(row=2, column=1, sticky="ew", pady=5)
        
        # وصف المشكلة
        ttk.Label(device_frame, text="وصف المشكلة:").grid(row=3, column=0, sticky="nw", pady=5)
        self.problem_text = tk.Text(device_frame, width=30, height=4)
        self.problem_text.grid(row=3, column=1, sticky="ew", pady=5)
        
        # التكلفة المتوقعة
        ttk.Label(device_frame, text="التكلفة المتوقعة (₪):").grid(row=4, column=0, sticky="w", pady=5)
        self.estimated_cost_var = tk.StringVar(value="0.00")
        ttk.Entry(device_frame, textvariable=self.estimated_cost_var, width=30).grid(row=4, column=1, sticky="ew", pady=5)
        
        # تكوين الأعمدة
        device_frame.columnconfigure(1, weight=1)
        
        # ملاحظات الفني
        notes_frame = ttk.LabelFrame(main_frame, text="ملاحظات الفني", padding=15)
        notes_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.technician_notes = tk.Text(notes_frame, width=50, height=3)
        self.technician_notes.pack(fill=tk.X)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="حفظ", 
                  command=self.save).pack(side=tk.RIGHT)
        
        # تعيين الحالة الافتراضية
        self.toggle_customer_fields()
    
    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            customers = self.db_manager.fetch_query("SELECT id, name, phone FROM customers ORDER BY name")
            customer_list = [f"{customer['name']} ({customer['phone']})" for customer in customers]
            self.customer_combo['values'] = customer_list
            self.customers_data = customers
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")
            self.customer_combo['values'] = []
            self.customers_data = []
    
    def toggle_customer_fields(self):
        """تبديل حقول العميل"""
        if self.customer_type.get() == "existing":
            # تفعيل قائمة العملاء
            self.customer_combo.config(state="readonly")
            # تعطيل حقول العميل الجديد
            self.customer_name_entry.config(state="disabled")
            self.customer_phone_entry.config(state="disabled")
            self.customer_address_entry.config(state="disabled")
        else:
            # تعطيل قائمة العملاء
            self.customer_combo.config(state="disabled")
            # تفعيل حقول العميل الجديد
            self.customer_name_entry.config(state="normal")
            self.customer_phone_entry.config(state="normal")
            self.customer_address_entry.config(state="normal")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من بيانات العميل
        if self.customer_type.get() == "existing":
            if not self.customer_combo.get():
                messagebox.showerror("خطأ", "يرجى اختيار عميل من القائمة!")
                return False
        else:
            if not self.customer_name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل!")
                return False
            if not self.customer_phone_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال رقم هاتف العميل!")
                return False
        
        # التحقق من بيانات الجهاز
        if not self.device_type_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال نوع الجهاز!")
            return False
        
        if not self.problem_text.get("1.0", tk.END).strip():
            messagebox.showerror("خطأ", "يرجى إدخال وصف المشكلة!")
            return False
        
        # التحقق من التكلفة
        try:
            float(self.estimated_cost_var.get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال تكلفة صحيحة!")
            return False
        
        return True
    
    def save(self):
        """حفظ البيانات"""
        if not self.validate_data():
            return
        
        try:
            customer_id = None
            
            # معالجة بيانات العميل
            if self.customer_type.get() == "existing":
                # العثور على معرف العميل المحدد
                selected_index = self.customer_combo.current()
                if selected_index >= 0:
                    customer_id = self.customers_data[selected_index]['id']
            else:
                # إضافة عميل جديد
                customer_query = """
                INSERT INTO customers (name, phone, address) 
                VALUES (?, ?, ?)
                """
                if self.db_manager.execute_query(customer_query, (
                    self.customer_name_var.get().strip(),
                    self.customer_phone_var.get().strip(),
                    self.customer_address_var.get().strip()
                )):
                    # الحصول على معرف العميل الجديد
                    result = self.db_manager.fetch_query("SELECT last_insert_rowid() as id")
                    customer_id = result[0]['id']
            
            if customer_id is None:
                messagebox.showerror("خطأ", "فشل في معالجة بيانات العميل!")
                return
            
            # إضافة الجهاز للصيانة
            repair_query = """
            INSERT INTO repairs (customer_id, device_type, device_model, device_imei, 
                               problem_description, technician_notes, estimated_cost, 
                               repair_status, date_received) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            if self.db_manager.execute_query(repair_query, (
                customer_id,
                self.device_type_var.get().strip(),
                self.device_model_var.get().strip(),
                self.device_imei_var.get().strip(),
                self.problem_text.get("1.0", tk.END).strip(),
                self.technician_notes.get("1.0", tk.END).strip(),
                float(self.estimated_cost_var.get()),
                "قيد الانتظار",
                date.today().isoformat()
            )):
                messagebox.showinfo("نجح", "تم إضافة الجهاز للصيانة بنجاح!")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الجهاز للصيانة!")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class UpdateRepairStatusDialog:
    def __init__(self, parent, db_manager, repair_id):
        self.parent = parent
        self.db_manager = db_manager
        self.repair_id = repair_id
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تحديث حالة الصيانة")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # تحميل بيانات الصيانة
        self.load_repair_data()
        
        # إعداد الواجهة
        self.setup_ui()
    
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def load_repair_data(self):
        """تحميل بيانات الصيانة"""
        try:
            query = """
            SELECT r.*, c.name as customer_name, c.phone as customer_phone
            FROM repairs r
            LEFT JOIN customers c ON r.customer_id = c.id
            WHERE r.id = ?
            """
            result = self.db_manager.fetch_query(query, (self.repair_id,))
            if result:
                self.repair_data = result[0]
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات الصيانة!")
                self.dialog.destroy()
                return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
            self.dialog.destroy()
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="تحديث حالة الصيانة", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # معلومات الجهاز
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجهاز", padding=15)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(info_frame, text=f"العميل: {self.repair_data['customer_name']}").pack(anchor="w")
        ttk.Label(info_frame, text=f"الهاتف: {self.repair_data['customer_phone']}").pack(anchor="w")
        ttk.Label(info_frame, text=f"الجهاز: {self.repair_data['device_type']} {self.repair_data['device_model'] or ''}").pack(anchor="w")
        ttk.Label(info_frame, text=f"المشكلة: {self.repair_data['problem_description']}").pack(anchor="w")
        ttk.Label(info_frame, text=f"الحالة الحالية: {self.repair_data['repair_status']}").pack(anchor="w")
        
        # تحديث الحالة
        status_frame = ttk.LabelFrame(main_frame, text="تحديث الحالة", padding=15)
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(status_frame, text="الحالة الجديدة:").pack(anchor="w", pady=(0, 5))
        self.new_status_var = tk.StringVar(value=self.repair_data['repair_status'])
        status_combo = ttk.Combobox(status_frame, textvariable=self.new_status_var, width=30)
        status_combo['values'] = ('قيد الانتظار', 'جاري الإصلاح', 'تم الإصلاح', 'تم التسليم', 'ملغي')
        status_combo.pack(fill=tk.X, pady=(0, 10))
        
        # التكلفة الفعلية
        ttk.Label(status_frame, text="التكلفة الفعلية (₪):").pack(anchor="w", pady=(0, 5))
        self.actual_cost_var = tk.StringVar(value=str(self.repair_data['actual_cost'] or self.repair_data['estimated_cost']))
        ttk.Entry(status_frame, textvariable=self.actual_cost_var).pack(fill=tk.X, pady=(0, 10))
        
        # ملاحظات إضافية
        ttk.Label(status_frame, text="ملاحظات إضافية:").pack(anchor="w", pady=(0, 5))
        self.additional_notes = tk.Text(status_frame, height=4)
        self.additional_notes.pack(fill=tk.X)
        
        # إدراج الملاحظات الحالية
        if self.repair_data['technician_notes']:
            self.additional_notes.insert("1.0", self.repair_data['technician_notes'])
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(buttons_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="حفظ التحديث", 
                  command=self.save).pack(side=tk.RIGHT)
    
    def save(self):
        """حفظ التحديث"""
        try:
            # التحقق من التكلفة
            try:
                actual_cost = float(self.actual_cost_var.get())
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال تكلفة صحيحة!")
                return
            
            # تحديث البيانات
            update_query = """
            UPDATE repairs 
            SET repair_status = ?, actual_cost = ?, technician_notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            
            # تحديث تاريخ الإكمال أو التسليم
            completion_date = None
            delivery_date = None
            
            if self.new_status_var.get() == "تم الإصلاح":
                completion_date = date.today().isoformat()
                update_query = """
                UPDATE repairs 
                SET repair_status = ?, actual_cost = ?, technician_notes = ?, 
                    date_completed = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
            elif self.new_status_var.get() == "تم التسليم":
                completion_date = self.repair_data['date_completed'] or date.today().isoformat()
                delivery_date = date.today().isoformat()
                update_query = """
                UPDATE repairs 
                SET repair_status = ?, actual_cost = ?, technician_notes = ?, 
                    date_completed = ?, date_delivered = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
            
            # تنفيذ التحديث
            params = [
                self.new_status_var.get(),
                actual_cost,
                self.additional_notes.get("1.0", tk.END).strip(),
            ]
            
            if completion_date:
                params.append(completion_date)
            if delivery_date:
                params.append(delivery_date)
            
            params.append(self.repair_id)
            
            if self.db_manager.execute_query(update_query, tuple(params)):
                messagebox.showinfo("نجح", "تم تحديث حالة الصيانة بنجاح!")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث حالة الصيانة!")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result
