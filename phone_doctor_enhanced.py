#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 Enhanced Edition
نسخة محسنة مع نموذج تسجيل دخول متطور

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import sys
import os
from datetime import datetime

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = 'database/phone_doctor.db'
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if not os.path.exists('database'):
                os.makedirs('database')
            
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة المستخدمين الافتراضيين
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('admin', 'admin123', 'مدير النظام', 'admin'))
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('sales', 'sales123', 'موظف المبيعات', 'sales'))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False

class AuthManager:
    """مدير المصادقة"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.current_user = None
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            users = self.db_manager.fetch_all('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                                            (username, password))
            
            if users:
                user = users[0]
                self.current_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role']
                }
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

class EnhancedLoginWindow:
    """نافذة تسجيل الدخول المحسنة"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.root = None
        self.is_loading = False
    
    def show(self):
        """عرض نافذة تسجيل الدخول المحسنة"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Enhanced Login")
        self.root.geometry("550x650")
        self.root.configure(bg='#0f172a')
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_content()
        
        self.root.mainloop()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 550
        height = 650
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_content(self):
        """إنشاء المحتوى"""
        # الحاوية الرئيسية
        main_frame = tk.Frame(self.root, bg='#0f172a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # شعار التطبيق
        self.create_logo(main_frame)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_frame)
        
        # معلومات تسجيل الدخول
        self.create_info_section(main_frame)
        
        # معلومات المطور
        self.create_developer_info(main_frame)
    
    def create_logo(self, parent):
        """إنشاء شعار التطبيق"""
        logo_frame = tk.Frame(parent, bg='#0f172a')
        logo_frame.pack(pady=(0, 30))
        
        # أيقونة التطبيق
        icon_label = tk.Label(
            logo_frame,
            text="📱💎📱",
            bg='#0f172a',
            font=('Arial', 36)
        )
        icon_label.pack()
        
        # اسم التطبيق
        app_name = tk.Label(
            logo_frame,
            text="Phone Doctor v2.0",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 24, 'bold')
        )
        app_name.pack(pady=(10, 0))
        
        # العنوان الفرعي
        subtitle = tk.Label(
            logo_frame,
            text="Enhanced Professional Edition",
            bg='#0f172a',
            fg='#94a3b8',
            font=('Arial', 12, 'italic')
        )
        subtitle.pack(pady=(5, 0))
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        # بطاقة النموذج
        form_card = tk.Frame(parent, bg='#1e293b', relief='raised', bd=3)
        form_card.pack(pady=20, fill=tk.X)
        
        # عنوان النموذج
        form_title = tk.Label(
            form_card,
            text="🔐 تسجيل الدخول",
            bg='#1e293b',
            fg='#fbbf24',
            font=('Arial', 16, 'bold')
        )
        form_title.pack(pady=(20, 15))
        
        # حقل اسم المستخدم
        self.create_input_field(form_card, "👤 اسم المستخدم:", "username")
        
        # حقل كلمة المرور
        self.create_input_field(form_card, "🔒 كلمة المرور:", "password", is_password=True)
        
        # الأزرار
        self.create_buttons(form_card)
    
    def create_input_field(self, parent, label_text, field_type, is_password=False):
        """إنشاء حقل إدخال"""
        # إطار الحقل
        field_frame = tk.Frame(parent, bg='#1e293b')
        field_frame.pack(pady=10, padx=30, fill=tk.X)
        
        # التسمية
        label = tk.Label(
            field_frame,
            text=label_text,
            bg='#1e293b',
            fg='white',
            font=('Arial', 12, 'bold'),
            anchor='w'
        )
        label.pack(fill=tk.X, pady=(0, 5))
        
        # حاوية الإدخال
        entry_container = tk.Frame(field_frame, bg='#374151', relief='flat', bd=2)
        entry_container.pack(fill=tk.X, ipady=2)
        
        # حقل الإدخال
        entry = tk.Entry(
            entry_container,
            font=('Arial', 13),
            bg='#4b5563',
            fg='white',
            relief='flat',
            bd=0,
            insertbackground='#60a5fa',
            show="*" if is_password else ""
        )
        entry.pack(fill=tk.X, padx=8, pady=6)
        
        # حفظ المرجع
        if field_type == "username":
            self.username_entry = entry
        else:
            self.password_entry = entry
        
        # تأثيرات التفاعل
        self.add_entry_effects(entry, entry_container)
    
    def add_entry_effects(self, entry, container):
        """إضافة تأثيرات للحقول"""
        def on_focus_in(event):
            container.configure(bg='#60a5fa', bd=3)
            entry.configure(bg='#374151')
        
        def on_focus_out(event):
            container.configure(bg='#374151', bd=2)
            entry.configure(bg='#4b5563')
        
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
    
    def create_buttons(self, parent):
        """إنشاء الأزرار"""
        buttons_frame = tk.Frame(parent, bg='#1e293b')
        buttons_frame.pack(pady=(15, 25), padx=30, fill=tk.X)
        
        # زر تسجيل الدخول الرئيسي
        self.login_btn = tk.Button(
            buttons_frame,
            text="🚀 دخول",
            command=self.enhanced_login,
            bg='#3b82f6',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=10,
            cursor='hand2'
        )
        self.login_btn.pack(fill=tk.X, pady=(0, 10))
        

        
        # ربط Enter
        self.username_entry.focus()
        self.root.bind('<Return>', lambda e: self.enhanced_login())
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg='#0f172a', relief='raised', bd=2)
        info_frame.pack(pady=15, fill=tk.X)
        
        info_title = tk.Label(
            info_frame,
            text="ℹ️ معلومات تسجيل الدخول",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 13, 'bold')
        )
        info_title.pack(pady=(15, 10))
        
        # معلومات المدير
        admin_info = tk.Label(
            info_frame,
            text="👑 المدير: admin / admin123 (صلاحيات كاملة)",
            bg='#0f172a',
            fg='#10b981',
            font=('Arial', 11, 'bold')
        )
        admin_info.pack(pady=5)
        
        # معلومات المبيعات
        sales_info = tk.Label(
            info_frame,
            text="💼 المبيعات: sales / sales123 (صلاحيات محدودة)",
            bg='#0f172a',
            fg='#f59e0b',
            font=('Arial', 11, 'bold')
        )
        sales_info.pack(pady=(5, 15))
    
    def create_developer_info(self, parent):
        """إنشاء معلومات المطور"""
        dev_frame = tk.Frame(parent, bg='#0f172a')
        dev_frame.pack(pady=(10, 0))
        
        dev_info = tk.Label(
            dev_frame,
            text="💎 المطور: محمد الشوامرة - 0566000140",
            bg='#0f172a',
            fg='#6b7280',
            font=('Arial', 10, 'italic')
        )
        dev_info.pack()
    

    
    def enhanced_login(self):
        """تسجيل دخول محسن"""
        if self.is_loading:
            return
        
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("تنبيه", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # بدء التحميل
        self.start_loading()
        
        # محاكاة وقت المعالجة
        self.root.after(1000, lambda: self.complete_login(username, password))
    
    def start_loading(self):
        """بدء حالة التحميل"""
        self.is_loading = True
        self.login_btn.configure(
            text="🔄 جاري التحقق...",
            bg='#6b7280',
            state='disabled'
        )
    
    def complete_login(self, username, password):
        """إكمال تسجيل الدخول"""
        if self.auth_manager.authenticate(username, password):
            self.login_btn.configure(
                text="✅ تم بنجاح!",
                bg='#10b981'
            )
            self.root.after(800, self.close_and_continue)
        else:
            self.login_btn.configure(
                text="❌ فشل تسجيل الدخول",
                bg='#ef4444'
            )
            self.root.after(1500, self.reset_login_button)
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)
    
    def reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.is_loading = False
        self.login_btn.configure(
            text="🚀 دخول",
            bg='#3b82f6',
            state='normal'
        )
    
    def close_and_continue(self):
        """إغلاق النافذة والمتابعة"""
        self.root.destroy()
        self.on_success_callback()

class MainApplication:
    """التطبيق الرئيسي المحسن"""

    def __init__(self, auth_manager, db_manager):
        self.auth_manager = auth_manager
        self.db_manager = db_manager
        self.root = None
        self.current_page = 'dashboard'

    def run(self):
        """تشغيل التطبيق"""
        self.setup_main_window()
        self.root.mainloop()

    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Enhanced Professional Edition")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0f172a')
        self.root.state('zoomed')

        # الهيدر
        self.create_header()

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#0f172a')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # الشريط الجانبي
        self.create_sidebar(main_frame)

        # منطقة المحتوى
        self.content_area = tk.Frame(main_frame, bg='#1e293b', relief='raised', bd=2)
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تحميل لوحة التحكم
        self.load_dashboard()

    def create_header(self):
        """إنشاء الهيدر"""
        header_frame = tk.Frame(self.root, bg='#1e40af', height=70, relief='raised', bd=2)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # العنوان
        title_label = tk.Label(
            header_frame,
            text="Phone Doctor v2.0 - Enhanced Professional Edition",
            bg='#1e40af',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)

        # معلومات المستخدم
        if self.auth_manager.current_user:
            user_frame = tk.Frame(header_frame, bg='#1e40af')
            user_frame.pack(side=tk.RIGHT, padx=20, pady=20)

            user_label = tk.Label(
                user_frame,
                text=f"مرحباً، {self.auth_manager.current_user['full_name']}",
                bg='#1e40af',
                fg='#fbbf24',
                font=('Arial', 12, 'bold')
            )
            user_label.pack()

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = tk.Frame(parent, bg='#334155', width=250, relief='raised', bd=2)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # عنوان القائمة
        menu_title = tk.Label(
            sidebar_frame,
            text="القائمة الرئيسية",
            bg='#334155',
            fg='#60a5fa',
            font=('Arial', 14, 'bold')
        )
        menu_title.pack(pady=20)

        # أزرار القائمة
        menu_items = [
            ("dashboard", "لوحة التحكم", "#3b82f6"),
            ("repairs", "إدارة الصيانة", "#10b981"),
            ("customers", "إدارة العملاء", "#f59e0b"),
            ("checks", "إدارة الشيكات", "#8b5cf6"),
            ("sales", "إدارة المبيعات", "#06b6d4"),
            ("inventory", "إدارة المخزون", "#84cc16"),
            ("reports", "التقارير", "#f97316"),
            ("settings", "الإعدادات", "#6b7280")
        ]

        for page_id, title, color in menu_items:
            btn = tk.Button(
                sidebar_frame,
                text=title,
                command=lambda p=page_id: self.change_page(p),
                bg=color if page_id == self.current_page else '#475569',
                fg='white',
                font=('Arial', 11, 'bold'),
                relief='flat',
                bd=0,
                padx=15,
                pady=10,
                anchor='w',
                cursor='hand2'
            )
            btn.pack(fill=tk.X, padx=10, pady=2)

        # زر تسجيل الخروج
        logout_btn = tk.Button(
            sidebar_frame,
            text="تسجيل الخروج",
            command=self.logout,
            bg='#ef4444',
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=10,
            cursor='hand2'
        )
        logout_btn.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

    def change_page(self, page_id):
        """تغيير الصفحة"""
        self.current_page = page_id

        # مسح المحتوى الحالي
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # تحميل الصفحة المطلوبة
        if page_id == "dashboard":
            self.load_dashboard()
        elif page_id == "repairs":
            self.load_repairs()
        elif page_id == "customers":
            self.load_customers()
        elif page_id == "checks":
            self.load_checks()
        else:
            self.load_placeholder(page_id)

    def load_dashboard(self):
        """تحميل لوحة التحكم مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="لوحة التحكم المحسنة",
            bg='#1e293b',
            fg='#60a5fa',
            font=('Arial', 24, 'bold')
        )
        title_label.pack()

        # بطاقات الإحصائيات
        stats_frame = tk.Frame(self.content_area, bg='#1e293b')
        stats_frame.pack(fill=tk.X, padx=20, pady=20)

        # جلب الإحصائيات الحقيقية
        repairs_count = len(self.db_manager.fetch_all("SELECT * FROM repairs"))
        customers_count = len(self.db_manager.fetch_all("SELECT * FROM customers"))
        checks_count = len(self.db_manager.fetch_all("SELECT * FROM checks"))

        # حساب إجمالي الإيرادات
        revenue_data = self.db_manager.fetch_all("SELECT SUM(total_cost) as total FROM repairs WHERE total_cost > 0")
        total_revenue = revenue_data[0]['total'] if revenue_data and revenue_data[0]['total'] else 0

        stats_data = [
            ("أوامر الصيانة", repairs_count, "#10b981"),
            ("العملاء", customers_count, "#3b82f6"),
            ("الشيكات", checks_count, "#8b5cf6"),
            ("الإيرادات", f"{total_revenue:.0f} ₪", "#f59e0b")
        ]

        for i, (title, value, color) in enumerate(stats_data):
            col = i % 2
            row = i // 2

            card_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew', ipadx=30, ipady=20)

            value_label = tk.Label(card_frame, text=str(value), bg=color, fg='white', font=('Arial', 28, 'bold'))
            value_label.pack()

            title_label = tk.Label(card_frame, text=title, bg=color, fg='white', font=('Arial', 14, 'bold'))
            title_label.pack()

        # تكوين الشبكة
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)

        # رسالة ترحيب محسنة
        welcome_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        welcome_text = f"""
مرحباً بك في Phone Doctor v2.0 Enhanced Edition

المستخدم: {self.auth_manager.current_user['full_name']}
الدور: {self.auth_manager.current_user['role']}

النظام يحتوي على بيانات حقيقية:
• {repairs_count} أمر صيانة مع تفاصيل كاملة
• {customers_count} عميل مع حسابات مالية
• {checks_count} شيك بحالات مختلفة
• {total_revenue:.0f} ₪ إجمالي الإيرادات

جميع البيانات متاحة للعرض والتفاعل في الصفحات
        """

        welcome_label = tk.Label(
            welcome_frame,
            text=welcome_text,
            bg='#374151',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        welcome_label.pack(expand=True)

    def load_repairs(self):
        """تحميل صفحة الصيانة مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="إدارة الصيانة - البيانات الحقيقية",
            bg='#1e293b',
            fg='#10b981',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة صيانة", "#10b981"),
            ("تعديل", "#3b82f6"),
            ("حذف", "#ef4444"),
            ("تقارير", "#8b5cf6"),
            ("تحديث", "#f59e0b")
        ]

        for text, color in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.show_message(f"ميزة {t} قيد التطوير"),
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول الصيانة
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة أوامر الصيانة (البيانات الحقيقية)",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "العميل", "الجهاز", "السيريال", "المشكلة", "القطع", "التكلفة", "الحالة", "التاريخ")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تعيين العناوين
        column_widths = [50, 120, 120, 100, 150, 120, 80, 80, 100]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[i], anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        repairs = self.db_manager.fetch_all("""
            SELECT id, customer_name, device_type, device_brand, device_serial,
                   problem_description, parts_used, total_cost, status,
                   repair_date, created_date
            FROM repairs
            ORDER BY id DESC
            LIMIT 30
        """)

        # إضافة البيانات للجدول
        for repair in repairs:
            device = f"{repair['device_brand'] or ''} {repair['device_type']}".strip()
            problem = repair['problem_description'][:20] + "..." if len(repair['problem_description']) > 20 else repair['problem_description']
            parts = repair['parts_used'][:15] + "..." if repair['parts_used'] and len(repair['parts_used']) > 15 else (repair['parts_used'] or 'لا يوجد')
            cost = f"{repair['total_cost']:.0f} ₪" if repair['total_cost'] else 'غير محدد'
            date = repair['repair_date'][:10] if repair['repair_date'] else (repair['created_date'][:10] if repair['created_date'] else 'غير محدد')

            tree.insert('', 'end', values=(
                repair['id'],
                repair['customer_name'],
                device,
                repair['device_serial'] or 'غير محدد',
                problem,
                parts,
                cost,
                repair['status'],
                date
            ))

        # إحصائيات
        stats_label = tk.Label(
            table_frame,
            text=f"إجمالي أوامر الصيانة: {len(self.db_manager.fetch_all('SELECT * FROM repairs'))} (عرض أحدث 30)",
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_customers(self):
        """تحميل صفحة العملاء مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="إدارة العملاء - الحسابات المالية",
            bg='#1e293b',
            fg='#3b82f6',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة عميل", "#3b82f6"),
            ("تعديل", "#10b981"),
            ("حذف", "#ef4444"),
            ("الحسابات المالية", "#f59e0b"),
            ("تحديث", "#8b5cf6")
        ]

        for text, color in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.show_message(f"ميزة {t} قيد التطوير"),
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول العملاء
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة العملاء والحسابات المالية (البيانات الحقيقية)",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "الاسم", "الهاتف", "البريد", "إجمالي المشتريات", "المدفوع", "الدين", "صافي الربح")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        column_widths = [50, 150, 120, 180, 120, 120, 120, 120]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[i], anchor='center')

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        customers = self.db_manager.fetch_all("""
            SELECT c.*,
                   COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END), 0) as total_purchases,
                   COALESCE(SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as total_paid
            FROM customers c
            LEFT JOIN customer_transactions ct ON c.id = ct.customer_id
            GROUP BY c.id
            ORDER BY c.id
        """)

        total_debt = 0
        total_paid = 0
        total_profit = 0

        for customer in customers:
            purchases = customer['total_purchases'] or 0
            paid = customer['total_paid'] or 0
            debt = max(0, purchases - paid)
            profit = paid * 0.3  # هامش ربح 30%

            total_debt += debt
            total_paid += paid
            total_profit += profit

            tree.insert('', 'end', values=(
                customer['id'],
                customer['name'],
                customer['phone'],
                customer['email'] or 'غير محدد',
                f"{purchases:.2f} ₪",
                f"{paid:.2f} ₪",
                f"{debt:.2f} ₪" if debt > 0 else "لا يوجد",
                f"{profit:.2f} ₪"
            ))

        # إحصائيات العملاء
        stats_text = f"إجمالي العملاء: {len(customers)} | إجمالي الديون: {total_debt:.2f} ₪ | إجمالي المدفوع: {total_paid:.2f} ₪ | صافي الربح: {total_profit:.2f} ₪"
        stats_label = tk.Label(
            table_frame,
            text=stats_text,
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_checks(self):
        """تحميل صفحة الشيكات مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="إدارة الشيكات - البيانات الحقيقية",
            bg='#1e293b',
            fg='#8b5cf6',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة شيك", "#8b5cf6"),
            ("تحصيل", "#10b981"),
            ("مرتد", "#ef4444"),
            ("تقرير الشيكات", "#f59e0b"),
            ("تحديث", "#3b82f6")
        ]

        for text, color in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.show_message(f"ميزة {t} قيد التطوير"),
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول الشيكات
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة الشيكات (البيانات الحقيقية)",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "رقم الشيك", "المبلغ", "البنك", "تاريخ الإصدار", "تاريخ الاستحقاق", "المستفيد", "الدافع", "الحالة")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        column_widths = [50, 100, 100, 120, 100, 100, 120, 120, 80]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[i], anchor='center')

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        checks = self.db_manager.fetch_all("""
            SELECT * FROM checks ORDER BY id DESC
        """)

        total_amount = 0
        pending_count = 0
        cashed_count = 0
        bounced_count = 0

        for check in checks:
            total_amount += check['amount']
            if check['status'] == 'معلق':
                pending_count += 1
            elif check['status'] == 'محصل':
                cashed_count += 1
            elif check['status'] == 'مرتد':
                bounced_count += 1

            tree.insert('', 'end', values=(
                check['id'],
                check['check_number'],
                f"{check['amount']:.2f} ₪",
                check['bank_name'],
                check['issue_date'][:10] if check['issue_date'] else 'غير محدد',
                check['due_date'][:10] if check['due_date'] else 'غير محدد',
                check['payee'],
                check['payer'],
                check['status']
            ))

        # إحصائيات الشيكات
        stats_text = f"إجمالي الشيكات: {len(checks)} | معلقة: {pending_count} | محصلة: {cashed_count} | مرتدة: {bounced_count} | إجمالي المبلغ: {total_amount:.2f} ₪"
        stats_label = tk.Label(
            table_frame,
            text=stats_text,
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_placeholder(self, page_name):
        """تحميل صفحة مؤقتة"""
        title_label = tk.Label(
            self.content_area,
            text=f"صفحة {page_name}",
            bg='#1e293b',
            fg='#f59e0b',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=20)

        content_text = f"""
صفحة {page_name} قيد التطوير

هذه الصفحة ستحتوي على:
• واجهة متطورة مع تأثيرات احترافية
• إدارة شاملة للبيانات
• تقارير تفاعلية ومفصلة
• تصميم احترافي وسهل الاستخدام

سيتم إضافة المزيد من الميزات قريباً...

الميزات المتاحة حالياً:
✅ لوحة التحكم مع إحصائيات حية
✅ إدارة الصيانة مع 50 أمر حقيقي
✅ إدارة العملاء مع 20 عميل وحسابات مالية
✅ إدارة الشيكات مع 25 شيك بحالات مختلفة
        """

        content_label = tk.Label(
            self.content_area,
            text=content_text,
            bg='#1e293b',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        content_label.pack(expand=True)

    def show_message(self, message):
        """عرض رسالة"""
        messagebox.showinfo("معلومات", message)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟")
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            main()

def main():
    """الدالة الرئيسية"""
    print("بدء تشغيل Phone Doctor v2.0 Enhanced Edition...")

    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    if not db_manager.initialize_database():
        messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
        return

    # إنشاء مدير المصادقة
    auth_manager = AuthManager(db_manager)

    def on_login_success():
        """عند نجاح تسجيل الدخول"""
        app = MainApplication(auth_manager, db_manager)
        app.run()

    # عرض نافذة تسجيل الدخول المحسنة
    login_window = EnhancedLoginWindow(auth_manager, on_login_success)
    login_window.show()

if __name__ == "__main__":
    main()
