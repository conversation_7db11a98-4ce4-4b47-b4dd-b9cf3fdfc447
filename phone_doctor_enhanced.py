#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 Enhanced Edition
نسخة محسنة مع نموذج تسجيل دخول متطور

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import sys
import os
from datetime import datetime

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = 'database/phone_doctor.db'
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if not os.path.exists('database'):
                os.makedirs('database')
            
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة المستخدمين الافتراضيين
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('admin', 'admin123', 'مدير النظام', 'admin'))
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('sales', 'sales123', 'موظف المبيعات', 'sales'))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False

class AuthManager:
    """مدير المصادقة"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.current_user = None
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            users = self.db_manager.fetch_all('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                                            (username, password))
            
            if users:
                user = users[0]
                self.current_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role']
                }
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

class EnhancedLoginWindow:
    """نافذة تسجيل الدخول المحسنة"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.root = None
        self.is_loading = False
    
    def show(self):
        """عرض نافذة تسجيل الدخول المحسنة"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Enhanced Login")
        self.root.geometry("550x650")
        self.root.configure(bg='#0f172a')
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_content()
        
        self.root.mainloop()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 550
        height = 650
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_content(self):
        """إنشاء المحتوى"""
        # الحاوية الرئيسية
        main_frame = tk.Frame(self.root, bg='#0f172a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # شعار التطبيق
        self.create_logo(main_frame)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_frame)
        
        # معلومات تسجيل الدخول
        self.create_info_section(main_frame)
        
        # معلومات المطور
        self.create_developer_info(main_frame)
    
    def create_logo(self, parent):
        """إنشاء شعار التطبيق"""
        logo_frame = tk.Frame(parent, bg='#0f172a')
        logo_frame.pack(pady=(0, 30))
        
        # أيقونة التطبيق
        icon_label = tk.Label(
            logo_frame,
            text="📱💎📱",
            bg='#0f172a',
            font=('Arial', 36)
        )
        icon_label.pack()
        
        # اسم التطبيق
        app_name = tk.Label(
            logo_frame,
            text="Phone Doctor v2.0",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 24, 'bold')
        )
        app_name.pack(pady=(10, 0))
        
        # العنوان الفرعي
        subtitle = tk.Label(
            logo_frame,
            text="Enhanced Professional Edition",
            bg='#0f172a',
            fg='#94a3b8',
            font=('Arial', 12, 'italic')
        )
        subtitle.pack(pady=(5, 0))
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        # بطاقة النموذج
        form_card = tk.Frame(parent, bg='#1e293b', relief='raised', bd=3)
        form_card.pack(pady=20, fill=tk.X)
        
        # عنوان النموذج
        form_title = tk.Label(
            form_card,
            text="🔐 تسجيل الدخول",
            bg='#1e293b',
            fg='#fbbf24',
            font=('Arial', 16, 'bold')
        )
        form_title.pack(pady=(20, 15))
        
        # حقل اسم المستخدم
        self.create_input_field(form_card, "👤 اسم المستخدم:", "username")
        
        # حقل كلمة المرور
        self.create_input_field(form_card, "🔒 كلمة المرور:", "password", is_password=True)
        
        # الأزرار
        self.create_buttons(form_card)
    
    def create_input_field(self, parent, label_text, field_type, is_password=False):
        """إنشاء حقل إدخال"""
        # إطار الحقل
        field_frame = tk.Frame(parent, bg='#1e293b')
        field_frame.pack(pady=10, padx=30, fill=tk.X)
        
        # التسمية
        label = tk.Label(
            field_frame,
            text=label_text,
            bg='#1e293b',
            fg='white',
            font=('Arial', 12, 'bold'),
            anchor='w'
        )
        label.pack(fill=tk.X, pady=(0, 5))
        
        # حاوية الإدخال
        entry_container = tk.Frame(field_frame, bg='#374151', relief='flat', bd=2)
        entry_container.pack(fill=tk.X, ipady=2)
        
        # حقل الإدخال
        entry = tk.Entry(
            entry_container,
            font=('Arial', 13),
            bg='#4b5563',
            fg='white',
            relief='flat',
            bd=0,
            insertbackground='#60a5fa',
            show="*" if is_password else ""
        )
        entry.pack(fill=tk.X, padx=8, pady=6)
        
        # حفظ المرجع
        if field_type == "username":
            self.username_entry = entry
        else:
            self.password_entry = entry
        
        # تأثيرات التفاعل
        self.add_entry_effects(entry, entry_container)
    
    def add_entry_effects(self, entry, container):
        """إضافة تأثيرات للحقول"""
        def on_focus_in(event):
            container.configure(bg='#60a5fa', bd=3)
            entry.configure(bg='#374151')
        
        def on_focus_out(event):
            container.configure(bg='#374151', bd=2)
            entry.configure(bg='#4b5563')
        
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
    
    def create_buttons(self, parent):
        """إنشاء الأزرار"""
        buttons_frame = tk.Frame(parent, bg='#1e293b')
        buttons_frame.pack(pady=(15, 25), padx=30, fill=tk.X)
        
        # زر تسجيل الدخول الرئيسي
        self.login_btn = tk.Button(
            buttons_frame,
            text="🚀 دخول",
            command=self.enhanced_login,
            bg='#3b82f6',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=10,
            cursor='hand2'
        )
        self.login_btn.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار سريعة
        quick_frame = tk.Frame(buttons_frame, bg='#1e293b')
        quick_frame.pack(fill=tk.X)
        
        # زر المدير
        admin_btn = tk.Button(
            quick_frame,
            text="👑 دخول المدير",
            command=lambda: self.quick_login('admin', 'admin123'),
            bg='#10b981',
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        admin_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        # زر المبيعات
        sales_btn = tk.Button(
            quick_frame,
            text="💼 دخول المبيعات",
            command=lambda: self.quick_login('sales', 'sales123'),
            bg='#f59e0b',
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        sales_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        
        # ربط Enter
        self.username_entry.focus()
        self.root.bind('<Return>', lambda e: self.enhanced_login())
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg='#0f172a', relief='raised', bd=2)
        info_frame.pack(pady=15, fill=tk.X)
        
        info_title = tk.Label(
            info_frame,
            text="ℹ️ معلومات تسجيل الدخول",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 13, 'bold')
        )
        info_title.pack(pady=(15, 10))
        
        # معلومات المدير
        admin_info = tk.Label(
            info_frame,
            text="👑 المدير: admin / admin123 (صلاحيات كاملة)",
            bg='#0f172a',
            fg='#10b981',
            font=('Arial', 11, 'bold')
        )
        admin_info.pack(pady=5)
        
        # معلومات المبيعات
        sales_info = tk.Label(
            info_frame,
            text="💼 المبيعات: sales / sales123 (صلاحيات محدودة)",
            bg='#0f172a',
            fg='#f59e0b',
            font=('Arial', 11, 'bold')
        )
        sales_info.pack(pady=(5, 15))
    
    def create_developer_info(self, parent):
        """إنشاء معلومات المطور"""
        dev_frame = tk.Frame(parent, bg='#0f172a')
        dev_frame.pack(pady=(10, 0))
        
        dev_info = tk.Label(
            dev_frame,
            text="💎 المطور: محمد الشوامرة - 0566000140",
            bg='#0f172a',
            fg='#6b7280',
            font=('Arial', 10, 'italic')
        )
        dev_info.pack()
    
    def quick_login(self, username, password):
        """تسجيل دخول سريع"""
        self.username_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.username_entry.insert(0, username)
        self.password_entry.insert(0, password)
        self.enhanced_login()
    
    def enhanced_login(self):
        """تسجيل دخول محسن"""
        if self.is_loading:
            return
        
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("تنبيه", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # بدء التحميل
        self.start_loading()
        
        # محاكاة وقت المعالجة
        self.root.after(1000, lambda: self.complete_login(username, password))
    
    def start_loading(self):
        """بدء حالة التحميل"""
        self.is_loading = True
        self.login_btn.configure(
            text="🔄 جاري التحقق...",
            bg='#6b7280',
            state='disabled'
        )
    
    def complete_login(self, username, password):
        """إكمال تسجيل الدخول"""
        if self.auth_manager.authenticate(username, password):
            self.login_btn.configure(
                text="✅ تم بنجاح!",
                bg='#10b981'
            )
            self.root.after(800, self.close_and_continue)
        else:
            self.login_btn.configure(
                text="❌ فشل تسجيل الدخول",
                bg='#ef4444'
            )
            self.root.after(1500, self.reset_login_button)
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)
    
    def reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.is_loading = False
        self.login_btn.configure(
            text="🚀 دخول",
            bg='#3b82f6',
            state='normal'
        )
    
    def close_and_continue(self):
        """إغلاق النافذة والمتابعة"""
        self.root.destroy()
        self.on_success_callback()

class MainApplication:
    """التطبيق الرئيسي المحسن"""

    def __init__(self, auth_manager, db_manager):
        self.auth_manager = auth_manager
        self.db_manager = db_manager
        self.root = None
        self.current_page = 'dashboard'

    def run(self):
        """تشغيل التطبيق"""
        self.setup_main_window()
        self.root.mainloop()

    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Enhanced Professional Edition")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0f172a')
        self.root.state('zoomed')

        # الهيدر
        self.create_header()

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#0f172a')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # الشريط الجانبي
        self.create_sidebar(main_frame)

        # منطقة المحتوى
        self.content_area = tk.Frame(main_frame, bg='#1e293b', relief='raised', bd=2)
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تحميل لوحة التحكم
        self.load_dashboard()

    def create_header(self):
        """إنشاء الهيدر"""
        header_frame = tk.Frame(self.root, bg='#1e40af', height=70, relief='raised', bd=2)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # العنوان
        title_label = tk.Label(
            header_frame,
            text="Phone Doctor v2.0 - Enhanced Professional Edition",
            bg='#1e40af',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)

        # معلومات المستخدم
        if self.auth_manager.current_user:
            user_frame = tk.Frame(header_frame, bg='#1e40af')
            user_frame.pack(side=tk.RIGHT, padx=20, pady=20)

            user_label = tk.Label(
                user_frame,
                text=f"مرحباً، {self.auth_manager.current_user['full_name']}",
                bg='#1e40af',
                fg='#fbbf24',
                font=('Arial', 12, 'bold')
            )
            user_label.pack()

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = tk.Frame(parent, bg='#334155', width=250, relief='raised', bd=2)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # عنوان القائمة
        menu_title = tk.Label(
            sidebar_frame,
            text="القائمة الرئيسية",
            bg='#334155',
            fg='#60a5fa',
            font=('Arial', 14, 'bold')
        )
        menu_title.pack(pady=20)

        # أزرار القائمة
        menu_items = [
            ("dashboard", "لوحة التحكم", "#3b82f6"),
            ("repairs", "إدارة الصيانة", "#10b981"),
            ("customers", "إدارة العملاء", "#f59e0b"),
            ("checks", "إدارة الشيكات", "#8b5cf6"),
            ("sales", "إدارة المبيعات", "#06b6d4"),
            ("inventory", "إدارة المخزون", "#84cc16"),
            ("reports", "التقارير", "#f97316"),
            ("settings", "الإعدادات", "#6b7280")
        ]

        for page_id, title, color in menu_items:
            btn = tk.Button(
                sidebar_frame,
                text=title,
                command=lambda p=page_id: self.change_page(p),
                bg=color if page_id == self.current_page else '#475569',
                fg='white',
                font=('Arial', 11, 'bold'),
                relief='flat',
                bd=0,
                padx=15,
                pady=10,
                anchor='w',
                cursor='hand2'
            )
            btn.pack(fill=tk.X, padx=10, pady=2)

        # زر تسجيل الخروج
        logout_btn = tk.Button(
            sidebar_frame,
            text="تسجيل الخروج",
            command=self.logout,
            bg='#ef4444',
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=10,
            cursor='hand2'
        )
        logout_btn.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

    def change_page(self, page_id):
        """تغيير الصفحة"""
        self.current_page = page_id

        # مسح المحتوى الحالي
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # تحميل الصفحة المطلوبة
        if page_id == "dashboard":
            self.load_dashboard()
        else:
            self.load_placeholder(page_id)

    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        # العنوان
        title_label = tk.Label(
            self.content_area,
            text="لوحة التحكم المحسنة",
            bg='#1e293b',
            fg='#60a5fa',
            font=('Arial', 20, 'bold')
        )
        title_label.pack(pady=20)

        # رسالة ترحيب
        welcome_text = f"""
مرحباً بك في Phone Doctor v2.0 Enhanced Edition

المستخدم: {self.auth_manager.current_user['full_name']}
الدور: {self.auth_manager.current_user['role']}

تم تحسين نموذج تسجيل الدخول بنجاح!

الميزات الجديدة:
✅ تصميم احترافي محسن
✅ تأثيرات تفاعلية للحقول
✅ أزرار دخول سريع
✅ تأثيرات تحميل
✅ رسائل خطأ محسنة
✅ توسيط تلقائي للنوافذ

جميع البيانات الحقيقية متاحة في الصفحات الأخرى
        """

        welcome_label = tk.Label(
            self.content_area,
            text=welcome_text,
            bg='#1e293b',
            fg='white',
            font=('Arial', 12),
            justify=tk.CENTER
        )
        welcome_label.pack(expand=True)

    def load_placeholder(self, page_name):
        """تحميل صفحة مؤقتة"""
        title_label = tk.Label(
            self.content_area,
            text=f"صفحة {page_name}",
            bg='#1e293b',
            fg='#f59e0b',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=20)

        content_label = tk.Label(
            self.content_area,
            text=f"صفحة {page_name} متاحة في النسخة النهائية\nمع جميع البيانات الحقيقية",
            bg='#1e293b',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        content_label.pack(expand=True)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟")
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            main()

def main():
    """الدالة الرئيسية"""
    print("بدء تشغيل Phone Doctor v2.0 Enhanced Edition...")

    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    if not db_manager.initialize_database():
        messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
        return

    # إنشاء مدير المصادقة
    auth_manager = AuthManager(db_manager)

    def on_login_success():
        """عند نجاح تسجيل الدخول"""
        app = MainApplication(auth_manager, db_manager)
        app.run()

    # عرض نافذة تسجيل الدخول المحسنة
    login_window = EnhancedLoginWindow(auth_manager, on_login_success)
    login_window.show()

if __name__ == "__main__":
    main()
