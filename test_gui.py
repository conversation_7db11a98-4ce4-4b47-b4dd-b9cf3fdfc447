#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة المستخدم - Phone Doctor v2.0
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_gui():
    """اختبار واجهة المستخدم الأساسية"""
    try:
        print("🔍 اختبار Tkinter...")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار Phone Doctor")
        root.geometry("400x300")
        root.configure(bg='#f0f0f0')
        
        # إضافة عنوان
        title_label = tk.Label(
            root,
            text="🔧 Phone Doctor v2.0",
            font=('Arial', 16, 'bold'),
            bg='#f0f0f0',
            fg='#333333'
        )
        title_label.pack(pady=20)
        
        # إضافة رسالة
        message_label = tk.Label(
            root,
            text="✅ واجهة المستخدم تعمل بشكل صحيح!",
            font=('Arial', 12),
            bg='#f0f0f0',
            fg='#28a745'
        )
        message_label.pack(pady=10)
        
        # زر الاختبار
        def show_success():
            messagebox.showinfo("نجح الاختبار", "🎉 البرنامج يعمل بشكل مثالي!")
            root.quit()
        
        test_button = tk.Button(
            root,
            text="اختبار النظام",
            command=show_success,
            font=('Arial', 12),
            bg='#007bff',
            fg='white',
            padx=20,
            pady=10
        )
        test_button.pack(pady=20)
        
        # زر الإغلاق
        close_button = tk.Button(
            root,
            text="إغلاق",
            command=root.quit,
            font=('Arial', 12),
            bg='#dc3545',
            fg='white',
            padx=20,
            pady=10
        )
        close_button.pack(pady=10)
        
        # معلومات النظام
        info_label = tk.Label(
            root,
            text=f"Python: {sys.version[:5]} | Tkinter: متاح",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#666666'
        )
        info_label.pack(side=tk.BOTTOM, pady=10)
        
        print("✅ نافذة الاختبار جاهزة")
        
        # تشغيل النافذة
        root.mainloop()
        root.destroy()
        
        print("✅ اختبار واجهة المستخدم مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار واجهة المستخدم...")
    
    if test_gui():
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("❌ فشل في الاختبار")
