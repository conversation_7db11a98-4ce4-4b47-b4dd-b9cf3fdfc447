#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الشيكات المتطورة - Phone Doctor v2.0
Advanced Checks Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.theme_manager import ThemeManager

class ChecksManager:
    """مدير الشيكات المتطور"""
    
    def __init__(self, parent_frame, db_manager, current_user=None):
        print("💳 تهيئة مدير الشيكات المتطور...")
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.current_user = current_user or {'username': 'admin', 'full_name': 'المدير'}
        self.selected_check = None
        
        # تهيئة مدير الثيمات
        self.theme = ThemeManager()
        
        # متغيرات الحالة
        self.current_filter = 'all'
        self.current_sort = 'newest'
        self.is_loading = False
        
        self.setup_ui()
        self.ensure_checks_table()
        self.load_checks()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتطورة"""
        # تنظيف الإطار
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # تطبيق النمط العصري
        self.parent_frame.configure(bg=self.theme.colors['bg_primary'])
        
        # الهيدر المتطور مع تدرج لوني
        header_frame = self.theme.create_gradient_frame(
            self.parent_frame, 
            self.theme.colors['gradient_warning'], 
            height=100
        )
        header_frame.pack(fill=tk.X)
        
        # محتوى الهيدر
        header_content = tk.Frame(header_frame, bg='transparent')
        header_content.place(relx=0.5, rely=0.5, anchor='center')
        
        # أيقونة وعنوان
        title_frame = tk.Frame(header_content, bg='transparent')
        title_frame.pack()
        
        icon_label = tk.Label(
            title_frame,
            text="💳",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=('Arial', 32)
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        title_label = tk.Label(
            title_frame,
            text="إدارة الشيكات المتطورة",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['title_large']
        )
        title_label.pack(side=tk.LEFT)
        
        # معلومات المستخدم
        user_info = tk.Label(
            header_content,
            text=f"{self.theme.icons['user']} {self.current_user.get('full_name', 'المستخدم')}",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['body_medium']
        )
        user_info.pack(pady=(10, 0))
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent_frame, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
        
        # بطاقة الإحصائيات المالية
        stats_card, stats_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['money']} إحصائيات الشيكات"
        )
        stats_card.pack(fill=tk.X, pady=(0, 20))
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(stats_content, bg=self.theme.colors['bg_card'])
        stats_frame.pack(fill=tk.X)
        
        # إحصائيات مختلفة
        self.stats_labels = {}
        stats_data = [
            ("total_checks", "إجمالي الشيكات", "info", "money"),
            ("pending_checks", "شيكات معلقة", "warning", "pending"),
            ("cashed_checks", "شيكات محصلة", "success", "completed"),
            ("total_amount", "إجمالي المبلغ", "primary", "star"),
        ]
        
        for i, (key, label, style, icon) in enumerate(stats_data):
            stat_frame = tk.Frame(stats_frame, bg=self.theme.colors['bg_card'])
            stat_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10 if i < len(stats_data)-1 else 0))
            
            # أيقونة الإحصائية
            icon_label = tk.Label(
                stat_frame,
                text=self.theme.icons[icon],
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors[style],
                font=('Arial', 20)
            )
            icon_label.pack()
            
            # قيمة الإحصائية
            value_label = tk.Label(
                stat_frame,
                text="0",
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_primary'],
                font=self.theme.fonts['title_medium']
            )
            value_label.pack()
            
            # تسمية الإحصائية
            name_label = tk.Label(
                stat_frame,
                text=label,
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_secondary'],
                font=self.theme.fonts['body_small']
            )
            name_label.pack()
            
            self.stats_labels[key] = value_label
        
        # بطاقة شريط الأدوات
        toolbar_card, toolbar_content = self.theme.create_modern_card(
            main_frame, 
            title=f"{self.theme.icons['settings']} أدوات إدارة الشيكات"
        )
        toolbar_card.pack(fill=tk.X, pady=(0, 20))
        
        # الصف الأول من الأزرار
        buttons_row1 = tk.Frame(toolbar_content, bg=self.theme.colors['bg_card'])
        buttons_row1.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار العمليات الأساسية
        add_btn = self.theme.create_modern_button(
            buttons_row1,
            "إضافة شيك",
            command=self.add_check,
            style='success',
            size='medium',
            icon='add'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        edit_btn = self.theme.create_modern_button(
            buttons_row1,
            "تعديل",
            command=self.edit_check,
            style='info',
            size='medium',
            icon='edit'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        delete_btn = self.theme.create_modern_button(
            buttons_row1,
            "حذف",
            command=self.delete_check,
            style='danger',
            size='medium',
            icon='delete'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # الصف الثاني من الأزرار
        buttons_row2 = tk.Frame(toolbar_content, bg=self.theme.colors['bg_card'])
        buttons_row2.pack(fill=tk.X)
        
        refresh_btn = self.theme.create_modern_button(
            buttons_row2,
            "تحديث",
            command=self.load_checks,
            style='warning',
            size='medium',
            icon='refresh'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        cash_btn = self.theme.create_modern_button(
            buttons_row2,
            "تحصيل شيك",
            command=self.cash_check,
            style='success',
            size='medium',
            icon='money'
        )
        cash_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        bounce_btn = self.theme.create_modern_button(
            buttons_row2,
            "شيك مرتد",
            command=self.bounce_check,
            style='danger',
            size='medium',
            icon='cancel'
        )
        bounce_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        report_btn = self.theme.create_modern_button(
            buttons_row2,
            "تقرير الشيكات",
            command=self.generate_checks_report,
            style='secondary',
            size='medium',
            icon='export'
        )
        report_btn.pack(side=tk.LEFT)
        
        # بطاقة البحث والفلترة
        search_card, search_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['search']} البحث والفلترة المتقدمة"
        )
        search_card.pack(fill=tk.X, pady=(0, 20))
        
        # البحث الرئيسي
        search_row = tk.Frame(search_content, bg=self.theme.colors['bg_card'])
        search_row.pack(fill=tk.X, pady=(0, 15))
        
        search_label = tk.Label(
            search_row,
            text=f"{self.theme.icons['search']} البحث:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['heading_small']
        )
        search_label.pack(side=tk.LEFT, padx=(0, 15))
        
        search_input_frame, self.search_entry = self.theme.create_modern_input(
            search_row,
            placeholder="ابحث في رقم الشيك، البنك، المستفيد، أو الدافع...",
            size='large'
        )
        search_input_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        
        search_btn = self.theme.create_modern_button(
            search_row,
            "بحث",
            command=self.perform_search,
            style='primary',
            size='medium',
            icon='search'
        )
        search_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = self.theme.create_modern_button(
            search_row,
            "مسح",
            command=self.clear_search,
            style='secondary',
            size='medium',
            icon='cancel'
        )
        clear_btn.pack(side=tk.LEFT)
        
        # الفلاتر
        filters_row = tk.Frame(search_content, bg=self.theme.colors['bg_card'])
        filters_row.pack(fill=tk.X)
        
        # فلتر حالة الشيك
        status_label = tk.Label(
            filters_row,
            text=f"{self.theme.icons['filter']} حالة الشيك:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        status_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.status_filter_var = tk.StringVar(value="الكل")
        status_combo = ttk.Combobox(
            filters_row,
            textvariable=self.status_filter_var,
            values=["الكل", "معلق", "محصل", "مرتد", "ملغي"],
            font=self.theme.fonts['body_medium'],
            width=12,
            state='readonly'
        )
        status_combo.pack(side=tk.LEFT, padx=(0, 20))
        status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # فلتر البنك
        bank_label = tk.Label(
            filters_row,
            text=f"🏦 البنك:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        bank_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.bank_filter_var = tk.StringVar(value="الكل")
        bank_combo = ttk.Combobox(
            filters_row,
            textvariable=self.bank_filter_var,
            values=["الكل", "بنك فلسطين", "البنك الإسلامي", "بنك الاستثمار", "البنك الوطني", "بنك القدس"],
            font=self.theme.fonts['body_medium'],
            width=15,
            state='readonly'
        )
        bank_combo.pack(side=tk.LEFT)
        bank_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # ربط البحث الفوري
        self.search_var = tk.StringVar()
        self.search_entry.configure(textvariable=self.search_var)
        self.search_var.trace('w', self.on_search_change)

        # بطاقة جدول الشيكات
        table_card, table_content = self.theme.create_modern_card(
            main_frame,
            title=f"💳 قائمة الشيكات"
        )
        table_card.pack(fill=tk.BOTH, expand=True)

        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(table_content, bg=self.theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الجدول المتطور
        columns = ("ID", "رقم الشيك", "المبلغ", "البنك", "تاريخ الإصدار", "تاريخ الاستحقاق", "المستفيد", "الدافع", "الحالة", "ملاحظات")
        self.tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15,
            style='Modern.Treeview'
        )

        # تعيين العناوين والعرض
        column_widths = [50, 100, 100, 120, 100, 100, 120, 120, 80, 150]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=f"{self.get_column_icon(col)} {col}")
            self.tree.column(col, width=column_widths[i], anchor='center')

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # تلوين الصفوف حسب حالة الشيك
        self.tree.tag_configure('pending', background='#fef3c7', foreground='#92400e')
        self.tree.tag_configure('cashed', background='#d1fae5', foreground='#065f46')
        self.tree.tag_configure('bounced', background='#fee2e2', foreground='#991b1b')
        self.tree.tag_configure('cancelled', background='#f3f4f6', foreground='#6b7280')
        self.tree.tag_configure('overdue', background='#fecaca', foreground='#7f1d1d')

        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.view_check_details)
        self.tree.bind('<Button-3>', self.show_context_menu)

        # شريط الحالة المتطور
        status_frame = tk.Frame(
            self.parent_frame,
            bg=self.theme.colors['bg_tertiary'],
            height=50
        )
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)

        # محتوى شريط الحالة
        status_content = tk.Frame(status_frame, bg=self.theme.colors['bg_tertiary'])
        status_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # حالة النظام
        self.status_label = tk.Label(
            status_content,
            text=f"{self.theme.icons['success']} النظام جاهز",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        self.status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        info_frame = tk.Frame(status_content, bg=self.theme.colors['bg_tertiary'])
        info_frame.pack(side=tk.RIGHT)

        # وقت آخر تحديث
        self.last_update_label = tk.Label(
            info_frame,
            text=f"{self.theme.icons['clock']} آخر تحديث: --",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_secondary'],
            font=self.theme.fonts['body_small']
        )
        self.last_update_label.pack(side=tk.RIGHT, padx=(20, 0))

        # عداد النتائج
        self.count_label = tk.Label(
            info_frame,
            text="",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['primary'],
            font=self.theme.fonts['body_medium']
        )
        self.count_label.pack(side=tk.RIGHT, padx=(20, 0))

    def get_column_icon(self, column):
        """الحصول على أيقونة العمود"""
        icons = {
            "ID": self.theme.icons['info'],
            "رقم الشيك": "🔢",
            "المبلغ": self.theme.icons['money'],
            "البنك": "🏦",
            "تاريخ الإصدار": self.theme.icons['calendar'],
            "تاريخ الاستحقاق": self.theme.icons['calendar'],
            "المستفيد": self.theme.icons['customer'],
            "الدافع": self.theme.icons['user'],
            "الحالة": self.theme.icons['info'],
            "ملاحظات": "📝"
        }
        return icons.get(column, "")

    def ensure_checks_table(self):
        """التأكد من وجود جدول الشيكات"""
        try:
            print("🔨 التحقق من جدول الشيكات...")

            checks_table = '''
                CREATE TABLE IF NOT EXISTS checks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_number TEXT NOT NULL UNIQUE,
                    amount REAL NOT NULL,
                    bank_name TEXT NOT NULL,
                    issue_date DATE NOT NULL,
                    due_date DATE NOT NULL,
                    payee TEXT NOT NULL,
                    payer TEXT NOT NULL,
                    status TEXT DEFAULT 'معلق',
                    notes TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    cashed_date TIMESTAMP,
                    bounced_date TIMESTAMP,
                    created_by TEXT,
                    updated_by TEXT
                )
            '''

            self.db_manager.execute_query(checks_table)
            print("✅ تم التحقق من جدول الشيكات")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الشيكات: {e}")

    def load_checks(self):
        """تحميل قائمة الشيكات"""
        try:
            print("🔄 تحميل بيانات الشيكات...")
            self.is_loading = True
            self.update_status(f"{self.theme.icons['loading']} جاري تحميل البيانات...", "info")

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # الاتصال المباشر بقاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = """
                SELECT * FROM checks ORDER BY created_date DESC
            """

            cursor.execute(query)
            checks = cursor.fetchall()
            conn.close()

            print(f"📊 تم جلب {len(checks)} شيك")

            # حساب الإحصائيات
            stats = {
                'total_checks': len(checks),
                'pending_checks': 0,
                'cashed_checks': 0,
                'total_amount': 0
            }

            # إضافة البيانات للجدول وحساب الإحصائيات
            for check in checks:
                issue_date = check['issue_date'][:10] if check['issue_date'] else 'غير محدد'
                due_date = check['due_date'][:10] if check['due_date'] else 'غير محدد'

                # تحديد التاغ للتلوين
                tag = self.get_check_tag(check['status'], check['due_date'])

                # إضافة للجدول
                self.tree.insert('', 'end', values=(
                    check['id'],
                    check['check_number'],
                    f"{check['amount']:.2f} ₪",
                    check['bank_name'],
                    issue_date,
                    due_date,
                    check['payee'],
                    check['payer'],
                    check['status'],
                    check['notes'][:30] + "..." if check['notes'] and len(check['notes']) > 30 else (check['notes'] or '')
                ), tags=(tag,))

                # تحديث الإحصائيات
                stats['total_amount'] += check['amount']
                if check['status'] == 'معلق':
                    stats['pending_checks'] += 1
                elif check['status'] == 'محصل':
                    stats['cashed_checks'] += 1

            # تحديث الإحصائيات في الواجهة
            self.update_statistics(stats)

            # تحديث العداد وشريط الحالة
            self.count_label.configure(text=f"💳 إجمالي: {len(checks)} شيك")
            self.update_status(f"{self.theme.icons['success']} تم تحميل {len(checks)} شيك بنجاح", "success")

            # تحديث وقت آخر تحديث
            current_time = datetime.now().strftime("%H:%M:%S")
            self.last_update_label.configure(text=f"{self.theme.icons['clock']} آخر تحديث: {current_time}")

            self.is_loading = False

        except Exception as e:
            print(f"❌ خطأ في تحميل الشيكات: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في تحميل البيانات: {str(e)}", "error")
            self.is_loading = False

    def get_check_tag(self, status, due_date):
        """تحديد تاغ الشيك للتلوين"""
        if status == 'محصل':
            return 'cashed'
        elif status == 'مرتد':
            return 'bounced'
        elif status == 'ملغي':
            return 'cancelled'
        elif status == 'معلق':
            # فحص إذا كان الشيك متأخر
            if due_date:
                try:
                    due_date_obj = datetime.strptime(due_date[:10], "%Y-%m-%d")
                    if due_date_obj < datetime.now():
                        return 'overdue'
                except:
                    pass
            return 'pending'
        else:
            return 'pending'

    def update_statistics(self, stats):
        """تحديث الإحصائيات في الواجهة"""
        self.stats_labels['total_checks'].configure(text=str(stats['total_checks']))
        self.stats_labels['pending_checks'].configure(text=str(stats['pending_checks']))
        self.stats_labels['cashed_checks'].configure(text=str(stats['cashed_checks']))
        self.stats_labels['total_amount'].configure(text=f"{stats['total_amount']:.2f} ₪")

    def update_status(self, message, status_type="info"):
        """تحديث شريط الحالة"""
        icons = {
            "success": self.theme.icons['success'],
            "error": self.theme.icons['error'],
            "warning": self.theme.icons['warning'],
            "info": self.theme.icons['info'],
            "loading": self.theme.icons['loading']
        }

        colors = {
            "success": self.theme.colors['success'],
            "error": self.theme.colors['danger'],
            "warning": self.theme.colors['warning'],
            "info": self.theme.colors['info'],
            "loading": self.theme.colors['secondary']
        }

        icon = icons.get(status_type, self.theme.icons['info'])
        color = colors.get(status_type, self.theme.colors['text_primary'])

        self.status_label.configure(
            text=f"{icon} {message}",
            fg=color
        )

    def on_search_change(self, *args):
        """معالجة تغيير البحث الفوري"""
        if not self.is_loading:
            self.parent_frame.after(300, self.perform_search)

    def on_filter_change(self, event=None):
        """معالجة تغيير الفلاتر"""
        if not self.is_loading:
            self.perform_search()

    def perform_search(self):
        """تنفيذ البحث والفلترة"""
        self.update_status(f"{self.theme.icons['info']} ميزة البحث قيد التطوير", "info")

    def clear_search(self):
        """مسح البحث والفلاتر"""
        self.search_var.set("")
        self.status_filter_var.set("الكل")
        self.bank_filter_var.set("الكل")
        self.load_checks()

    def on_select(self, event):
        """معالجة اختيار شيك من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_check = {
                    'id': values[0],
                    'check_number': values[1],
                    'amount': values[2],
                    'bank_name': values[3],
                    'payee': values[6],
                    'payer': values[7],
                    'status': values[8]
                }
                self.update_status(f"💳 تم اختيار الشيك رقم: {values[1]}", "info")

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        self.update_status(f"{self.theme.icons['info']} قائمة السياق قيد التطوير", "info")

    def add_check(self):
        """إضافة شيك جديد"""
        self.update_status(f"{self.theme.icons['info']} ميزة إضافة الشيكات قيد التطوير", "info")

    def edit_check(self):
        """تعديل شيك"""
        if not self.selected_check:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار شيك للتعديل", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة تعديل الشيكات قيد التطوير", "info")

    def delete_check(self):
        """حذف شيك"""
        if not self.selected_check:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار شيك للحذف", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة حذف الشيكات قيد التطوير", "info")

    def view_check_details(self, event=None):
        """عرض تفاصيل الشيك"""
        if not self.selected_check:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار شيك لعرض التفاصيل", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة عرض تفاصيل الشيكات قيد التطوير", "info")

    def cash_check(self):
        """تحصيل شيك"""
        if not self.selected_check:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار شيك للتحصيل", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة تحصيل الشيكات قيد التطوير", "info")

    def bounce_check(self):
        """شيك مرتد"""
        if not self.selected_check:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار شيك لتسجيله كمرتد", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة الشيكات المرتدة قيد التطوير", "info")

    def generate_checks_report(self):
        """إنشاء تقرير الشيكات"""
        self.update_status(f"{self.theme.icons['info']} ميزة تقارير الشيكات قيد التطوير", "info")
