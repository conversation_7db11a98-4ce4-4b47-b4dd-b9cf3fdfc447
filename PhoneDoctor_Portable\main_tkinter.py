#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة محلات صيانة الهواتف - نسخة Tkinter
Phone Doctor - Professional Phone Repair Shop Management System

المطور: محمد الشوامرة
الهاتف: 0566000140
جميع الحقوق محفوظة © 2024
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os
from datetime import datetime
import threading
import time

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from utils.config import Config
from ui.repair_dialogs import NewRepairDialog, UpdateRepairStatusDialog
from ui.inventory_dialogs import NewInventoryItemDialog, InventoryManagementWindow
from ui.supplier_dialogs import NewSupplierDialog, SuppliersManagementWindow
from ui.modern_styles import ModernStyles
from ui.modern_sidebar import ModernSidebar
from ui.modern_header import ModernHeader, ModernPageHeader, ModernCard

class PhoneDoctorApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Phone Doctor - نظام إدارة محلات صيانة الهواتف")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        self.root.state('zoomed')  # تكبير النافذة

        # تهيئة الأنماط العصرية
        self.styles = ModernStyles()
        self.styles.configure_ttk_styles()

        # تهيئة قاعدة البيانات والإعدادات
        self.db_manager = DatabaseManager()
        self.config = Config()

        # تهيئة قاعدة البيانات
        if not self.db_manager.initialize_database():
            messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
            return

        # متغيرات التطبيق
        self.current_page = "dashboard"
        self.page_titles = {
            "dashboard": ("لوحة التحكم", "🏠"),
            "repairs": ("إدارة الصيانة", "🔧"),
            "inventory": ("إدارة المخزون", "📦"),
            "suppliers": ("إدارة الموردين", "🏪"),
            "sales": ("إدارة المبيعات", "💰"),
            "financial": ("الشؤون المالية", "💳"),
            "reports": ("التقارير والإحصائيات", "📊"),
            "settings": ("الإعدادات والتخصيص", "⚙️")
        }

        # إعداد الواجهة العصرية
        self.setup_modern_ui()
        self.load_dashboard_data()
        
    def setup_modern_ui(self):
        """إعداد الواجهة العصرية"""
        # تكوين النافذة الرئيسية
        self.root.configure(bg=self.styles.colors['bg_primary'])

        # الإطار الرئيسي
        main_container = tk.Frame(
            self.root,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_container.pack(fill=tk.BOTH, expand=True)

        # الشريط الجانبي العصري
        self.sidebar = ModernSidebar(
            main_container,
            on_menu_select=self.on_page_change
        )
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)

        # المنطقة الرئيسية للمحتوى
        self.content_area = tk.Frame(
            main_container,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # رأس الصفحة العصري
        self.header = ModernHeader(self.content_area)
        self.header.pack(fill=tk.X)

        # منطقة المحتوى القابلة للتمرير
        self.setup_content_area()

        # تحديث رأس الصفحة للصفحة الافتراضية
        self.update_page_header("dashboard")

    def setup_content_area(self):
        """إعداد منطقة المحتوى"""
        # إطار التمرير
        self.scroll_frame = tk.Frame(
            self.content_area,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        self.scroll_frame.pack(fill=tk.BOTH, expand=True)

        # Canvas للتمرير
        self.canvas = tk.Canvas(
            self.scroll_frame,
            bg=self.styles.colors['bg_secondary'],
            highlightthickness=0,
            relief='flat',
            bd=0
        )

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(
            self.scroll_frame,
            orient=tk.VERTICAL,
            command=self.canvas.yview,
            style='Modern.Vertical.TScrollbar'
        )

        # ربط Canvas بشريط التمرير
        self.canvas.configure(yscrollcommand=v_scrollbar.set)

        # إطار المحتوى الداخلي
        self.scrollable_frame = tk.Frame(
            self.canvas,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # إضافة إطار المحتوى إلى Canvas
        self.canvas_frame = self.canvas.create_window(
            (0, 0),
            window=self.scrollable_frame,
            anchor="nw"
        )

        # تخطيط العناصر
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط أحداث التمرير
        self.scrollable_frame.bind('<Configure>', self.on_frame_configure)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.canvas.bind_all('<MouseWheel>', self.on_mousewheel)

        # إنشاء صفحات المحتوى
        self.create_pages()

    def on_frame_configure(self, event=None):
        """تحديث منطقة التمرير عند تغيير حجم الإطار"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def on_canvas_configure(self, event=None):
        """تحديث عرض الإطار الداخلي عند تغيير حجم Canvas"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_frame, width=canvas_width)

    def on_mousewheel(self, event):
        """معالجة التمرير بالماوس"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def on_page_change(self, page_id):
        """معالجة تغيير الصفحة"""
        self.current_page = page_id
        self.update_page_header(page_id)
        self.show_page_content(page_id)

    def update_page_header(self, page_id):
        """تحديث رأس الصفحة"""
        if page_id in self.page_titles:
            title, icon = self.page_titles[page_id]
            self.header.update_page_info(title, icon)

    def show_page_content(self, page_id):
        """عرض محتوى الصفحة"""
        # إخفاء جميع الصفحات
        for page_frame in self.pages.values():
            page_frame.pack_forget()

        # عرض الصفحة المطلوبة
        if page_id in self.pages:
            self.pages[page_id].pack(fill=tk.BOTH, expand=True, padx=24, pady=24)

            # تحديث البيانات حسب الصفحة
            if page_id == "dashboard":
                self.load_dashboard_data()
            elif page_id == "inventory":
                self.refresh_inventory_summary()
            elif page_id == "suppliers":
                self.refresh_suppliers_summary()

    def create_pages(self):
        """إنشاء صفحات المحتوى"""
        self.pages = {}

        # إنشاء الصفحات
        self.pages["dashboard"] = self.create_modern_dashboard()
        self.pages["repairs"] = self.create_modern_repairs_page()
        self.pages["inventory"] = self.create_modern_inventory_page()
        self.pages["suppliers"] = self.create_modern_suppliers_page()
        self.pages["sales"] = self.create_modern_sales_page()
        self.pages["financial"] = self.create_modern_financial_page()
        self.pages["reports"] = self.create_modern_reports_page()
        self.pages["settings"] = self.create_modern_settings_page()

        # عرض لوحة التحكم افتراضياً
        self.show_page_content("dashboard")

    def create_modern_dashboard(self):
        """إنشاء لوحة التحكم العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="مرحباً بك في Phone Doctor",
            subtitle="نظرة شاملة على أداء محلك وإحصائيات اليوم",
            actions=[
                {
                    'text': '🔄 تحديث البيانات',
                    'command': self.load_dashboard_data,
                    'variant': 'secondary'
                },
                {
                    'text': '💾 نسخة احتياطية',
                    'command': self.backup_database,
                    'variant': 'primary'
                }
            ]
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # شبكة البطاقات الإحصائية
        stats_grid = tk.Frame(
            page_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        stats_grid.pack(fill=tk.X, pady=(0, 24))

        # الصف الأول من البطاقات
        row1_frame = tk.Frame(
            stats_grid,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        row1_frame.pack(fill=tk.X, pady=(0, 16))

        # بطاقة المبيعات اليومية
        sales_card = self.create_stat_card(
            row1_frame,
            "💰",
            "المبيعات اليوم",
            "0.00 ₪",
            self.styles.colors['success'],
            "زيادة 12% عن أمس"
        )
        sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # بطاقة الصيانات الجارية
        repairs_card = self.create_stat_card(
            row1_frame,
            "🔧",
            "صيانات جارية",
            "0",
            self.styles.colors['info'],
            "3 جاهزة للتسليم"
        )
        repairs_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=4)

        # بطاقة تنبيهات المخزون
        inventory_card = self.create_stat_card(
            row1_frame,
            "📦",
            "تنبيهات المخزون",
            "0",
            self.styles.colors['warning'],
            "قطع تحتاج إعادة تموين"
        )
        inventory_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # الصف الثاني من البطاقات
        row2_frame = tk.Frame(
            stats_grid,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        row2_frame.pack(fill=tk.X)

        # بطاقة الشيكات المستحقة
        checks_card = self.create_stat_card(
            row2_frame,
            "💳",
            "شيكات مستحقة",
            "0",
            self.styles.colors['danger'],
            "تحتاج متابعة"
        )
        checks_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # بطاقة العملاء الجدد
        customers_card = self.create_stat_card(
            row2_frame,
            "👥",
            "عملاء جدد",
            "0",
            self.styles.colors['accent'],
            "هذا الشهر"
        )
        customers_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=4)

        # بطاقة الأرباح الشهرية
        profit_card = self.create_stat_card(
            row2_frame,
            "📈",
            "الأرباح الشهرية",
            "0.00 ₪",
            self.styles.colors['primary'],
            "نمو مستمر"
        )
        profit_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # حفظ مراجع البطاقات للتحديث
        self.stat_cards = {
            'daily_sales': sales_card,
            'active_repairs': repairs_card,
            'low_stock': inventory_card,
            'due_checks': checks_card,
            'new_customers': customers_card,
            'monthly_profit': profit_card
        }

        # قسم الأعمال السريعة والأنشطة
        bottom_section = tk.Frame(
            page_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        bottom_section.pack(fill=tk.BOTH, expand=True)

        # الأعمال السريعة
        quick_actions_card = ModernCard(bottom_section, title="الأعمال السريعة")
        quick_actions_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 12))

        self.create_quick_actions(quick_actions_card.get_content_frame())

        # الأنشطة الأخيرة
        activities_card = ModernCard(bottom_section, title="الأنشطة الأخيرة")
        activities_card.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(12, 0))

        self.create_recent_activities(activities_card.get_content_frame())

        return page_frame

    def create_stat_card(self, parent, icon, title, value, color, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(
            parent,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )

        # إطار المحتوى
        content_frame = tk.Frame(
            card,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الصف العلوي - الأيقونة والقيمة
        top_frame = tk.Frame(
            content_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        top_frame.pack(fill=tk.X, pady=(0, 12))

        # الأيقونة مع خلفية ملونة
        icon_bg = tk.Frame(
            top_frame,
            bg=color,
            width=48,
            height=48,
            relief='flat',
            bd=0
        )
        icon_bg.pack(side=tk.LEFT)
        icon_bg.pack_propagate(False)

        icon_label = tk.Label(
            icon_bg,
            text=icon,
            bg=color,
            fg=self.styles.colors['text_white'],
            font=('Segoe UI Emoji', 18),
            anchor='center'
        )
        icon_label.pack(expand=True)

        # القيمة
        value_label = tk.Label(
            top_frame,
            text=value,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=('Segoe UI', 24, 'bold'),
            anchor='e'
        )
        value_label.pack(side=tk.RIGHT)

        # العنوان
        title_label = tk.Label(
            content_frame,
            text=title,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small'],
            anchor='w'
        )
        title_label.pack(fill=tk.X, pady=(0, 4))

        # العنوان الفرعي
        subtitle_label = tk.Label(
            content_frame,
            text=subtitle,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['caption'],
            anchor='w'
        )
        subtitle_label.pack(fill=tk.X)

        # حفظ مراجع للتحديث
        card.value_label = value_label
        card.subtitle_label = subtitle_label

        return card

    def create_quick_actions(self, parent):
        """إنشاء الأعمال السريعة"""
        # شبكة الأزرار
        buttons_grid = tk.Frame(
            parent,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        buttons_grid.pack(fill=tk.BOTH, expand=True)

        # قائمة الأعمال السريعة
        actions = [
            ("🔧", "إضافة جهاز للصيانة", self.new_repair, 'primary'),
            ("💰", "تسجيل بيع جديد", self.new_sale, 'success'),
            ("📦", "إضافة قطعة للمخزون", self.new_inventory_item, 'info'),
            ("🏪", "إضافة مورد جديد", self.new_supplier, 'secondary'),
            ("📊", "عرض التقارير", lambda: self.on_page_change('reports'), 'warning'),
            ("💾", "نسخة احتياطية", self.backup_database, 'danger')
        ]

        # إنشاء الأزرار في شبكة 2x3
        for i, (icon, text, command, variant) in enumerate(actions):
            row = i // 2
            col = i % 2

            btn_frame = tk.Frame(
                buttons_grid,
                bg=self.styles.colors['bg_card'],
                relief='flat',
                bd=0
            )
            btn_frame.grid(row=row, column=col, padx=8, pady=8, sticky="ew")

            # الزر
            btn = tk.Button(
                btn_frame,
                text=f"{icon}\n{text}",
                command=command,
                **self.styles.get_button_style(variant),
                font=('Segoe UI', 10, 'bold'),
                compound=tk.TOP,
                width=20,
                height=3
            )
            btn.pack(fill=tk.BOTH, expand=True)

        # تكوين الأعمدة
        buttons_grid.columnconfigure(0, weight=1)
        buttons_grid.columnconfigure(1, weight=1)

    def create_recent_activities(self, parent):
        """إنشاء الأنشطة الأخيرة"""
        # إطار قائمة الأنشطة
        self.activities_frame = tk.Frame(
            parent,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        self.activities_frame.pack(fill=tk.BOTH, expand=True)

        # رسالة افتراضية
        no_activities = tk.Label(
            self.activities_frame,
            text="لا توجد أنشطة حديثة",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['body_medium'],
            anchor='center'
        )
        no_activities.pack(expand=True)

    def update_stat_card(self, card_key, value, subtitle=None):
        """تحديث بطاقة إحصائية"""
        if card_key in self.stat_cards:
            card = self.stat_cards[card_key]
            card.value_label.configure(text=str(value))
            if subtitle:
                card.subtitle_label.configure(text=subtitle)

    def create_modern_repairs_page(self):
        """إنشاء صفحة الصيانة العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="إدارة الصيانة",
            subtitle="تتبع وإدارة جميع عمليات صيانة الأجهزة",
            actions=[
                {
                    'text': '🔧 إضافة جهاز جديد',
                    'command': self.new_repair,
                    'variant': 'primary'
                },
                {
                    'text': '🔄 تحديث الحالة',
                    'command': self.update_repair_status,
                    'variant': 'secondary'
                }
            ]
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # بطاقة قائمة الصيانات
        repairs_card = ModernCard(page_frame, title="قائمة الصيانات")
        repairs_card.pack(fill=tk.BOTH, expand=True)

        # جدول الصيانات
        content_frame = repairs_card.get_content_frame()

        # إطار الجدول
        table_frame = tk.Frame(
            content_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        table_frame.pack(fill=tk.BOTH, expand=True)

        # جدول الصيانات
        columns = ("ID", "العميل", "نوع الجهاز", "المشكلة", "الحالة", "التاريخ")
        self.repairs_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15,
            style='Modern.Treeview'
        )

        for col in columns:
            self.repairs_tree.heading(col, text=col)
            self.repairs_tree.column(col, width=150)

        # شريط التمرير للجدول
        repairs_scrollbar = ttk.Scrollbar(
            table_frame,
            orient=tk.VERTICAL,
            command=self.repairs_tree.yview,
            style='Modern.Vertical.TScrollbar'
        )
        self.repairs_tree.configure(yscrollcommand=repairs_scrollbar.set)

        self.repairs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        repairs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        return page_frame

    def create_modern_inventory_page(self):
        """إنشاء صفحة المخزون العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="إدارة المخزون",
            subtitle="تتبع وإدارة قطع الغيار والمخزون",
            actions=[
                {
                    'text': '📦 إضافة قطعة جديدة',
                    'command': self.new_inventory_item,
                    'variant': 'primary'
                },
                {
                    'text': '🔧 إدارة المخزون',
                    'command': self.open_inventory_management,
                    'variant': 'secondary'
                }
            ]
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # إحصائيات المخزون
        stats_frame = tk.Frame(
            page_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        stats_frame.pack(fill=tk.X, pady=(0, 24))

        # بطاقات الإحصائيات
        total_items_card = self.create_stat_card(
            stats_frame,
            "📦",
            "إجمالي القطع",
            "0",
            self.styles.colors['info'],
            "في المخزون"
        )
        total_items_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        low_stock_card = self.create_stat_card(
            stats_frame,
            "⚠️",
            "مخزون منخفض",
            "0",
            self.styles.colors['warning'],
            "تحتاج إعادة تموين"
        )
        low_stock_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=4)

        inventory_value_card = self.create_stat_card(
            stats_frame,
            "💰",
            "قيمة المخزون",
            "0.00 ₪",
            self.styles.colors['success'],
            "القيمة الإجمالية"
        )
        inventory_value_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # حفظ مراجع البطاقات
        self.inventory_stat_cards = {
            'total_items': total_items_card,
            'low_stock': low_stock_card,
            'inventory_value': inventory_value_card
        }

        # بطاقة تنبيهات المخزون
        alerts_card = ModernCard(page_frame, title="تنبيهات المخزون المنخفض")
        alerts_card.pack(fill=tk.BOTH, expand=True)

        # جدول التنبيهات
        content_frame = alerts_card.get_content_frame()

        columns = ("اسم القطعة", "الكمية الحالية", "الحد الأدنى", "المطلوب")
        self.low_stock_tree = ttk.Treeview(
            content_frame,
            columns=columns,
            show="headings",
            height=10,
            style='Modern.Treeview'
        )

        for col in columns:
            self.low_stock_tree.heading(col, text=col)
            self.low_stock_tree.column(col, width=150)

        # شريط التمرير
        low_stock_scrollbar = ttk.Scrollbar(
            content_frame,
            orient=tk.VERTICAL,
            command=self.low_stock_tree.yview,
            style='Modern.Vertical.TScrollbar'
        )
        self.low_stock_tree.configure(yscrollcommand=low_stock_scrollbar.set)

        self.low_stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        low_stock_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        return page_frame

    def create_modern_suppliers_page(self):
        """إنشاء صفحة الموردين العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="إدارة الموردين",
            subtitle="إدارة قاعدة بيانات الموردين والمشتريات",
            actions=[
                {
                    'text': '🏪 إضافة مورد جديد',
                    'command': self.new_supplier,
                    'variant': 'primary'
                },
                {
                    'text': '🔧 إدارة الموردين',
                    'command': self.open_suppliers_management,
                    'variant': 'secondary'
                }
            ]
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # إحصائيات الموردين
        stats_frame = tk.Frame(
            page_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )
        stats_frame.pack(fill=tk.X, pady=(0, 24))

        # بطاقات الإحصائيات
        total_suppliers_card = self.create_stat_card(
            stats_frame,
            "🏪",
            "إجمالي الموردين",
            "0",
            self.styles.colors['primary'],
            "موردين نشطين"
        )
        total_suppliers_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 16))

        total_balance_card = self.create_stat_card(
            stats_frame,
            "💰",
            "إجمالي الأرصدة",
            "0.00 ₪",
            self.styles.colors['success'],
            "أرصدة الموردين"
        )
        total_balance_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(16, 0))

        # حفظ مراجع البطاقات
        self.suppliers_stat_cards = {
            'total_suppliers': total_suppliers_card,
            'total_balance': total_balance_card
        }

        # بطاقة قائمة الموردين
        suppliers_card = ModernCard(page_frame, title="قائمة الموردين")
        suppliers_card.pack(fill=tk.BOTH, expand=True)

        # جدول الموردين
        content_frame = suppliers_card.get_content_frame()

        columns = ("اسم المورد", "الشركة", "الهاتف", "الرصيد")
        self.suppliers_tree = ttk.Treeview(
            content_frame,
            columns=columns,
            show="headings",
            height=12,
            style='Modern.Treeview'
        )

        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            if col == "اسم المورد":
                self.suppliers_tree.column(col, width=200)
            elif col == "الشركة":
                self.suppliers_tree.column(col, width=200)
            elif col == "الرصيد":
                self.suppliers_tree.column(col, width=100)
            else:
                self.suppliers_tree.column(col, width=150)

        # شريط التمرير
        suppliers_scrollbar = ttk.Scrollbar(
            content_frame,
            orient=tk.VERTICAL,
            command=self.suppliers_tree.yview,
            style='Modern.Vertical.TScrollbar'
        )
        self.suppliers_tree.configure(yscrollcommand=suppliers_scrollbar.set)

        self.suppliers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        suppliers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        return page_frame

    def create_modern_sales_page(self):
        """إنشاء صفحة المبيعات العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="إدارة المبيعات",
            subtitle="تتبع وإدارة جميع عمليات البيع والفواتير"
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # رسالة قيد التطوير
        dev_card = ModernCard(page_frame, title="قيد التطوير")
        dev_card.pack(fill=tk.BOTH, expand=True)

        content_frame = dev_card.get_content_frame()

        dev_label = tk.Label(
            content_frame,
            text="🚧 هذا القسم قيد التطوير\nسيتم إضافة المزيد من الميزات قريباً",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['heading_small'],
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)

        return page_frame

    def create_modern_financial_page(self):
        """إنشاء صفحة الشؤون المالية العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="الشؤون المالية",
            subtitle="إدارة الشيكات والفواتير والمصاريف"
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # رسالة قيد التطوير
        dev_card = ModernCard(page_frame, title="قيد التطوير")
        dev_card.pack(fill=tk.BOTH, expand=True)

        content_frame = dev_card.get_content_frame()

        dev_label = tk.Label(
            content_frame,
            text="🚧 هذا القسم قيد التطوير\nسيتم إضافة المزيد من الميزات قريباً",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['heading_small'],
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)

        return page_frame

    def create_modern_reports_page(self):
        """إنشاء صفحة التقارير العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="التقارير والإحصائيات",
            subtitle="تقارير شاملة وإحصائيات مفصلة عن الأداء"
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # رسالة قيد التطوير
        dev_card = ModernCard(page_frame, title="قيد التطوير")
        dev_card.pack(fill=tk.BOTH, expand=True)

        content_frame = dev_card.get_content_frame()

        dev_label = tk.Label(
            content_frame,
            text="🚧 هذا القسم قيد التطوير\nسيتم إضافة المزيد من الميزات قريباً",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['heading_small'],
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)

        return page_frame

    def create_modern_settings_page(self):
        """إنشاء صفحة الإعدادات العصرية"""
        page_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.styles.colors['bg_secondary'],
            relief='flat',
            bd=0
        )

        # رأس الصفحة
        page_header = ModernPageHeader(
            page_frame,
            title="الإعدادات والتخصيص",
            subtitle="تخصيص البرنامج وإعدادات النظام"
        )
        page_header.pack(fill=tk.X, pady=(0, 24))

        # إعدادات المحل
        shop_card = ModernCard(page_frame, title="إعدادات المحل")
        shop_card.pack(fill=tk.X, pady=(0, 16))

        shop_content = shop_card.get_content_frame()

        # اسم المحل
        shop_frame = tk.Frame(
            shop_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        shop_frame.pack(fill=tk.X, pady=(0, 16))

        shop_label = tk.Label(
            shop_frame,
            text="اسم المحل:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium'],
            anchor='w'
        )
        shop_label.pack(side=tk.LEFT, padx=(0, 12))

        self.shop_name_var = tk.StringVar(value=self.config.get('shop.name', ''))
        shop_entry = tk.Entry(
            shop_frame,
            textvariable=self.shop_name_var,
            **self.styles.get_entry_style(),
            width=40
        )
        shop_entry.pack(side=tk.LEFT, padx=(0, 12))

        save_btn = tk.Button(
            shop_frame,
            text="حفظ",
            command=self.save_shop_settings,
            **self.styles.get_button_style('primary')
        )
        save_btn.pack(side=tk.LEFT)

        # إعدادات قاعدة البيانات
        db_card = ModernCard(page_frame, title="إعدادات قاعدة البيانات")
        db_card.pack(fill=tk.X)

        db_content = db_card.get_content_frame()

        # أزرار النسخ الاحتياطي
        backup_frame = tk.Frame(
            db_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        backup_frame.pack(fill=tk.X)

        backup_btn = tk.Button(
            backup_frame,
            text="💾 إنشاء نسخة احتياطية",
            command=self.backup_database,
            **self.styles.get_button_style('primary')
        )
        backup_btn.pack(side=tk.LEFT, padx=(0, 12))

        restore_btn = tk.Button(
            backup_frame,
            text="📁 استعادة نسخة احتياطية",
            command=self.restore_database,
            **self.styles.get_button_style('secondary')
        )
        restore_btn.pack(side=tk.LEFT)

        return page_frame
    

    

    

    

    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            if not self.db_manager.connect():
                return

            # التحقق من وجود البطاقات
            if not hasattr(self, 'stat_cards'):
                return

            # المبيعات اليومية
            daily_sales = self.get_daily_sales()
            self.update_stat_card('daily_sales', f"{daily_sales:.2f} ₪",
                                f"{'زيادة' if daily_sales > 0 else 'لا توجد مبيعات'}")

            # الصيانات الجارية
            active_repairs = self.get_active_repairs()
            self.update_stat_card('active_repairs', str(active_repairs),
                                f"{active_repairs} جهاز قيد الصيانة")

            # تنبيهات المخزون
            low_stock = self.get_low_stock_count()
            self.update_stat_card('low_stock', str(low_stock),
                                f"{'تحتاج إعادة تموين' if low_stock > 0 else 'المخزون جيد'}")

            # الشيكات المستحقة
            due_checks = self.get_due_checks()
            self.update_stat_card('due_checks', str(due_checks),
                                f"{'تحتاج متابعة' if due_checks > 0 else 'لا توجد شيكات مستحقة'}")

            # العملاء الجدد
            new_customers = self.get_new_customers()
            self.update_stat_card('new_customers', str(new_customers),
                                f"عميل جديد هذا الشهر")

            # الأرباح الشهرية
            monthly_profit = self.get_monthly_profit()
            self.update_stat_card('monthly_profit', f"{monthly_profit:.2f} ₪",
                                f"{'نمو مستمر' if monthly_profit > 0 else 'لا توجد أرباح'}")

            # تحديث جدول الصيانات إذا كان موجوداً
            if hasattr(self, 'repairs_tree'):
                self.refresh_repairs()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def get_daily_sales(self):
        """الحصول على المبيعات اليومية"""
        query = "SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE DATE(sale_date) = DATE('now')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_active_repairs(self):
        """الحصول على عدد الصيانات الجارية"""
        query = "SELECT COUNT(*) FROM repairs WHERE repair_status IN ('قيد الانتظار', 'جاري الإصلاح')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_low_stock_count(self):
        """الحصول على عدد القطع منخفضة المخزون"""
        query = "SELECT COUNT(*) FROM inventory WHERE quantity <= min_quantity"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_due_checks(self):
        """الحصول على عدد الشيكات المستحقة"""
        query = "SELECT COUNT(*) FROM checks WHERE due_date <= DATE('now') AND status = 'معلق'"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_new_customers(self):
        """الحصول على عدد العملاء الجدد هذا الشهر"""
        query = "SELECT COUNT(*) FROM customers WHERE DATE(created_at) >= DATE('now', 'start of month')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def get_monthly_profit(self):
        """الحصول على الأرباح الشهرية"""
        query = "SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE DATE(sale_date) >= DATE('now', 'start of month')"
        result = self.db_manager.fetch_query(query)
        return result[0][list(result[0].keys())[0]] if result else 0
    
    def refresh_repairs(self):
        """تحديث جدول الصيانات"""
        # مسح البيانات الحالية
        for item in self.repairs_tree.get_children():
            self.repairs_tree.delete(item)
        
        # جلب البيانات الجديدة
        query = """
        SELECT r.id, c.name, r.device_type, r.problem_description, 
               r.repair_status, r.date_received
        FROM repairs r
        LEFT JOIN customers c ON r.customer_id = c.id
        ORDER BY r.date_received DESC
        """
        
        repairs = self.db_manager.fetch_query(query)
        for repair in repairs:
            self.repairs_tree.insert("", "end", values=(
                repair['id'],
                repair['name'] or 'غير محدد',
                repair['device_type'],
                repair['problem_description'][:50] + '...' if len(repair['problem_description']) > 50 else repair['problem_description'],
                repair['repair_status'],
                repair['date_received']
            ))
    
    def new_repair(self):
        """إضافة جهاز جديد للصيانة"""
        dialog = NewRepairDialog(self.root, self.db_manager)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
    
    def new_sale(self):
        """إضافة بيع جديد"""
        messagebox.showinfo("قيد التطوير", "نافذة البيع الجديد قيد التطوير")
    
    def new_inventory_item(self):
        """إضافة قطعة جديدة للمخزون"""
        dialog = NewInventoryItemDialog(self.root, self.db_manager)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
            self.refresh_inventory_summary()  # تحديث ملخص المخزون
    
    def new_supplier(self):
        """إضافة مورد جديد"""
        dialog = NewSupplierDialog(self.root, self.db_manager)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
            self.refresh_suppliers_summary()  # تحديث ملخص الموردين
    
    def update_repair_status(self):
        """تحديث حالة الصيانة"""
        selected_item = self.repairs_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صيانة من القائمة!")
            return

        # الحصول على معرف الصيانة
        repair_id = self.repairs_tree.item(selected_item[0])['values'][0]

        dialog = UpdateRepairStatusDialog(self.root, self.db_manager, repair_id)
        if dialog.show():
            self.load_dashboard_data()  # تحديث البيانات
    
    def save_shop_settings(self):
        """حفظ إعدادات المحل"""
        shop_name = self.shop_name_var.get().strip()
        if shop_name:
            self.config.set('shop.name', shop_name)
            messagebox.showinfo("تم الحفظ", "تم حفظ إعدادات المحل بنجاح!")
        else:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المحل!")
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ النسخة الاحتياطية",
            defaultextension=".db",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.db_manager.backup_database(file_path):
                messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية!")
    
    def restore_database(self):
        """استعادة نسخة احتياطية"""
        file_path = filedialog.askopenfilename(
            title="اختيار النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if file_path:
            result = messagebox.askyesno(
                "تأكيد", 
                "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية!"
            )
            
            if result:
                if self.db_manager.restore_database(file_path):
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح!")
                    self.load_dashboard_data()
                else:
                    messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية!")
    
    def run(self):
        """تشغيل التطبيق"""
        # إعداد إغلاق التطبيق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # تشغيل الحلقة الرئيسية
        self.root.mainloop()
    
    def open_inventory_management(self):
        """فتح نافذة إدارة المخزون"""
        inventory_window = InventoryManagementWindow(self.root, self.db_manager)

    def refresh_inventory_summary(self):
        """تحديث ملخص المخزون"""
        try:
            # التحقق من وجود البطاقات
            if not hasattr(self, 'inventory_stat_cards'):
                return

            # إجمالي القطع
            total_query = "SELECT COUNT(*) as count FROM inventory"
            total_result = self.db_manager.fetch_query(total_query)
            total_items = total_result[0]['count'] if total_result else 0

            # القطع المنخفضة المخزون
            low_stock_query = "SELECT COUNT(*) as count FROM inventory WHERE quantity <= min_quantity"
            low_stock_result = self.db_manager.fetch_query(low_stock_query)
            low_stock_count = low_stock_result[0]['count'] if low_stock_result else 0

            # قيمة المخزون
            value_query = "SELECT SUM(quantity * purchase_price) as total_value FROM inventory"
            value_result = self.db_manager.fetch_query(value_query)
            total_value = value_result[0]['total_value'] if value_result and value_result[0]['total_value'] else 0

            # تحديث البطاقات
            self.inventory_stat_cards['total_items'].value_label.config(text=str(total_items))
            self.inventory_stat_cards['low_stock'].value_label.config(text=str(low_stock_count))
            self.inventory_stat_cards['inventory_value'].value_label.config(text=f"{total_value:.2f} ₪")

            # تحديث قائمة التنبيهات
            self.refresh_low_stock_alerts()

        except Exception as e:
            print(f"خطأ في تحديث ملخص المخزون: {e}")

    def refresh_low_stock_alerts(self):
        """تحديث تنبيهات المخزون المنخفض"""
        # مسح البيانات الحالية
        for item in self.low_stock_tree.get_children():
            self.low_stock_tree.delete(item)

        try:
            # جلب القطع المنخفضة المخزون
            query = """
            SELECT item_name, quantity, min_quantity, (min_quantity - quantity + 5) as needed
            FROM inventory
            WHERE quantity <= min_quantity
            ORDER BY (quantity - min_quantity) ASC
            """

            low_stock_items = self.db_manager.fetch_query(query)
            for item in low_stock_items:
                needed = max(0, item['needed'])
                self.low_stock_tree.insert("", "end", values=(
                    item['item_name'],
                    item['quantity'],
                    item['min_quantity'],
                    needed
                ))

        except Exception as e:
            print(f"خطأ في تحديث تنبيهات المخزون: {e}")

    def open_suppliers_management(self):
        """فتح نافذة إدارة الموردين"""
        suppliers_window = SuppliersManagementWindow(self.root, self.db_manager)

    def refresh_suppliers_summary(self):
        """تحديث ملخص الموردين"""
        try:
            # التحقق من وجود البطاقات
            if not hasattr(self, 'suppliers_stat_cards'):
                return

            # إجمالي الموردين
            total_query = "SELECT COUNT(*) as count FROM suppliers"
            total_result = self.db_manager.fetch_query(total_query)
            total_suppliers = total_result[0]['count'] if total_result else 0

            # إجمالي الأرصدة
            balance_query = "SELECT SUM(balance) as total_balance FROM suppliers"
            balance_result = self.db_manager.fetch_query(balance_query)
            total_balance = balance_result[0]['total_balance'] if balance_result and balance_result[0]['total_balance'] else 0

            # تحديث البطاقات
            self.suppliers_stat_cards['total_suppliers'].value_label.config(text=str(total_suppliers))
            self.suppliers_stat_cards['total_balance'].value_label.config(text=f"{total_balance:.2f} ₪")

            # تحديث قائمة الموردين
            self.refresh_suppliers_list()

        except Exception as e:
            print(f"خطأ في تحديث ملخص الموردين: {e}")

    def refresh_suppliers_list(self):
        """تحديث قائمة الموردين"""
        # مسح البيانات الحالية
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)

        try:
            # جلب الموردين
            query = """
            SELECT name, company, phone, balance
            FROM suppliers
            ORDER BY name
            """

            suppliers = self.db_manager.fetch_query(query)
            for supplier in suppliers:
                self.suppliers_tree.insert("", "end", values=(
                    supplier['name'],
                    supplier['company'] or '',
                    supplier['phone'] or '',
                    f"{supplier['balance']:.2f}"
                ))

        except Exception as e:
            print(f"خطأ في تحديث قائمة الموردين: {e}")

    def on_closing(self):
        """معالجة إغلاق التطبيق"""
        if messagebox.askokcancel("خروج", "هل تريد إغلاق البرنامج؟"):
            self.db_manager.disconnect()
            self.root.destroy()

def main():
    """الدالة الرئيسية"""
    try:
        app = PhoneDoctorApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")

if __name__ == "__main__":
    main()
