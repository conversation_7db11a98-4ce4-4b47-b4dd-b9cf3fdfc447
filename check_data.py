#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص البيانات في قاعدة البيانات
"""

import sqlite3
import os

def check_data():
    """فحص البيانات"""
    try:
        if not os.path.exists('database/phone_doctor.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return
        
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📊 الجداول الموجودة: {len(tables)}")
        for table in tables:
            print(f"  - {table[0]}")
        
        # فحص البيانات
        try:
            cursor.execute("SELECT COUNT(*) FROM repairs")
            repairs_count = cursor.fetchone()[0]
            print(f"\n🔧 أوامر الصيانة: {repairs_count}")
            
            if repairs_count > 0:
                cursor.execute("SELECT id, customer_name, device_type FROM repairs LIMIT 3")
                repairs = cursor.fetchall()
                for repair in repairs:
                    print(f"  - {repair[0]}: {repair[1]} - {repair[2]}")
        except Exception as e:
            print(f"❌ خطأ في جدول الصيانة: {e}")
        
        try:
            cursor.execute("SELECT COUNT(*) FROM customers")
            customers_count = cursor.fetchone()[0]
            print(f"\n👥 العملاء: {customers_count}")
            
            if customers_count > 0:
                cursor.execute("SELECT id, name, phone FROM customers LIMIT 3")
                customers = cursor.fetchall()
                for customer in customers:
                    print(f"  - {customer[0]}: {customer[1]} - {customer[2]}")
        except Exception as e:
            print(f"❌ خطأ في جدول العملاء: {e}")
        
        try:
            cursor.execute("SELECT COUNT(*) FROM checks")
            checks_count = cursor.fetchone()[0]
            print(f"\n💳 الشيكات: {checks_count}")
            
            if checks_count > 0:
                cursor.execute("SELECT id, check_number, amount FROM checks LIMIT 3")
                checks = cursor.fetchall()
                for check in checks:
                    print(f"  - {check[0]}: {check[1]} - {check[2]} ₪")
        except Exception as e:
            print(f"❌ خطأ في جدول الشيكات: {e}")
        
        conn.close()
        print("\n✅ فحص البيانات مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    check_data()
