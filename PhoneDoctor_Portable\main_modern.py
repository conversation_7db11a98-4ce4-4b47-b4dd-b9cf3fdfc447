#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة محلات صيانة الهواتف - النسخة العصرية
Phone Doctor - Modern Professional Phone Repair Shop Management System

المطور: محمد الشوامرة
الهاتف: 0566000140
جميع الحقوق محفوظة © 2024
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المطلوبة
try:
    from main_tkinter import PhoneDoctorApp
    print("✅ تم تحميل جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    messagebox.showerror("خطأ", f"فشل في تحميل الوحدات المطلوبة:\n{e}")
    sys.exit(1)

def show_splash_screen():
    """عرض شاشة البداية العصرية"""
    splash = tk.Toplevel()
    splash.title("Phone Doctor")
    splash.geometry("500x350")
    splash.resizable(False, False)
    splash.configure(bg='#1e293b')

    # توسيط الشاشة
    splash.update_idletasks()
    x = (splash.winfo_screenwidth() // 2) - (500 // 2)
    y = (splash.winfo_screenheight() // 2) - (350 // 2)
    splash.geometry(f"500x350+{x}+{y}")

    # إزالة شريط العنوان
    splash.overrideredirect(True)

    # إطار رئيسي مع حدود مدورة
    main_frame = tk.Frame(
        splash,
        bg='#1e293b',
        relief='flat',
        bd=0
    )
    main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

    # إطار المحتوى
    content_frame = tk.Frame(
        main_frame,
        bg='#2563eb',
        relief='flat',
        bd=0
    )
    content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

    # شعار التطبيق
    logo_label = tk.Label(
        content_frame,
        text="📱",
        bg='#2563eb',
        fg='white',
        font=('Segoe UI Emoji', 48)
    )
    logo_label.pack(pady=(40, 20))

    # اسم التطبيق
    app_name = tk.Label(
        content_frame,
        text="Phone Doctor",
        bg='#2563eb',
        fg='white',
        font=('Segoe UI', 24, 'bold')
    )
    app_name.pack(pady=(0, 10))

    # وصف التطبيق
    app_desc = tk.Label(
        content_frame,
        text="نظام إدارة محلات صيانة الهواتف الاحترافي",
        bg='#2563eb',
        fg='#e2e8f0',
        font=('Segoe UI', 12)
    )
    app_desc.pack(pady=(0, 30))

    # شريط التقدم
    progress_frame = tk.Frame(
        content_frame,
        bg='#2563eb',
        relief='flat',
        bd=0
    )
    progress_frame.pack(fill=tk.X, padx=40, pady=(0, 20))

    progress_bg = tk.Frame(
        progress_frame,
        bg='#1e40af',
        height=6,
        relief='flat',
        bd=0
    )
    progress_bg.pack(fill=tk.X)

    progress_bar = tk.Frame(
        progress_bg,
        bg='#10b981',
        height=6,
        relief='flat',
        bd=0
    )
    progress_bar.pack(side=tk.LEFT)

    # نص الحالة
    status_label = tk.Label(
        content_frame,
        text="جاري التحميل...",
        bg='#2563eb',
        fg='#cbd5e1',
        font=('Segoe UI', 10)
    )
    status_label.pack(pady=(0, 20))

    # معلومات المطور
    dev_label = tk.Label(
        content_frame,
        text="المطور: محمد الشوامرة - 📞 0566000140",
        bg='#2563eb',
        fg='#94a3b8',
        font=('Segoe UI', 9)
    )
    dev_label.pack(side=tk.BOTTOM, pady=(0, 20))

    # تحديث شريط التقدم
    def update_progress():
        steps = [
            "تحميل قاعدة البيانات...",
            "تحميل الإعدادات...",
            "تحميل الواجهات العصرية...",
            "تحميل الأنماط والثيمات...",
            "إعداد النظام...",
            "اكتمل التحميل!"
        ]

        for i, step in enumerate(steps):
            status_label.config(text=step)
            progress_width = int((i + 1) * 400 / len(steps))
            progress_bar.config(width=progress_width)
            splash.update()
            splash.after(500)  # انتظار 500 مللي ثانية

        splash.after(1000, splash.destroy)

    # بدء التحديث
    splash.after(500, update_progress)

    return splash

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل Phone Doctor - النسخة العصرية")
    print("=" * 60)

    try:
        # إنشاء النافذة الرئيسية (مخفية)
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية مؤقتاً

        # عرض شاشة البداية
        splash = show_splash_screen()
        splash.mainloop()

        # إظهار النافذة الرئيسية
        root.deiconify()

        # إنشاء وتشغيل التطبيق
        app = PhoneDoctorApp()

        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎨 التصميم العصري مُفعَّل")
        print("📱 Phone Doctor جاهز للاستخدام")
        print("👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
        print("=" * 60)

        # تشغيل الحلقة الرئيسية
        app.run()

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل التطبيق:\n{e}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nاضغط Enter للخروج...")
        sys.exit(1)