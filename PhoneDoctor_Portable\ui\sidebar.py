#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الشريط الجانبي - Sidebar
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QFrame, QScrollArea, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

class SidebarButton(QPushButton):
    def __init__(self, icon, text, window_name):
        super().__init__()
        self.window_name = window_name
        self.icon = icon
        self.text = text
        self.is_active = False
        
        self.setFixedHeight(50)
        self.setCheckable(True)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة الزر"""
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # أيقونة
        icon_label = QLabel(self.icon)
        icon_label.setFont(QFont("Arial", 16))
        icon_label.setFixedSize(24, 24)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # نص
        text_label = QLabel(self.text)
        text_label.setFont(QFont("Arial", 11, QFont.Bold))
        
        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def set_active(self, active):
        """تعيين حالة النشاط"""
        self.is_active = active
        self.setChecked(active)

class Sidebar(QWidget):
    menu_clicked = pyqtSignal(str)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.current_theme = self.config.get('ui.theme', 'blue')
        self.active_button = None
        
        self.init_ui()
        self.apply_theme()
        
    def init_ui(self):
        """إعداد واجهة الشريط الجانبي"""
        self.setFixedWidth(250)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # رأس الشريط الجانبي
        header = self.create_header()
        main_layout.addWidget(header)
        
        # منطقة القوائم
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        menu_widget = QWidget()
        menu_layout = QVBoxLayout(menu_widget)
        menu_layout.setContentsMargins(0, 10, 0, 10)
        menu_layout.setSpacing(2)
        
        # أزرار القوائم
        self.menu_buttons = []
        
        menus = [
            ("🏠", "لوحة التحكم", "dashboard"),
            ("🔧", "الصيانة", "repairs"),
            ("📦", "المخزون", "inventory"),
            ("🏪", "الموردين", "suppliers"),
            ("💰", "المبيعات", "sales"),
            ("💳", "الشؤون المالية", "financial"),
            ("📊", "التقارير", "reports"),
            ("⚙️", "الإعدادات", "settings")
        ]
        
        for icon, text, window_name in menus:
            button = SidebarButton(icon, text, window_name)
            button.clicked.connect(lambda checked, name=window_name: self.on_menu_clicked(name))
            self.menu_buttons.append(button)
            menu_layout.addWidget(button)
        
        # تعيين الزر الأول كنشط
        if self.menu_buttons:
            self.set_active_button(self.menu_buttons[0])
        
        menu_layout.addStretch()
        scroll_area.setWidget(menu_widget)
        main_layout.addWidget(scroll_area)
        
        # تذييل الشريط الجانبي
        footer = self.create_footer()
        main_layout.addWidget(footer)
        
        self.setLayout(main_layout)
        
    def create_header(self):
        """إنشاء رأس الشريط الجانبي"""
        header = QFrame()
        header.setFixedHeight(80)
        
        layout = QVBoxLayout(header)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        # شعار التطبيق
        logo_layout = QHBoxLayout()
        logo_label = QLabel("📱")
        logo_label.setFont(QFont("Arial", 24))
        logo_label.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel("Phone Doctor")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(title_label)
        logo_layout.addStretch()
        
        # اسم المحل
        shop_name = self.config.get('shop.name', 'محل صيانة الهواتف')
        shop_label = QLabel(shop_name)
        shop_label.setFont(QFont("Arial", 10))
        shop_label.setAlignment(Qt.AlignCenter)
        shop_label.setWordWrap(True)
        
        layout.addLayout(logo_layout)
        layout.addWidget(shop_label)
        
        return header
        
    def create_footer(self):
        """إنشاء تذييل الشريط الجانبي"""
        footer = QFrame()
        footer.setFixedHeight(60)
        
        layout = QVBoxLayout(footer)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(2)
        
        # معلومات المطور
        developer_label = QLabel("المطور: محمد الشوامرة")
        developer_label.setFont(QFont("Arial", 8))
        developer_label.setAlignment(Qt.AlignCenter)
        
        phone_label = QLabel("📞 0566000140")
        phone_label.setFont(QFont("Arial", 8))
        phone_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(developer_label)
        layout.addWidget(phone_label)
        
        return footer
        
    def on_menu_clicked(self, window_name):
        """معالجة النقر على قائمة"""
        # العثور على الزر المنقور
        clicked_button = None
        for button in self.menu_buttons:
            if button.window_name == window_name:
                clicked_button = button
                break
        
        if clicked_button:
            self.set_active_button(clicked_button)
            self.menu_clicked.emit(window_name)
    
    def set_active_button(self, button):
        """تعيين الزر النشط"""
        # إلغاء تنشيط الزر السابق
        if self.active_button:
            self.active_button.set_active(False)
        
        # تنشيط الزر الجديد
        button.set_active(True)
        self.active_button = button
        
        # تطبيق الأنماط
        self.apply_button_styles()
    
    def apply_theme(self):
        """تطبيق الثيم الحالي"""
        colors = self.config.get_theme_colors(self.current_theme)
        
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {colors['surface']};
                color: {colors['text']};
            }}
            QFrame {{
                background-color: {colors['surface']};
                border: none;
            }}
            QScrollArea {{
                background-color: {colors['surface']};
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {colors['background']};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {colors['primary']};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {colors['secondary']};
            }}
        """)
        
        self.apply_button_styles()
    
    def apply_button_styles(self):
        """تطبيق أنماط الأزرار"""
        colors = self.config.get_theme_colors(self.current_theme)
        
        for button in self.menu_buttons:
            if button.is_active:
                # الزر النشط
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 {colors['primary']}, stop:1 {colors['secondary']});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        text-align: left;
                        font-weight: bold;
                        margin: 2px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 {colors['secondary']}, stop:1 {colors['primary']});
                    }}
                    QLabel {{
                        color: white;
                        background: transparent;
                    }}
                """)
            else:
                # الأزرار غير النشطة
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: transparent;
                        color: {colors['text']};
                        border: none;
                        border-radius: 8px;
                        text-align: left;
                        margin: 2px;
                    }}
                    QPushButton:hover {{
                        background-color: {colors['background']};
                        color: {colors['primary']};
                    }}
                    QLabel {{
                        color: {colors['text']};
                        background: transparent;
                    }}
                """)
    
    def update_theme(self, theme_name):
        """تحديث الثيم"""
        self.current_theme = theme_name
        self.apply_theme()
