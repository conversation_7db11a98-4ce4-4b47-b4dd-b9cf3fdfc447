#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - Database Manager
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

class DatabaseManager:
    def __init__(self, db_path: str = "phone_doctor.db"):
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> bool:
        """تنفيذ استعلام (INSERT, UPDATE, DELETE)"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return True
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def fetch_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """تنفيذ استعلام SELECT وإرجاع النتائج"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return []
    
    def initialize_database(self):
        """إنشاء جداول قاعدة البيانات"""
        if not self.connect():
            return False
        
        # جدول الإعدادات العامة
        settings_table = """
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shop_name TEXT NOT NULL DEFAULT 'محل صيانة الهواتف',
            theme_color TEXT DEFAULT 'blue',
            dark_mode INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول العملاء
        customers_table = """
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT NOT NULL,
            address TEXT,
            email TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول الأجهزة والصيانة
        repairs_table = """
        CREATE TABLE IF NOT EXISTS repairs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            device_type TEXT NOT NULL,
            device_model TEXT,
            device_imei TEXT,
            problem_description TEXT NOT NULL,
            repair_status TEXT DEFAULT 'قيد الانتظار',
            technician_notes TEXT,
            estimated_cost REAL DEFAULT 0,
            actual_cost REAL DEFAULT 0,
            date_received DATE DEFAULT CURRENT_DATE,
            date_completed DATE,
            date_delivered DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
        """
        
        # جدول الموردين
        suppliers_table = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            company TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance REAL DEFAULT 0,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول المخزون
        inventory_table = """
        CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_name TEXT NOT NULL,
            barcode TEXT UNIQUE,
            category TEXT,
            supplier_id INTEGER,
            purchase_price REAL DEFAULT 0,
            selling_price REAL DEFAULT 0,
            quantity INTEGER DEFAULT 0,
            min_quantity INTEGER DEFAULT 5,
            location TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
        """
        
        # جدول المبيعات
        sales_table = """
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            item_id INTEGER,
            repair_id INTEGER,
            quantity INTEGER DEFAULT 1,
            unit_price REAL NOT NULL,
            total_amount REAL NOT NULL,
            payment_method TEXT DEFAULT 'نقدي',
            payment_status TEXT DEFAULT 'مدفوع',
            sale_date DATE DEFAULT CURRENT_DATE,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (item_id) REFERENCES inventory (id),
            FOREIGN KEY (repair_id) REFERENCES repairs (id)
        )
        """
        
        # جدول الشيكات
        checks_table = """
        CREATE TABLE IF NOT EXISTS checks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            sale_id INTEGER,
            check_number TEXT NOT NULL,
            bank_name TEXT,
            amount REAL NOT NULL,
            issue_date DATE NOT NULL,
            due_date DATE NOT NULL,
            status TEXT DEFAULT 'معلق',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (sale_id) REFERENCES sales (id)
        )
        """
        
        # جدول المشتريات
        purchases_table = """
        CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER NOT NULL,
            item_id INTEGER,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_amount REAL NOT NULL,
            purchase_date DATE DEFAULT CURRENT_DATE,
            payment_method TEXT DEFAULT 'نقدي',
            payment_status TEXT DEFAULT 'مدفوع',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (item_id) REFERENCES inventory (id)
        )
        """
        
        # جدول المصاريف
        expenses_table = """
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            description TEXT NOT NULL,
            amount REAL NOT NULL,
            expense_date DATE DEFAULT CURRENT_DATE,
            payment_method TEXT DEFAULT 'نقدي',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        tables = [
            settings_table, customers_table, repairs_table, suppliers_table,
            inventory_table, sales_table, checks_table, purchases_table, expenses_table
        ]
        
        try:
            for table in tables:
                self.execute_query(table)
            
            # إدراج الإعدادات الافتراضية
            self.execute_query("""
                INSERT OR IGNORE INTO settings (id, shop_name, theme_color, dark_mode) 
                VALUES (1, 'محل صيانة الهواتف', 'blue', 0)
            """)
            
            print("تم إنشاء قاعدة البيانات بنجاح!")
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    def backup_database(self, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            self.disconnect()
            shutil.copy2(backup_path, self.db_path)
            self.connect()
            return True
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
