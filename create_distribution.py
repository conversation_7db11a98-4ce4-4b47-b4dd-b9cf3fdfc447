#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع - Create Distribution Package
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import os
import shutil
import sys
from pathlib import Path

def create_distribution():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع Phone Doctor...")
    
    # إنشاء مجلد التوزيع
    dist_folder = "PhoneDoctor_Portable"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    print(f"✅ تم إنشاء مجلد التوزيع: {dist_folder}")
    
    # قائمة الملفات المطلوبة
    files_to_copy = [
        "main_tkinter.py",
        "main_modern.py",
        "test_db.py",
        "requirements.txt",
        "run_phone_doctor.bat",
        "run_modern.bat",
        "README.md",
        "README_MODERN.md",
        "دليل_المستخدم.md"
    ]
    
    # قائمة المجلدات المطلوبة
    folders_to_copy = [
        "database",
        "ui", 
        "utils",
        "assets"
    ]
    
    # نسخ الملفات
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, dist_folder)
            print(f"✅ تم نسخ ملف: {file}")
        else:
            print(f"⚠️ ملف غير موجود: {file}")
    
    # نسخ المجلدات
    for folder in folders_to_copy:
        if os.path.exists(folder):
            shutil.copytree(folder, os.path.join(dist_folder, folder))
            print(f"✅ تم نسخ مجلد: {folder}")
        else:
            print(f"⚠️ مجلد غير موجود: {folder}")
    
    # إنشاء قاعدة بيانات نظيفة للتوزيع
    try:
        sys.path.append(dist_folder)
        from database.db_manager import DatabaseManager
        
        dist_db_path = os.path.join(dist_folder, "phone_doctor.db")
        db_manager = DatabaseManager(dist_db_path)
        if db_manager.initialize_database():
            print("✅ تم إنشاء قاعدة البيانات للتوزيع")
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
    
    # إنشاء ملف الإعدادات للتوزيع
    try:
        from utils.config import Config
        
        dist_config_path = os.path.join(dist_folder, "config.json")
        config = Config(dist_config_path)
        config.save_config()
        print("✅ تم إنشاء ملف الإعدادات للتوزيع")
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الإعدادات: {e}")
    
    # إنشاء ملف تشغيل محدث للنسخة العصرية
    modern_run_script = """@echo off
chcp 65001 > nul
title Phone Doctor - النسخة العصرية

echo.
echo ========================================
echo   📱 Phone Doctor - النسخة العصرية
echo   نظام إدارة محلات صيانة الهواتف
echo   المطور: محمد الشوامرة - 0566000140
echo ========================================
echo.

echo التحقق من متطلبات النظام...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام!
    echo يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 🎨 تشغيل النسخة العصرية مع التصميم المتقدم...
echo.

python main_modern.py

if errorlevel 1 (
    echo.
    echo ⚠️ فشل في تشغيل النسخة العصرية!
    echo 🔄 جاري المحاولة مع النسخة العادية...
    echo.
    python main_tkinter.py

    if errorlevel 1 (
        echo.
        echo ❌ فشل في تشغيل البرنامج!
        echo.
        echo تحقق من:
        echo 1. تثبيت Python بشكل صحيح
        echo 2. وجود جميع الملفات المطلوبة
        echo 3. صلاحيات الكتابة في المجلد
        echo.
        echo للدعم الفني: محمد الشوامرة - 0566000140
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح.
echo 🙏 شكراً لاستخدام Phone Doctor!
pause
"""

    # إنشاء ملف تشغيل النسخة العادية
    classic_run_script = """@echo off
chcp 65001 > nul
title Phone Doctor - النسخة التقليدية

echo.
echo ========================================
echo   Phone Doctor - النسخة التقليدية
echo   نظام إدارة محلات صيانة الهواتف
echo   المطور: محمد الشوامرة - 0566000140
echo ========================================
echo.

echo التحقق من متطلبات النظام...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام!
    echo يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo جاري تشغيل النسخة التقليدية...
echo.

python main_tkinter.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج!
    echo.
    echo تحقق من:
    echo 1. تثبيت Python بشكل صحيح
    echo 2. وجود جميع الملفات المطلوبة
    echo 3. صلاحيات الكتابة في المجلد
    echo.
    echo للدعم الفني: محمد الشوامرة - 0566000140
    echo.
    pause
    exit /b 1
)

echo.
echo تم إغلاق البرنامج بنجاح.
echo شكراً لاستخدام Phone Doctor!
pause
"""

    # حفظ ملفات التشغيل
    with open(os.path.join(dist_folder, "تشغيل_النسخة_العصرية.bat"), 'w', encoding='utf-8') as f:
        f.write(modern_run_script)

    with open(os.path.join(dist_folder, "تشغيل_النسخة_التقليدية.bat"), 'w', encoding='utf-8') as f:
        f.write(classic_run_script)
    
    print("✅ تم إنشاء ملفات التشغيل (عصرية + تقليدية)")
    
    # إنشاء ملف README للتوزيع
    readme_content = """
# 📱 Phone Doctor - نظام إدارة محلات صيانة الهواتف

## 🎨 النسخة العصرية الجديدة متاحة الآن!

**المطور:** محمد الشوامرة
**الهاتف:** 0566000140
**جميع الحقوق محفوظة © 2024**

---

## 🚀 طرق التشغيل

### 🌟 النسخة العصرية (موصى بها)
```
تشغيل_النسخة_العصرية.bat
```
- تصميم عصري ومتقدم
- شريط جانبي احترافي
- بطاقات إحصائية جذابة
- تأثيرات بصرية متطورة

### 📋 النسخة التقليدية (احتياطية)
```
تشغيل_النسخة_التقليدية.bat
```
- واجهة تقليدية مستقرة
- أداء سريع
- متوافقة مع جميع الأنظمة

---

## 📋 المتطلبات

- **نظام التشغيل:** Windows 7 أو أحدث
- **Python:** 3.7 أو أحدث
- **المساحة:** 50 ميجابايت

## 🔧 خطوات التثبيت

1. **تثبيت Python:**
   - حمل من: https://python.org
   - تأكد من تحديد "Add Python to PATH"

2. **تشغيل البرنامج:**
   - انقر نقراً مزدوجاً على ملف التشغيل المطلوب
   - أو استخدم: `python main_modern.py`

3. **التشغيل الأول:**
   - سيتم إنشاء قاعدة البيانات تلقائياً
   - يمكن تعديل اسم المحل من الإعدادات

---

## ✨ الميزات الرئيسية

### 🔧 إدارة الصيانات
- تسجيل الأجهزة الجديدة
- تتبع حالة الإصلاح
- إدارة بيانات العملاء
- ملاحظات الفني التفصيلية

### 📦 إدارة المخزون
- إضافة قطع الغيار
- تنبيهات المخزون المنخفض
- نظام باركود متقدم
- تصنيف حسب الفئات

### 🏪 إدارة الموردين
- قاعدة بيانات شاملة
- تتبع الأرصدة والمدفوعات
- معلومات الاتصال الكاملة

### 📊 لوحة التحكم
- إحصائيات مباشرة ومحدثة
- أعمال سريعة للمهام الشائعة
- بطاقات معلومات تفاعلية

### 💾 النسخ الاحتياطي
- إنشاء نسخ احتياطية آمنة
- استعادة البيانات بسهولة
- حماية من فقدان البيانات

---

## 📁 الملفات المهمة

### الملفات الرئيسية
- `main_modern.py` - النسخة العصرية
- `main_tkinter.py` - النسخة التقليدية
- `phone_doctor.db` - قاعدة البيانات
- `config.json` - ملف الإعدادات

### ملفات التشغيل
- `تشغيل_النسخة_العصرية.bat` - تشغيل النسخة العصرية
- `تشغيل_النسخة_التقليدية.bat` - تشغيل النسخة التقليدية

### الوثائق
- `README_MODERN.md` - دليل النسخة العصرية
- `دليل_المستخدم.md` - دليل المستخدم المفصل

---

## 🆘 الدعم الفني

**محمد الشوامرة**
📞 **الهاتف:** 0566000140
💼 **مطور نظم إدارة احترافية**
🎨 **خبير في التصميم العصري**

### ساعات الدعم
- الأحد - الخميس: 9:00 ص - 6:00 م
- الجمعة - السبت: حسب الحاجة

---

## ⚠️ ملاحظات مهمة

🔒 **احرص على إنشاء نسخة احتياطية من البيانات بانتظام**
📁 **لا تحذف ملف قاعدة البيانات phone_doctor.db**
🔧 **تأكد من وجود صلاحيات الكتابة في مجلد البرنامج**
🔄 **استخدم النسخة التقليدية في حالة مشاكل النسخة العصرية**

---

**🎨 Phone Doctor v1.0.0 - النسخة العصرية**
**Professional Modern Phone Repair Shop Management System**
**© 2024 Mohammad Shawamreh. All rights reserved.**
"""
    
    with open(os.path.join(dist_folder, "اقرأني_أولاً.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف التعليمات")
    
    # إنشاء ملف اختبار النظام
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def test_system():
    print("🔧 اختبار متطلبات النظام...")
    print("=" * 40)
    
    # اختبار Python
    print(f"✅ Python {sys.version}")
    
    # اختبار المكتبات
    try:
        import tkinter
        print("✅ Tkinter متوفر")
    except ImportError:
        print("❌ Tkinter غير متوفر")
        return False
    
    try:
        import sqlite3
        print("✅ SQLite3 متوفر")
    except ImportError:
        print("❌ SQLite3 غير متوفر")
        return False
    
    # اختبار الملفات
    required_files = ['main_tkinter.py', 'database/db_manager.py', 'utils/config.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} غير موجود")
            return False
    
    print("=" * 40)
    print("🎉 جميع المتطلبات متوفرة!")
    print("يمكنك الآن تشغيل البرنامج بأمان.")
    
    return True

if __name__ == "__main__":
    test_system()
    input("\\nاضغط Enter للخروج...")
"""
    
    with open(os.path.join(dist_folder, "اختبار_النظام.py"), 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ تم إنشاء ملف اختبار النظام")
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء حزمة التوزيع بنجاح!")
    print(f"📁 المجلد: {dist_folder}")
    print("📋 المحتويات:")
    
    # عرض محتويات المجلد
    for root, dirs, files in os.walk(dist_folder):
        level = root.replace(dist_folder, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}📁 {os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}📄 {file}")
    
    print("\n👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
    print("=" * 50)
    
    return True

def main():
    """الدالة الرئيسية"""
    try:
        return create_distribution()
    except Exception as e:
        print(f"❌ خطأ في إنشاء حزمة التوزيع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
