#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع - Create Distribution Package
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import os
import shutil
import sys
from pathlib import Path

def create_distribution():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع Phone Doctor...")
    
    # إنشاء مجلد التوزيع
    dist_folder = "PhoneDoctor_Portable"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    print(f"✅ تم إنشاء مجلد التوزيع: {dist_folder}")
    
    # قائمة الملفات المطلوبة
    files_to_copy = [
        "main_tkinter.py",
        "test_db.py",
        "requirements.txt",
        "run_phone_doctor.bat",
        "README.md",
        "دليل_المستخدم.md"
    ]
    
    # قائمة المجلدات المطلوبة
    folders_to_copy = [
        "database",
        "ui", 
        "utils",
        "assets"
    ]
    
    # نسخ الملفات
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, dist_folder)
            print(f"✅ تم نسخ ملف: {file}")
        else:
            print(f"⚠️ ملف غير موجود: {file}")
    
    # نسخ المجلدات
    for folder in folders_to_copy:
        if os.path.exists(folder):
            shutil.copytree(folder, os.path.join(dist_folder, folder))
            print(f"✅ تم نسخ مجلد: {folder}")
        else:
            print(f"⚠️ مجلد غير موجود: {folder}")
    
    # إنشاء قاعدة بيانات نظيفة للتوزيع
    try:
        sys.path.append(dist_folder)
        from database.db_manager import DatabaseManager
        
        dist_db_path = os.path.join(dist_folder, "phone_doctor.db")
        db_manager = DatabaseManager(dist_db_path)
        if db_manager.initialize_database():
            print("✅ تم إنشاء قاعدة البيانات للتوزيع")
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
    
    # إنشاء ملف الإعدادات للتوزيع
    try:
        from utils.config import Config
        
        dist_config_path = os.path.join(dist_folder, "config.json")
        config = Config(dist_config_path)
        config.save_config()
        print("✅ تم إنشاء ملف الإعدادات للتوزيع")
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الإعدادات: {e}")
    
    # إنشاء ملف تشغيل محدث
    run_script = """@echo off
chcp 65001 > nul
title Phone Doctor - نظام إدارة محلات صيانة الهواتف

echo.
echo ========================================
echo   Phone Doctor - نظام إدارة محلات صيانة الهواتف
echo   المطور: محمد الشوامرة - 0566000140
echo   جميع الحقوق محفوظة © 2024
echo ========================================
echo.

echo التحقق من متطلبات النظام...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام!
    echo يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo جاري تشغيل البرنامج...
echo.

python main_tkinter.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج!
    echo.
    echo تحقق من:
    echo 1. تثبيت Python بشكل صحيح
    echo 2. وجود جميع الملفات المطلوبة
    echo 3. صلاحيات الكتابة في المجلد
    echo.
    echo للدعم الفني: محمد الشوامرة - 0566000140
    echo.
    pause
    exit /b 1
)

echo.
echo تم إغلاق البرنامج بنجاح.
echo شكراً لاستخدام Phone Doctor!
pause
"""
    
    with open(os.path.join(dist_folder, "تشغيل_البرنامج.bat"), 'w', encoding='utf-8') as f:
        f.write(run_script)
    
    print("✅ تم إنشاء ملف التشغيل المحدث")
    
    # إنشاء ملف README للتوزيع
    readme_content = """
# Phone Doctor - نظام إدارة محلات صيانة الهواتف

## المطور: محمد الشوامرة
## الهاتف: 0566000140
## جميع الحقوق محفوظة © 2024

## تعليمات التشغيل السريع:

### المتطلبات:
- نظام التشغيل: Windows 7 أو أحدث
- Python 3.7 أو أحدث

### خطوات التشغيل:

1. **تثبيت Python (إذا لم يكن مثبتاً):**
   - قم بتحميل Python من: https://python.org
   - تأكد من تحديد "Add Python to PATH" أثناء التثبيت

2. **تشغيل البرنامج:**
   - قم بتشغيل ملف "تشغيل_البرنامج.bat"
   - أو افتح موجه الأوامر واكتب: python main_tkinter.py

3. **عند التشغيل الأول:**
   - سيتم إنشاء قاعدة البيانات تلقائياً
   - يمكنك تعديل اسم المحل من تبويب الإعدادات

## الميزات الرئيسية:

✅ **إدارة الصيانات:**
- تسجيل الأجهزة الجديدة
- تتبع حالة الإصلاح
- إدارة بيانات العملاء

✅ **إدارة المخزون:**
- إضافة قطع الغيار
- تنبيهات المخزون المنخفض
- نظام باركود

✅ **إدارة الموردين:**
- قاعدة بيانات الموردين
- تتبع الأرصدة

✅ **لوحة التحكم:**
- إحصائيات مباشرة
- أعمال سريعة

✅ **النسخ الاحتياطي:**
- حفظ واستعادة البيانات

## الملفات المهمة:

- `main_tkinter.py` - الملف الرئيسي للبرنامج
- `phone_doctor.db` - قاعدة البيانات
- `config.json` - ملف الإعدادات
- `دليل_المستخدم.md` - دليل المستخدم المفصل

## الدعم الفني:

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
📞 محمد الشوامرة - 0566000140

## ملاحظات مهمة:

⚠️ **احرص على إنشاء نسخة احتياطية من البيانات بانتظام**
⚠️ **لا تحذف ملف قاعدة البيانات phone_doctor.db**
⚠️ **تأكد من وجود صلاحيات الكتابة في مجلد البرنامج**

---
Phone Doctor v1.0.0 - Professional Phone Repair Shop Management System
© 2024 Mohammad Shawamreh. All rights reserved.
"""
    
    with open(os.path.join(dist_folder, "اقرأني_أولاً.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف التعليمات")
    
    # إنشاء ملف اختبار النظام
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def test_system():
    print("🔧 اختبار متطلبات النظام...")
    print("=" * 40)
    
    # اختبار Python
    print(f"✅ Python {sys.version}")
    
    # اختبار المكتبات
    try:
        import tkinter
        print("✅ Tkinter متوفر")
    except ImportError:
        print("❌ Tkinter غير متوفر")
        return False
    
    try:
        import sqlite3
        print("✅ SQLite3 متوفر")
    except ImportError:
        print("❌ SQLite3 غير متوفر")
        return False
    
    # اختبار الملفات
    required_files = ['main_tkinter.py', 'database/db_manager.py', 'utils/config.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} غير موجود")
            return False
    
    print("=" * 40)
    print("🎉 جميع المتطلبات متوفرة!")
    print("يمكنك الآن تشغيل البرنامج بأمان.")
    
    return True

if __name__ == "__main__":
    test_system()
    input("\\nاضغط Enter للخروج...")
"""
    
    with open(os.path.join(dist_folder, "اختبار_النظام.py"), 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ تم إنشاء ملف اختبار النظام")
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء حزمة التوزيع بنجاح!")
    print(f"📁 المجلد: {dist_folder}")
    print("📋 المحتويات:")
    
    # عرض محتويات المجلد
    for root, dirs, files in os.walk(dist_folder):
        level = root.replace(dist_folder, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}📁 {os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}📄 {file}")
    
    print("\n👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
    print("=" * 50)
    
    return True

def main():
    """الدالة الرئيسية"""
    try:
        return create_distribution()
    except Exception as e:
        print(f"❌ خطأ في إنشاء حزمة التوزيع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
