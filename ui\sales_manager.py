#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة المبيعات مع عمليات CRUD
Sales Manager with CRUD Operations

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date

class SalesManager:
    """مدير المبيعات مع عمليات الإضافة والتعديل والحذف"""
    
    def __init__(self, parent_frame, db_manager):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.selected_sale = None
        
        # ألوان النظام
        self.colors = {
            'primary': '#2563eb',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'bg_light': '#f8fafc',
            'text_dark': '#1f2937'
        }
        
        self.setup_ui()
        self.load_sales()
    
    def setup_ui(self):
        """إعداد واجهة إدارة المبيعات"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(
            title_frame,
            text="💰 إدارة المبيعات",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        title_label.pack(side=tk.LEFT)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(title_frame, bg='white')
        buttons_frame.pack(side=tk.RIGHT)
        
        # زر بيع جديد
        new_sale_btn = tk.Button(
            buttons_frame,
            text="🛒 بيع جديد",
            command=self.new_sale,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        new_sale_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر عرض تفاصيل
        view_btn = tk.Button(
            buttons_frame,
            text="👁️ عرض التفاصيل",
            command=self.view_sale_details,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        view_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر حذف بيع
        delete_btn = tk.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_sale,
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تحديث
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.load_sales,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.LEFT)
        
        # شريط الفلترة
        filter_frame = tk.Frame(self.parent_frame, bg='white')
        filter_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # فلتر التاريخ
        date_label = tk.Label(
            filter_frame,
            text="📅 التاريخ:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        date_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.date_var = tk.StringVar()
        self.date_var.trace('w', self.on_filter)
        
        date_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.date_var,
            font=('Arial', 11),
            width=15,
            state='readonly'
        )
        date_combo['values'] = ['جميع التواريخ', 'اليوم', 'أمس', 'هذا الأسبوع', 'هذا الشهر']
        date_combo.set('جميع التواريخ')
        date_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        # إجمالي المبيعات
        self.total_label = tk.Label(
            filter_frame,
            text="",
            bg='white',
            fg=self.colors['success'],
            font=('Arial', 12, 'bold')
        )
        self.total_label.pack(side=tk.RIGHT)
        
        # جدول المبيعات
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء الجدول
        columns = ("ID", "التاريخ", "العميل", "المنتجات", "الإجمالي", "طريقة الدفع", "الحالة")
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # تعيين العناوين وعرض الأعمدة
        column_widths = [50, 100, 150, 200, 100, 120, 100]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor='center')
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.view_sale_details)
        
        # شريط الحالة
        status_frame = tk.Frame(self.parent_frame, bg='white')
        status_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 10)
        )
        self.status_label.pack(side=tk.LEFT)
        
        self.count_label = tk.Label(
            status_frame,
            text="",
            bg='white',
            fg=self.colors['primary'],
            font=('Arial', 10, 'bold')
        )
        self.count_label.pack(side=tk.RIGHT)
    
    def load_sales(self):
        """تحميل قائمة المبيعات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب المبيعات من قاعدة البيانات
            # نحتاج إنشاء جدول المبيعات أولاً
            self.ensure_sales_table()
            
            sales = self.db_manager.fetch_all("""
                SELECT s.*, c.name as customer_name 
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id 
                ORDER BY s.sale_date DESC
            """)
            
            total_amount = 0
            
            # إضافة البيانات للجدول
            for sale in sales:
                # تنسيق التاريخ
                sale_date = sale['sale_date'][:10] if sale['sale_date'] else 'غير محدد'
                
                # حساب عدد المنتجات
                items_count = self.get_sale_items_count(sale['id'])
                
                total_amount += sale['total_amount']
                
                self.tree.insert('', 'end', values=(
                    sale['id'],
                    sale_date,
                    sale['customer_name'] or 'عميل نقدي',
                    f"{items_count} منتج",
                    f"{sale['total_amount']:.2f} ₪",
                    sale['payment_method'] or 'نقدي',
                    sale['status'] or 'مكتمل'
                ))
            
            # تحديث الإحصائيات
            count = len(sales)
            self.count_label.configure(text=f"إجمالي المبيعات: {count}")
            self.total_label.configure(text=f"💰 الإجمالي: {total_amount:.2f} ₪")
            self.status_label.configure(text="تم تحميل البيانات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المبيعات: {str(e)}")
            self.status_label.configure(text="خطأ في تحميل البيانات")
    
    def ensure_sales_table(self):
        """التأكد من وجود جدول المبيعات"""
        try:
            # إنشاء جدول المبيعات
            sales_table = '''
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_amount REAL DEFAULT 0,
                    payment_method TEXT DEFAULT 'نقدي',
                    status TEXT DEFAULT 'مكتمل',
                    notes TEXT,
                    created_by INTEGER,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            '''
            
            # إنشاء جدول تفاصيل المبيعات
            sale_items_table = '''
                CREATE TABLE IF NOT EXISTS sale_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sale_id INTEGER NOT NULL,
                    item_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    FOREIGN KEY (sale_id) REFERENCES sales (id),
                    FOREIGN KEY (item_id) REFERENCES inventory (id)
                )
            '''
            
            self.db_manager.execute_query(sales_table)
            self.db_manager.execute_query(sale_items_table)
            
        except Exception as e:
            print(f"خطأ في إنشاء جداول المبيعات: {e}")
    
    def get_sale_items_count(self, sale_id):
        """الحصول على عدد المنتجات في البيع"""
        try:
            result = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM sale_items WHERE sale_id = ?",
                (sale_id,)
            )
            return result['count'] if result else 0
        except:
            return 0
    
    def on_filter(self, *args):
        """فلترة المبيعات حسب التاريخ"""
        filter_value = self.date_var.get()
        
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # بناء الاستعلام حسب الفلتر
            query = """
                SELECT s.*, c.name as customer_name 
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id 
            """
            params = []
            
            if filter_value == 'اليوم':
                query += " WHERE DATE(s.sale_date) = DATE('now')"
            elif filter_value == 'أمس':
                query += " WHERE DATE(s.sale_date) = DATE('now', '-1 day')"
            elif filter_value == 'هذا الأسبوع':
                query += " WHERE DATE(s.sale_date) >= DATE('now', '-7 days')"
            elif filter_value == 'هذا الشهر':
                query += " WHERE DATE(s.sale_date) >= DATE('now', 'start of month')"
            
            query += " ORDER BY s.sale_date DESC"
            
            # تنفيذ الاستعلام
            sales = self.db_manager.fetch_all(query, params)
            
            total_amount = 0
            
            # إضافة النتائج للجدول
            for sale in sales:
                sale_date = sale['sale_date'][:10] if sale['sale_date'] else 'غير محدد'
                items_count = self.get_sale_items_count(sale['id'])
                total_amount += sale['total_amount']
                
                self.tree.insert('', 'end', values=(
                    sale['id'],
                    sale_date,
                    sale['customer_name'] or 'عميل نقدي',
                    f"{items_count} منتج",
                    f"{sale['total_amount']:.2f} ₪",
                    sale['payment_method'] or 'نقدي',
                    sale['status'] or 'مكتمل'
                ))
            
            # تحديث الإحصائيات
            count = len(sales)
            self.count_label.configure(text=f"نتائج الفلتر: {count}")
            self.total_label.configure(text=f"💰 الإجمالي: {total_amount:.2f} ₪")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الفلترة: {str(e)}")
    
    def on_select(self, event):
        """معالجة اختيار بيع من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_sale = {
                    'id': values[0],
                    'date': values[1],
                    'customer': values[2],
                    'items': values[3],
                    'total': values[4],
                    'payment_method': values[5],
                    'status': values[6]
                }
                self.status_label.configure(text=f"تم اختيار البيع رقم: {values[0]}")
    
    def new_sale(self):
        """إنشاء بيع جديد"""
        NewSaleDialog(self)
    
    def view_sale_details(self):
        """عرض تفاصيل البيع"""
        if not self.selected_sale:
            messagebox.showwarning("تحذير", "يرجى اختيار بيع لعرض تفاصيله")
            return
        
        SaleDetailsDialog(self, self.selected_sale['id'])
    
    def delete_sale(self):
        """حذف بيع محدد"""
        if not self.selected_sale:
            messagebox.showwarning("تحذير", "يرجى اختيار بيع للحذف")
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف البيع رقم {self.selected_sale['id']}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )
        
        if result:
            try:
                # حذف تفاصيل البيع أولاً
                self.db_manager.execute_query(
                    "DELETE FROM sale_items WHERE sale_id = ?",
                    (self.selected_sale['id'],)
                )
                
                # حذف البيع
                self.db_manager.execute_query(
                    "DELETE FROM sales WHERE id = ?",
                    (self.selected_sale['id'],)
                )
                
                messagebox.showinfo("نجح", "تم حذف البيع بنجاح")
                self.selected_sale = None
                self.load_sales()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف البيع: {str(e)}")

class NewSaleDialog:
    """نافذة إنشاء بيع جديد"""

    def __init__(self, parent_manager):
        self.parent_manager = parent_manager
        self.db_manager = parent_manager.db_manager
        self.sale_items = []  # قائمة المنتجات في البيع

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("🛒 بيع جديد")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_dialog()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_dialog(self):
        """إعداد نافذة البيع الجديد"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="🛒 إنشاء بيع جديد",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack()

        # معلومات البيع
        info_frame = tk.Frame(self.window, bg='white')
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        # العميل
        customer_label = tk.Label(
            info_frame,
            text="العميل:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        customer_label.grid(row=0, column=0, sticky='w', padx=(0, 10))

        self.customer_var = tk.StringVar()
        customer_combo = ttk.Combobox(
            info_frame,
            textvariable=self.customer_var,
            font=('Arial', 11),
            width=25,
            state='readonly'
        )
        customer_combo.grid(row=0, column=1, sticky='w', padx=(0, 20))

        # تحميل العملاء
        self.load_customers(customer_combo)

        # طريقة الدفع
        payment_label = tk.Label(
            info_frame,
            text="طريقة الدفع:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        payment_label.grid(row=0, column=2, sticky='w', padx=(0, 10))

        self.payment_var = tk.StringVar(value="نقدي")
        payment_combo = ttk.Combobox(
            info_frame,
            textvariable=self.payment_var,
            values=["نقدي", "بطاقة ائتمان", "تحويل بنكي", "شيك"],
            font=('Arial', 11),
            width=15,
            state='readonly'
        )
        payment_combo.grid(row=0, column=3, sticky='w')

        # إضافة منتجات
        products_frame = tk.Frame(self.window, bg='white')
        products_frame.pack(fill=tk.X, padx=20, pady=10)

        products_title = tk.Label(
            products_frame,
            text="📦 إضافة منتجات:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 14, 'bold')
        )
        products_title.pack(anchor='w')

        # شريط إضافة منتج
        add_product_frame = tk.Frame(products_frame, bg='#f8fafc', relief='solid', bd=1)
        add_product_frame.pack(fill=tk.X, pady=(10, 0))

        add_content = tk.Frame(add_product_frame, bg='#f8fafc')
        add_content.pack(fill=tk.X, padx=10, pady=10)

        # اختيار المنتج
        product_label = tk.Label(
            add_content,
            text="المنتج:",
            bg='#f8fafc',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        product_label.grid(row=0, column=0, sticky='w', padx=(0, 10))

        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(
            add_content,
            textvariable=self.product_var,
            font=('Arial', 10),
            width=25,
            state='readonly'
        )
        self.product_combo.grid(row=0, column=1, padx=(0, 10))
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_select)

        # الكمية
        qty_label = tk.Label(
            add_content,
            text="الكمية:",
            bg='#f8fafc',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        qty_label.grid(row=0, column=2, sticky='w', padx=(0, 10))

        self.qty_var = tk.StringVar(value="1")
        qty_entry = tk.Entry(
            add_content,
            textvariable=self.qty_var,
            font=('Arial', 10),
            width=8,
            validate='key',
            validatecommand=(self.window.register(self.validate_number), '%P')
        )
        qty_entry.grid(row=0, column=3, padx=(0, 10))

        # السعر
        price_label = tk.Label(
            add_content,
            text="السعر:",
            bg='#f8fafc',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        price_label.grid(row=0, column=4, sticky='w', padx=(0, 10))

        self.price_var = tk.StringVar()
        price_entry = tk.Entry(
            add_content,
            textvariable=self.price_var,
            font=('Arial', 10),
            width=10,
            validate='key',
            validatecommand=(self.window.register(self.validate_float), '%P')
        )
        price_entry.grid(row=0, column=5, padx=(0, 10))

        # زر الإضافة
        add_btn = tk.Button(
            add_content,
            text="➕ إضافة",
            command=self.add_product_to_sale,
            bg='#10b981',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            bd=0,
            padx=10,
            pady=5,
            cursor='hand2'
        )
        add_btn.grid(row=0, column=6)

        # تحميل المنتجات
        self.load_products()

        # جدول المنتجات المضافة
        table_frame = tk.Frame(self.window, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        table_title = tk.Label(
            table_frame,
            text="🛍️ منتجات البيع:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        table_title.pack(anchor='w', pady=(0, 5))

        # إنشاء الجدول
        columns = ("المنتج", "الكمية", "السعر", "الإجمالي")
        self.items_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=8)

        # تعيين العناوين
        for col in columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=150, anchor='center')

        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط حدث النقر المزدوج لحذف المنتج
        self.items_tree.bind('<Double-1>', self.remove_product_from_sale)

        # الإجمالي
        total_frame = tk.Frame(self.window, bg='white')
        total_frame.pack(fill=tk.X, padx=20, pady=10)

        self.total_label = tk.Label(
            total_frame,
            text="💰 الإجمالي: 0.00 ₪",
            bg='white',
            fg='#10b981',
            font=('Arial', 16, 'bold')
        )
        self.total_label.pack(side=tk.RIGHT)

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ البيع",
            command=self.save_sale,
            bg='#2563eb',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#ef4444',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def load_customers(self, combo):
        """تحميل قائمة العملاء"""
        try:
            customers = self.db_manager.fetch_all("SELECT id, name FROM customers ORDER BY name")
            customer_list = ['عميل نقدي'] + [f"{c['name']} (ID: {c['id']})" for c in customers]
            combo['values'] = customer_list
            combo.set('عميل نقدي')
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            products = self.db_manager.fetch_all(
                "SELECT id, item_name, selling_price, quantity FROM inventory WHERE quantity > 0 ORDER BY item_name"
            )
            product_list = [f"{p['item_name']} - {p['quantity']} متوفر (ID: {p['id']})" for p in products]
            self.product_combo['values'] = product_list
        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")

    def on_product_select(self, event):
        """عند اختيار منتج، ملء السعر تلقائياً"""
        try:
            selected = self.product_var.get()
            if selected and 'ID:' in selected:
                # استخراج ID المنتج
                product_id = selected.split('ID: ')[1].split(')')[0]

                # جلب بيانات المنتج
                product = self.db_manager.fetch_one(
                    "SELECT selling_price FROM inventory WHERE id = ?",
                    (product_id,)
                )

                if product:
                    self.price_var.set(str(product['selling_price']))
        except Exception as e:
            print(f"خطأ في جلب سعر المنتج: {e}")

    def validate_number(self, value):
        """التحقق من صحة الأرقام الصحيحة"""
        if value == "":
            return True
        try:
            int(value)
            return True
        except ValueError:
            return False

    def validate_float(self, value):
        """التحقق من صحة الأرقام العشرية"""
        if value == "":
            return True
        try:
            float(value)
            return True
        except ValueError:
            return False

    def add_product_to_sale(self):
        """إضافة منتج للبيع"""
        try:
            # التحقق من البيانات
            selected_product = self.product_var.get()
            quantity_str = self.qty_var.get().strip()
            price_str = self.price_var.get().strip()

            if not selected_product or 'ID:' not in selected_product:
                messagebox.showerror("خطأ", "يرجى اختيار منتج")
                return

            if not quantity_str:
                messagebox.showerror("خطأ", "يرجى إدخال الكمية")
                return

            if not price_str:
                messagebox.showerror("خطأ", "يرجى إدخال السعر")
                return

            # تحويل القيم
            quantity = int(quantity_str)
            price = float(price_str)

            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return

            if price < 0:
                messagebox.showerror("خطأ", "السعر لا يمكن أن يكون سالباً")
                return

            # استخراج معلومات المنتج
            product_id = selected_product.split('ID: ')[1].split(')')[0]
            product_name = selected_product.split(' - ')[0]

            # التحقق من توفر الكمية
            product_data = self.db_manager.fetch_one(
                "SELECT quantity FROM inventory WHERE id = ?",
                (product_id,)
            )

            if not product_data or product_data['quantity'] < quantity:
                messagebox.showerror("خطأ", f"الكمية المتوفرة غير كافية\nالمتوفر: {product_data['quantity'] if product_data else 0}")
                return

            # التحقق من عدم إضافة نفس المنتج مرتين
            for item in self.sale_items:
                if item['product_id'] == product_id:
                    messagebox.showerror("خطأ", "هذا المنتج مضاف بالفعل\nيمكنك تعديل الكمية من الجدول")
                    return

            # حساب الإجمالي
            total = quantity * price

            # إضافة المنتج للقائمة
            item_data = {
                'product_id': product_id,
                'product_name': product_name,
                'quantity': quantity,
                'price': price,
                'total': total
            }

            self.sale_items.append(item_data)

            # إضافة للجدول
            self.items_tree.insert('', 'end', values=(
                product_name,
                quantity,
                f"{price:.2f} ₪",
                f"{total:.2f} ₪"
            ))

            # تحديث الإجمالي
            self.update_total()

            # مسح الحقول
            self.product_var.set('')
            self.qty_var.set('1')
            self.price_var.set('')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {str(e)}")

    def remove_product_from_sale(self, event):
        """حذف منتج من البيع"""
        selection = self.items_tree.selection()
        if selection:
            # الحصول على فهرس العنصر
            item_index = self.items_tree.index(selection[0])

            # حذف من القائمة
            if 0 <= item_index < len(self.sale_items):
                del self.sale_items[item_index]

            # حذف من الجدول
            self.items_tree.delete(selection[0])

            # تحديث الإجمالي
            self.update_total()

    def update_total(self):
        """تحديث الإجمالي"""
        total = sum(item['total'] for item in self.sale_items)
        self.total_label.configure(text=f"💰 الإجمالي: {total:.2f} ₪")

    def save_sale(self):
        """حفظ البيع"""
        try:
            # التحقق من وجود منتجات
            if not self.sale_items:
                messagebox.showerror("خطأ", "يرجى إضافة منتجات للبيع")
                return

            # جمع البيانات
            customer_text = self.customer_var.get()
            payment_method = self.payment_var.get()
            total_amount = sum(item['total'] for item in self.sale_items)

            # استخراج ID العميل
            customer_id = None
            if customer_text != 'عميل نقدي' and 'ID:' in customer_text:
                customer_id = customer_text.split('ID: ')[1].split(')')[0]

            # حفظ البيع في قاعدة البيانات
            sale_query = """
                INSERT INTO sales (customer_id, total_amount, payment_method, sale_date)
                VALUES (?, ?, ?, ?)
            """

            cursor = self.db_manager.execute_query(
                sale_query,
                (customer_id, total_amount, payment_method, datetime.now())
            )

            if not cursor:
                raise Exception("فشل في إنشاء البيع")

            sale_id = cursor.lastrowid

            # حفظ تفاصيل البيع
            for item in self.sale_items:
                item_query = """
                    INSERT INTO sale_items (sale_id, item_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                """

                self.db_manager.execute_query(
                    item_query,
                    (sale_id, item['product_id'], item['quantity'], item['price'], item['total'])
                )

                # تحديث المخزون
                self.db_manager.execute_query(
                    "UPDATE inventory SET quantity = quantity - ? WHERE id = ?",
                    (item['quantity'], item['product_id'])
                )

            messagebox.showinfo("نجح", f"تم حفظ البيع بنجاح\nرقم البيع: {sale_id}")

            # تحديث قائمة المبيعات
            self.parent_manager.load_sales()
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيع: {str(e)}")

class SaleDetailsDialog:
    """نافذة عرض تفاصيل البيع"""

    def __init__(self, parent_manager, sale_id):
        self.parent_manager = parent_manager
        self.db_manager = parent_manager.db_manager
        self.sale_id = sale_id

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title(f"تفاصيل البيع رقم {sale_id}")
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_dialog()
        self.load_sale_details()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_dialog(self):
        """إعداد نافذة تفاصيل البيع"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text=f"📋 تفاصيل البيع رقم {self.sale_id}",
            bg='white',
            fg='#1f2937',
            font=('Arial', 16, 'bold')
        )
        title_label.pack()

        # معلومات البيع
        self.info_frame = tk.Frame(self.window, bg='#f8fafc', relief='solid', bd=1)
        self.info_frame.pack(fill=tk.X, padx=20, pady=10)

        # جدول المنتجات
        table_frame = tk.Frame(self.window, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        table_title = tk.Label(
            table_frame,
            text="🛍️ منتجات البيع:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        table_title.pack(anchor='w', pady=(0, 5))

        # إنشاء الجدول
        columns = ("المنتج", "الكمية", "سعر الوحدة", "الإجمالي")
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)

        # تعيين العناوين
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=140, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # زر الإغلاق
        close_btn = tk.Button(
            self.window,
            text="❌ إغلاق",
            command=self.window.destroy,
            bg='#6b7280',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        close_btn.pack(pady=20)

    def load_sale_details(self):
        """تحميل تفاصيل البيع"""
        try:
            # جلب معلومات البيع
            sale = self.db_manager.fetch_one("""
                SELECT s.*, c.name as customer_name
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                WHERE s.id = ?
            """, (self.sale_id,))

            if not sale:
                messagebox.showerror("خطأ", "لم يتم العثور على البيع")
                self.window.destroy()
                return

            # عرض معلومات البيع
            info_content = tk.Frame(self.info_frame, bg='#f8fafc')
            info_content.pack(fill=tk.X, padx=15, pady=15)

            # التاريخ
            date_label = tk.Label(
                info_content,
                text=f"📅 التاريخ: {sale['sale_date'][:19] if sale['sale_date'] else 'غير محدد'}",
                bg='#f8fafc',
                fg='#1f2937',
                font=('Arial', 11, 'bold')
            )
            date_label.pack(anchor='w')

            # العميل
            customer_label = tk.Label(
                info_content,
                text=f"👤 العميل: {sale['customer_name'] or 'عميل نقدي'}",
                bg='#f8fafc',
                fg='#1f2937',
                font=('Arial', 11, 'bold')
            )
            customer_label.pack(anchor='w', pady=(5, 0))

            # طريقة الدفع
            payment_label = tk.Label(
                info_content,
                text=f"💳 طريقة الدفع: {sale['payment_method'] or 'نقدي'}",
                bg='#f8fafc',
                fg='#1f2937',
                font=('Arial', 11, 'bold')
            )
            payment_label.pack(anchor='w', pady=(5, 0))

            # الإجمالي
            total_label = tk.Label(
                info_content,
                text=f"💰 الإجمالي: {sale['total_amount']:.2f} ₪",
                bg='#f8fafc',
                fg='#10b981',
                font=('Arial', 12, 'bold')
            )
            total_label.pack(anchor='w', pady=(5, 0))

            # جلب تفاصيل المنتجات
            items = self.db_manager.fetch_all("""
                SELECT si.*, i.item_name
                FROM sale_items si
                JOIN inventory i ON si.item_id = i.id
                WHERE si.sale_id = ?
            """, (self.sale_id,))

            # إضافة المنتجات للجدول
            for item in items:
                self.tree.insert('', 'end', values=(
                    item['item_name'],
                    item['quantity'],
                    f"{item['unit_price']:.2f} ₪",
                    f"{item['total_price']:.2f} ₪"
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل البيع: {str(e)}")
