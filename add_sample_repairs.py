#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لنظام إدارة الصيانة
Add Sample Repair Data

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
from datetime import datetime, timedelta
import random
import uuid

def create_repairs_table():
    """إنشاء جدول الصيانة إذا لم يكن موجوداً"""
    conn = sqlite3.connect('database/phone_doctor.db')
    cursor = conn.cursor()

    repairs_table = '''
        CREATE TABLE IF NOT EXISTS repairs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            repair_number TEXT UNIQUE,
            customer_name TEXT NOT NULL,
            customer_phone TEXT NOT NULL,
            customer_email TEXT,
            customer_address TEXT,
            device_type TEXT NOT NULL,
            device_brand TEXT,
            device_model TEXT,
            device_color TEXT,
            device_imei TEXT,
            device_serial TEXT,
            problem_description TEXT NOT NULL,
            problem_category TEXT,
            status TEXT DEFAULT 'pending',
            priority TEXT DEFAULT 'normal',
            technician_name TEXT,
            assigned_date TIMESTAMP,
            estimated_cost REAL DEFAULT 0,
            actual_cost REAL DEFAULT 0,
            parts_cost REAL DEFAULT 0,
            labor_cost REAL DEFAULT 0,
            total_cost REAL DEFAULT 0,
            parts_needed TEXT,
            parts_used TEXT,
            warranty_period INTEGER DEFAULT 0,
            warranty_start_date TIMESTAMP,
            warranty_end_date TIMESTAMP,
            technician_notes TEXT,
            repair_notes TEXT,
            customer_notes TEXT,
            internal_notes TEXT,
            images_path TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_date TIMESTAMP,
            delivered_date TIMESTAMP,
            created_by TEXT,
            updated_by TEXT
        )
    '''

    try:
        cursor.execute(repairs_table)
        conn.commit()
        print("✅ تم إنشاء جدول الصيانة بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول الصيانة: {e}")
    finally:
        conn.close()

def create_sample_repairs():
    """إنشاء بيانات تجريبية لأوامر الصيانة"""

    # إنشاء الجدول أولاً
    create_repairs_table()

    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('database/phone_doctor.db')
    cursor = conn.cursor()
    
    # بيانات تجريبية
    customers = [
        ("أحمد محمد علي", "0599123456", "<EMAIL>", "رام الله - المنارة"),
        ("فاطمة أحمد", "0567234567", "<EMAIL>", "نابلس - رفيديا"),
        ("محمد خالد", "0598345678", "", "الخليل - الدهيشة"),
        ("سارة عبدالله", "0569456789", "<EMAIL>", "بيت لحم - بيت جالا"),
        ("علي حسن", "0597567890", "", "جنين - قباطية"),
        ("نور الدين", "0568678901", "<EMAIL>", "طولكرم - عنبتا"),
        ("ليلى محمود", "0596789012", "<EMAIL>", "قلقيلية - حبلة"),
        ("عمر يوسف", "0565890123", "", "سلفيت - بروقين"),
        ("رنا سمير", "0594901234", "<EMAIL>", "أريحا - النويعمة"),
        ("خالد أمين", "0566012345", "", "رام الله - البيرة")
    ]
    
    devices = [
        ("iPhone 13", "Apple", "أزرق", "356789012345678"),
        ("Galaxy S21", "Samsung", "أسود", "357890123456789"),
        ("P40 Pro", "Huawei", "ذهبي", "358901234567890"),
        ("Mi 11", "Xiaomi", "أبيض", "359012345678901"),
        ("Reno 6", "Oppo", "أخضر", "360123456789012"),
        ("V21", "Vivo", "وردي", "361234567890123"),
        ("9 Pro", "OnePlus", "أسود", "362345678901234"),
        ("Pixel 6", "Google", "أبيض", "363456789012345"),
        ("Xperia 5", "Sony", "أزرق", "364567890123456"),
        ("iPhone 12", "Apple", "أحمر", "365678901234567")
    ]
    
    problems = [
        ("شاشة مكسورة", "شاشة", "تم كسر الشاشة بعد سقوط الجهاز"),
        ("بطارية تالفة", "بطارية", "البطارية لا تحتفظ بالشحن لفترة طويلة"),
        ("مشكلة في الشحن", "شحن", "الجهاز لا يشحن أو يشحن ببطء شديد"),
        ("مشكلة في الصوت", "صوت", "لا يوجد صوت من السماعات"),
        ("مشكلة في الكاميرا", "كاميرا", "الكاميرا لا تعمل أو تعطي صور مشوشة"),
        ("مشكلة في الواي فاي", "شبكة", "لا يتصل بشبكة الواي فاي"),
        ("مشكلة في اللمس", "لمس", "الشاشة لا تستجيب للمس في بعض المناطق"),
        ("تلف بالماء", "ماء", "سقط الجهاز في الماء ولا يعمل"),
        ("مشكلة في الأزرار", "أزرار", "أزرار الصوت لا تعمل"),
        ("بطء في النظام", "برمجيات", "الجهاز بطيء جداً في الاستجابة")
    ]
    
    statuses = [
        ("pending", "قيد الانتظار"),
        ("in_progress", "قيد الإصلاح"),
        ("waiting_parts", "انتظار قطع غيار"),
        ("testing", "قيد الاختبار"),
        ("completed", "تم الإصلاح"),
        ("delivered", "تم التسليم")
    ]
    
    priorities = [
        ("low", "منخفضة"),
        ("normal", "عادية"),
        ("high", "عالية"),
        ("urgent", "عاجلة")
    ]
    
    technicians = [
        "أحمد محمد",
        "محمد علي", 
        "علي أحمد",
        "سارة محمد",
        "فاطمة علي",
        "عبدالله محمد"
    ]
    
    parts_list = [
        "شاشة LCD",
        "بطارية ليثيوم",
        "كابل شحن",
        "سماعة داخلية",
        "كاميرا خلفية",
        "مودم واي فاي",
        "لوحة اللمس",
        "أزرار جانبية",
        "مكثف كهربائي",
        "معالج رئيسي"
    ]
    
    # إنشاء 50 أمر صيانة تجريبي
    for i in range(50):
        # اختيار بيانات عشوائية
        customer = random.choice(customers)
        device = random.choice(devices)
        problem = random.choice(problems)
        status = random.choice(statuses)
        priority = random.choice(priorities)
        technician = random.choice(technicians)
        parts = random.choice(parts_list)
        
        # تواريخ عشوائية
        created_date = datetime.now() - timedelta(days=random.randint(1, 30))
        updated_date = created_date + timedelta(days=random.randint(0, 5))
        
        # تكاليف عشوائية
        estimated_cost = random.randint(50, 500)
        actual_cost = estimated_cost + random.randint(-20, 50)
        parts_cost = random.randint(20, 200)
        labor_cost = actual_cost - parts_cost
        total_cost = parts_cost + labor_cost
        
        # فترة ضمان عشوائية
        warranty_period = random.choice([7, 14, 30, 90])
        
        # رقم أمر الصيانة
        repair_number = f"REP-{datetime.now().year}-{i+1:04d}"
        
        # ملاحظات تجريبية
        technician_notes = f"تم فحص الجهاز وتحديد المشكلة. يحتاج إلى {parts}."
        repair_notes = f"تم استبدال {parts} وإجراء اختبار شامل للجهاز."
        
        # إدراج البيانات
        query = """
            INSERT INTO repairs (
                repair_number, customer_name, customer_phone, customer_email, customer_address,
                device_type, device_brand, device_model, device_color, device_imei,
                problem_description, problem_category, status, priority, technician_name,
                estimated_cost, actual_cost, parts_cost, labor_cost, total_cost,
                parts_needed, parts_used, warranty_period,
                technician_notes, repair_notes,
                created_date, updated_date, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            repair_number, customer[0], customer[1], customer[2], customer[3],
            device[0], device[1], device[0], device[2], device[3],
            problem[2], problem[1], status[0], priority[0], technician,
            estimated_cost, actual_cost, parts_cost, labor_cost, total_cost,
            parts, parts, warranty_period,
            technician_notes, repair_notes,
            created_date, updated_date, "admin", "admin"
        )
        
        try:
            cursor.execute(query, params)
            print(f"✅ تم إضافة أمر الصيانة رقم: {repair_number}")
        except Exception as e:
            print(f"❌ خطأ في إضافة أمر الصيانة {i+1}: {e}")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print(f"\n🎉 تم إنشاء 50 أمر صيانة تجريبي بنجاح!")
    print("يمكنك الآن فتح برنامج Phone Doctor ومشاهدة البيانات في قسم إدارة الصيانة.")

def clear_repairs():
    """مسح جميع أوامر الصيانة"""
    conn = sqlite3.connect('database/phone_doctor.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute("DELETE FROM repairs")
        conn.commit()
        print("✅ تم مسح جميع أوامر الصيانة")
    except Exception as e:
        print(f"❌ خطأ في مسح البيانات: {e}")
    finally:
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 إضافة بيانات تجريبية لنظام إدارة الصيانة")
    print("Phone Doctor v2.0 - Sample Repair Data Generator")
    print("=" * 60)
    
    choice = input("\nاختر العملية:\n1. إضافة بيانات تجريبية\n2. مسح جميع البيانات\n3. خروج\n\nاختيارك: ")
    
    if choice == "1":
        confirm = input("\nهل تريد إضافة 50 أمر صيانة تجريبي؟ (y/n): ")
        if confirm.lower() in ['y', 'yes', 'نعم']:
            create_sample_repairs()
        else:
            print("تم إلغاء العملية.")
    
    elif choice == "2":
        confirm = input("\n⚠️ تحذير: سيتم مسح جميع أوامر الصيانة!\nهل تريد المتابعة؟ (y/n): ")
        if confirm.lower() in ['y', 'yes', 'نعم']:
            clear_repairs()
        else:
            print("تم إلغاء العملية.")
    
    elif choice == "3":
        print("وداعاً!")
    
    else:
        print("اختيار غير صحيح!")

if __name__ == "__main__":
    main()
