#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول النظيفة
Clean Login Window

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox
import sys

class CleanLoginWindow:
    """نافذة تسجيل الدخول النظيفة والبسيطة"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("تسجيل الدخول - Phone Doctor")
        self.root.geometry("400x450")
        self.root.resizable(False, False)
        self.root.configure(bg='#f0f0f0')
        
        # توسيط النافذة
        self.center_window()
        
        # منع إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.setup_ui()
        
        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # شعار التطبيق
        logo_label = tk.Label(
            main_frame,
            text="📱",
            bg='#f0f0f0',
            fg='#2563eb',
            font=('Arial', 48)
        )
        logo_label.pack(pady=(0, 10))
        
        # اسم التطبيق
        title_label = tk.Label(
            main_frame,
            text="Phone Doctor",
            bg='#f0f0f0',
            fg='#1f2937',
            font=('Arial', 20, 'bold')
        )
        title_label.pack(pady=(0, 5))
        
        # وصف التطبيق
        desc_label = tk.Label(
            main_frame,
            text="نظام إدارة محلات صيانة الهواتف",
            bg='#f0f0f0',
            fg='#6b7280',
            font=('Arial', 11)
        )
        desc_label.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        login_frame.pack(fill=tk.X, pady=(0, 20))
        
        # محتوى إطار تسجيل الدخول
        content_frame = tk.Frame(login_frame, bg='white')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(
            content_frame,
            text="🔐 تسجيل الدخول",
            bg='white',
            fg='#1f2937',
            font=('Arial', 14, 'bold')
        )
        login_title.pack(pady=(0, 20))
        
        # اسم المستخدم
        username_label = tk.Label(
            content_frame,
            text="👤 اسم المستخدم:",
            bg='white',
            fg='#374151',
            font=('Arial', 11, 'bold')
        )
        username_label.pack(anchor='w', pady=(0, 5))
        
        self.username_entry = tk.Entry(
            content_frame,
            font=('Arial', 12),
            bg='white',
            fg='black',
            relief='solid',
            bd=1
        )
        self.username_entry.pack(fill=tk.X, pady=(0, 15), ipady=5)
        
        # كلمة المرور
        password_label = tk.Label(
            content_frame,
            text="🔑 كلمة المرور:",
            bg='white',
            fg='#374151',
            font=('Arial', 11, 'bold')
        )
        password_label.pack(anchor='w', pady=(0, 5))
        
        self.password_entry = tk.Entry(
            content_frame,
            font=('Arial', 12),
            bg='white',
            fg='black',
            relief='solid',
            bd=1,
            show="*"
        )
        self.password_entry.pack(fill=tk.X, pady=(0, 15), ipady=5)
        
        # إظهار كلمة المرور
        self.show_password_var = tk.BooleanVar()
        show_check = tk.Checkbutton(
            content_frame,
            text="إظهار كلمة المرور",
            variable=self.show_password_var,
            command=self.toggle_password,
            bg='white',
            fg='#6b7280',
            font=('Arial', 9),
            relief='flat'
        )
        show_check.pack(anchor='w', pady=(0, 20))
        
        # زر تسجيل الدخول
        login_button = tk.Button(
            content_frame,
            text="🚀 تسجيل الدخول",
            command=self.login,
            bg='#2563eb',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            pady=10,
            cursor='hand2'
        )
        login_button.pack(fill=tk.X, pady=(0, 10))
        
        # زر الخروج
        exit_button = tk.Button(
            content_frame,
            text="❌ خروج",
            command=self.exit_app,
            bg='#dc2626',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            pady=10,
            cursor='hand2'
        )
        exit_button.pack(fill=tk.X)
        
        # معلومات المطور
        dev_frame = tk.Frame(main_frame, bg='#f0f0f0')
        dev_frame.pack(fill=tk.X)
        
        dev_label = tk.Label(
            dev_frame,
            text="المطور: محمد الشوامرة - 📞 0566000140",
            bg='#f0f0f0',
            fg='#6b7280',
            font=('Arial', 9)
        )
        dev_label.pack()
        
        # معلومات تسجيل الدخول
        info_label = tk.Label(
            dev_frame,
            text="المدير: admin/admin123 | المبيعات: sales/sales123",
            bg='#f0f0f0',
            fg='#f59e0b',
            font=('Arial', 9, 'bold')
        )
        info_label.pack(pady=(5, 0))
        
        # ربط مفاتيح الاختصار
        self.root.bind('<Return>', lambda e: self.login())
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda e: self.login())
    
    def toggle_password(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.show_password_var.get():
            self.password_entry.configure(show="")
        else:
            self.password_entry.configure(show="*")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # محاولة المصادقة
        try:
            if self.auth_manager.authenticate(username, password):
                user_name = self.auth_manager.current_user.get('full_name', 'المستخدم')
                messagebox.showinfo("نجح تسجيل الدخول", f"مرحباً {user_name}!")
                self.root.destroy()
                self.on_success_callback()
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_entry.delete(0, tk.END)
                self.username_entry.focus()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {str(e)}")
    
    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من التطبيق؟"):
            self.root.quit()
            sys.exit()
    
    def on_closing(self):
        """معالجة إغلاق النافذة"""
        self.exit_app()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.root.mainloop()
