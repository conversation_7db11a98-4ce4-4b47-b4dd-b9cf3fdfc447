#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المصادقة وإدارة المستخدمين
Authentication and User Management System

المطور: محمد الشوامرة - 0566000140
"""

import hashlib
import sqlite3
import os
from datetime import datetime, timedelta
import json

class AuthManager:
    """مدير المصادقة والمستخدمين"""
    
    def __init__(self, db_path="phone_doctor.db"):
        self.db_path = db_path
        self.current_user = None
        self.session_timeout = 30  # 30 دقيقة
        self.last_activity = None
        self.initialize_auth_tables()
        self.create_default_admin()
    
    def initialize_auth_tables(self):
        """تهيئة جداول المصادقة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    role TEXT DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    permissions TEXT DEFAULT '{}'
                )
            ''')
            
            # جدول جلسات المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    session_token TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active INTEGER DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # جدول سجل الأنشطة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم تهيئة جداول المصادقة")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة جداول المصادقة: {e}")
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_default_admin(self):
        """إنشاء مستخدم مدير افتراضي ومستخدم مبيعات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود مدير
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = cursor.fetchone()[0]

            if admin_count == 0:
                # إنشاء مدير افتراضي
                admin_password = self.hash_password("admin123")
                admin_permissions = json.dumps({
                    "dashboard": True,
                    "repairs": True,
                    "inventory": True,
                    "suppliers": True,
                    "sales": True,
                    "financial": True,
                    "reports": True,
                    "settings": True,
                    "users": True
                })

                cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, role, permissions)
                    VALUES (?, ?, ?, ?, ?)
                ''', ("admin", admin_password, "مدير النظام", "admin", admin_permissions))

                print("✅ تم إنشاء مستخدم المدير الافتراضي")
                print("📝 اسم المستخدم: admin")
                print("🔑 كلمة المرور: admin123")

            # التحقق من وجود مستخدم مبيعات
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'sales'")
            sales_count = cursor.fetchone()[0]

            if sales_count == 0:
                # إنشاء مستخدم مبيعات
                sales_password = self.hash_password("sales123")
                sales_permissions = json.dumps({
                    "dashboard": True,
                    "repairs": True,  # إدخال أوامر صيانة فقط
                    "inventory": False,
                    "suppliers": False,
                    "sales": True,    # صلاحيات البيع
                    "financial": False,
                    "reports": False,
                    "settings": False,
                    "users": False
                })

                cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, role, permissions)
                    VALUES (?, ?, ?, ?, ?)
                ''', ("sales", sales_password, "موظف المبيعات", "sales", sales_permissions))

                print("✅ تم إنشاء مستخدم المبيعات")
                print("📝 اسم المستخدم: sales")
                print("🔑 كلمة المرور: sales123")
                print("🔧 الصلاحيات: البيع وإدخال أوامر الصيانة فقط")

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدمين الافتراضيين: {e}")
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                SELECT id, username, full_name, role, permissions, is_active
                FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            ''', (username, password_hash))
            
            user = cursor.fetchone()
            
            if user:
                user_id, username, full_name, role, permissions, is_active = user
                
                # تحديث آخر تسجيل دخول
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                ''', (user_id,))
                
                # تسجيل النشاط
                self.log_activity(user_id, "تسجيل دخول", f"تسجيل دخول ناجح للمستخدم {username}")
                
                conn.commit()
                
                # حفظ بيانات المستخدم الحالي
                self.current_user = {
                    'id': user_id,
                    'username': username,
                    'full_name': full_name,
                    'role': role,
                    'permissions': json.loads(permissions) if permissions else {}
                }
                
                self.last_activity = datetime.now()
                
                conn.close()
                return True
            
            conn.close()
            return False
            
        except Exception as e:
            print(f"❌ خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل خروج المستخدم"""
        if self.current_user:
            self.log_activity(self.current_user['id'], "تسجيل خروج", "تسجيل خروج من النظام")
            self.current_user = None
            self.last_activity = None
    
    def is_authenticated(self):
        """التحقق من حالة المصادقة"""
        if not self.current_user:
            return False
        
        # التحقق من انتهاء الجلسة
        if self.last_activity:
            time_diff = datetime.now() - self.last_activity
            if time_diff.total_seconds() > (self.session_timeout * 60):
                self.logout()
                return False
        
        return True
    
    def update_activity(self):
        """تحديث وقت آخر نشاط"""
        if self.current_user:
            self.last_activity = datetime.now()
    
    def has_permission(self, permission):
        """التحقق من صلاحية معينة"""
        if not self.is_authenticated():
            return False
        
        if self.current_user['role'] == 'admin':
            return True
        
        return self.current_user.get('permissions', {}).get(permission, False)
    
    def get_current_user(self):
        """الحصول على المستخدم الحالي"""
        return self.current_user if self.is_authenticated() else None
    
    def log_activity(self, user_id, action, details=""):
        """تسجيل نشاط المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO activity_log (user_id, action, details)
                VALUES (?, ?, ?)
            ''', (user_id, action, details))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في تسجيل النشاط: {e}")
    
    def create_user(self, username, password, full_name, role="user", permissions=None):
        """إنشاء مستخدم جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود المستخدم
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
            if cursor.fetchone()[0] > 0:
                conn.close()
                return False, "اسم المستخدم موجود بالفعل"
            
            password_hash = self.hash_password(password)
            permissions_json = json.dumps(permissions) if permissions else "{}"
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, password_hash, full_name, role, permissions_json))
            
            conn.commit()
            conn.close()
            
            return True, "تم إنشاء المستخدم بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنشاء المستخدم: {e}"
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, full_name, email, phone, role, is_active, created_at, last_login
                FROM users
                ORDER BY created_at DESC
            ''')
            
            users = cursor.fetchall()
            conn.close()
            
            return users
            
        except Exception as e:
            print(f"❌ خطأ في جلب المستخدمين: {e}")
            return []
    
    def change_password(self, user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من كلمة المرور القديمة
            old_hash = self.hash_password(old_password)
            cursor.execute("SELECT COUNT(*) FROM users WHERE id = ? AND password_hash = ?", 
                         (user_id, old_hash))
            
            if cursor.fetchone()[0] == 0:
                conn.close()
                return False, "كلمة المرور القديمة غير صحيحة"
            
            # تحديث كلمة المرور
            new_hash = self.hash_password(new_password)
            cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", 
                         (new_hash, user_id))
            
            conn.commit()
            conn.close()
            
            return True, "تم تغيير كلمة المرور بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تغيير كلمة المرور: {e}"
