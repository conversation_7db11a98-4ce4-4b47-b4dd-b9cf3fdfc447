#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الصيانة المتطورة - Phone Doctor v2.0
Advanced Repairs Management System

المطور: محمد الشوامرة - 0566000140
جميع الحقوق محفوظة © 2024
"""

import tkinter as tk
from tkinter import messagebox, ttk, filedialog
from datetime import datetime, date, timedelta
import uuid
import os
import json
import re

class RepairsManager:
    """مدير الصيانة مع عمليات الإضافة والتعديل والحذف"""
    
    def __init__(self, parent_frame, db_manager, current_user=None):
        print("🔧 تهيئة مدير الصيانة...")
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.current_user = current_user or {'username': 'admin', 'full_name': 'المدير'}
        self.selected_repair = None
        self.current_repair = None
        self.selected_repair_id = None

        # التحقق من اتصال قاعدة البيانات
        if not self.db_manager:
            print("❌ خطأ: لا يوجد اتصال بقاعدة البيانات")
            return

        print(f"👤 المستخدم الحالي: {self.current_user.get('username', 'غير محدد')}")
        print("✅ تم تهيئة مدير الصيانة بنجاح")
        
        # ألوان النظام العصرية
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'info': '#06b6d4',
            'bg_light': '#f8fafc',
            'bg_dark': '#1e293b',
            'text_dark': '#1f2937',
            'text_light': '#6b7280',
            'accent': '#ec4899',
            'bg_main': '#ffffff',
            'bg_card': '#f9fafb',
            'text_primary': '#111827',
            'text_secondary': '#6b7280',
            'border': '#e5e7eb'
        }

        # قوائم البيانات المتطورة
        self.device_types = [
            'iPhone', 'Samsung Galaxy', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo',
            'OnePlus', 'Google Pixel', 'Sony Xperia', 'LG', 'Nokia', 'أخرى'
        ]

        self.device_brands = [
            'Apple', 'Samsung', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo',
            'OnePlus', 'Google', 'Sony', 'LG', 'Nokia', 'Realme', 'أخرى'
        ]

        self.problem_categories = [
            'شاشة مكسورة', 'بطارية تالفة', 'مشكلة في الشحن', 'مشكلة في الصوت',
            'مشكلة في الكاميرا', 'مشكلة في الواي فاي', 'مشكلة في البلوتوث',
            'مشكلة في اللمس', 'مشكلة في الأزرار', 'مشكلة في البرمجيات',
            'تلف بالماء', 'مشكلة في الميكروفون', 'أخرى'
        ]

        self.status_options = [
            ('pending', 'قيد الانتظار', '#f59e0b'),
            ('in_progress', 'قيد الإصلاح', '#06b6d4'),
            ('waiting_parts', 'انتظار قطع غيار', '#8b5cf6'),
            ('testing', 'قيد الاختبار', '#10b981'),
            ('completed', 'تم الإصلاح', '#059669'),
            ('delivered', 'تم التسليم', '#065f46'),
            ('cancelled', 'ملغي', '#dc2626')
        ]

        self.priority_options = [
            ('low', 'منخفضة', '#10b981'),
            ('normal', 'عادية', '#06b6d4'),
            ('high', 'عالية', '#f59e0b'),
            ('urgent', 'عاجلة', '#ef4444')
        ]

        self.technicians = [
            'أحمد محمد', 'محمد علي', 'علي أحمد', 'سارة محمد',
            'فاطمة علي', 'عبدالله محمد', 'خالد أحمد'
        ]
        
        try:
            self.setup_ui()
            self.ensure_repairs_table()
            self.load_repairs()
        except Exception as e:
            print(f"❌ خطأ في تهيئة مدير الصيانة: {e}")
            import traceback
            traceback.print_exc()
    
    def setup_ui(self):
        """إعداد واجهة إدارة الصيانة العصرية"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي مع تدرج لوني
        title_frame = tk.Frame(self.parent_frame, bg='white', height=80)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        title_frame.pack_propagate(False)
        
        # إطار التدرج للعنوان
        gradient_frame = tk.Frame(title_frame, bg=self.colors['primary'], height=60)
        gradient_frame.pack(fill=tk.X, pady=10)
        gradient_frame.pack_propagate(False)
        
        title_content = tk.Frame(gradient_frame, bg=self.colors['primary'])
        title_content.pack(expand=True, fill=tk.BOTH)
        
        title_label = tk.Label(
            title_content,
            text="🔧 إدارة الصيانة والإصلاح",
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 20, 'bold')
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            title_content,
            text="إدارة شاملة لجميع أوامر الصيانة والإصلاح",
            bg=self.colors['primary'],
            fg='#e2e8f0',
            font=('Arial', 11)
        )
        subtitle_label.pack()
        
        # شريط الأدوات العصري
        toolbar_frame = tk.Frame(self.parent_frame, bg='white')
        toolbar_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # الأزرار الرئيسية
        buttons_container = tk.Frame(toolbar_frame, bg='white')
        buttons_container.pack(side=tk.LEFT)
        
        # زر إضافة صيانة جديدة
        self.create_modern_button(
            buttons_container, "➕ صيانة جديدة", self.add_repair,
            self.colors['success'], "إضافة أمر صيانة جديد"
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تعديل الصيانة
        self.create_modern_button(
            buttons_container, "✏️ تعديل", self.edit_repair,
            self.colors['warning'], "تعديل أمر الصيانة المحدد"
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # زر حذف الصيانة
        self.create_modern_button(
            buttons_container, "🗑️ حذف", self.delete_repair,
            self.colors['danger'], "حذف أمر الصيانة المحدد"
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تحديث الحالة
        self.create_modern_button(
            buttons_container, "🔄 تحديث الحالة", self.update_status,
            self.colors['info'], "تحديث حالة الصيانة"
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تحديث القائمة
        self.create_modern_button(
            buttons_container, "🔄 تحديث", self.load_repairs,
            self.colors['primary'], "تحديث قائمة الصيانة"
        ).pack(side=tk.LEFT)
        
        # شريط البحث والفلترة العصري
        search_frame = tk.Frame(self.parent_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # إطار البحث
        search_container = tk.Frame(search_frame, bg='#f1f5f9', relief='solid', bd=1)
        search_container.pack(fill=tk.X, pady=5)
        
        search_content = tk.Frame(search_container, bg='#f1f5f9')
        search_content.pack(fill=tk.X, padx=15, pady=10)
        
        # البحث
        search_label = tk.Label(
            search_content,
            text="🔍 البحث:",
            bg='#f1f5f9',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        search_label.grid(row=0, column=0, sticky='w', padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        
        search_entry = tk.Entry(
            search_content,
            textvariable=self.search_var,
            font=('Arial', 11),
            relief='flat',
            bd=0,
            bg='white',
            width=25
        )
        search_entry.grid(row=0, column=1, padx=(0, 20))
        
        # فلتر الحالة
        status_label = tk.Label(
            search_content,
            text="📊 الحالة:",
            bg='#f1f5f9',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        status_label.grid(row=0, column=2, sticky='w', padx=(0, 10))
        
        self.status_var = tk.StringVar()
        self.status_var.trace('w', self.on_search)
        
        status_combo = ttk.Combobox(
            search_content,
            textvariable=self.status_var,
            values=['جميع الحالات', 'قيد الانتظار', 'قيد الإصلاح', 'تم الإصلاح', 'تم التسليم', 'ملغي'],
            font=('Arial', 10),
            width=15,
            state='readonly'
        )
        status_combo.set('جميع الحالات')
        status_combo.grid(row=0, column=3, padx=(0, 20))
        
        # فلتر الأولوية
        priority_label = tk.Label(
            search_content,
            text="⚡ الأولوية:",
            bg='#f1f5f9',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        priority_label.grid(row=0, column=4, sticky='w', padx=(0, 10))
        
        self.priority_var = tk.StringVar()
        self.priority_var.trace('w', self.on_search)
        
        priority_combo = ttk.Combobox(
            search_content,
            textvariable=self.priority_var,
            values=['جميع الأولويات', 'عادي', 'مهم', 'عاجل', 'طارئ'],
            font=('Arial', 10),
            width=12,
            state='readonly'
        )
        priority_combo.set('جميع الأولويات')
        priority_combo.grid(row=0, column=5)
        
        # تكوين الشبكة
        search_content.grid_columnconfigure(1, weight=1)
        
        # جدول الصيانة العصري
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # عنوان الجدول
        table_title = tk.Label(
            table_frame,
            text="📋 قائمة أوامر الصيانة",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold')
        )
        table_title.pack(anchor='w', pady=(0, 10))

        # إطار فرعي للجدول مع grid
        grid_frame = tk.Frame(table_frame, bg='white')
        grid_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ("ID", "العميل", "الجهاز", "المشكلة", "الحالة", "الأولوية", "التاريخ", "التكلفة")
        self.tree = ttk.Treeview(grid_frame, columns=columns, show='headings', height=12)
        
        # تعيين العناوين وعرض الأعمدة
        column_widths = [60, 150, 120, 200, 100, 80, 100, 100]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor='center')
        
        # تلوين الصفوف حسب الحالة
        self.tree.tag_configure('pending', background='#fef3c7', foreground='#92400e')
        self.tree.tag_configure('in_progress', background='#dbeafe', foreground='#1e40af')
        self.tree.tag_configure('completed', background='#d1fae5', foreground='#065f46')
        self.tree.tag_configure('delivered', background='#e0e7ff', foreground='#3730a3')
        self.tree.tag_configure('cancelled', background='#fee2e2', foreground='#991b1b')
        
        # تلوين حسب الأولوية
        self.tree.tag_configure('urgent', background='#fecaca', foreground='#7f1d1d')
        self.tree.tag_configure('emergency', background='#dc2626', foreground='white')
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(grid_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(grid_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        grid_frame.grid_rowconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.edit_repair)
        
        # شريط الحالة العصري
        status_frame = tk.Frame(self.parent_frame, bg=self.colors['bg_light'], height=40)
        status_frame.pack(fill=tk.X, padx=20, pady=10)
        status_frame.pack_propagate(False)
        
        status_content = tk.Frame(status_frame, bg=self.colors['bg_light'])
        status_content.pack(fill=tk.BOTH, expand=True, padx=15, pady=8)
        
        self.status_label = tk.Label(
            status_content,
            text="جاهز",
            bg=self.colors['bg_light'],
            fg=self.colors['text_dark'],
            font=('Arial', 10)
        )
        self.status_label.pack(side=tk.LEFT)
        
        self.count_label = tk.Label(
            status_content,
            text="",
            bg=self.colors['bg_light'],
            fg=self.colors['primary'],
            font=('Arial', 10, 'bold')
        )
        self.count_label.pack(side=tk.RIGHT)
    
    def create_modern_button(self, parent, text, command, color, tooltip=""):
        """إنشاء زر عصري مع تأثيرات"""
        btn = tk.Button(
            parent,
            text=text,
            command=command,
            bg=color,
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        
        # تأثيرات hover
        def on_enter(e):
            btn.configure(bg=self.darken_color(color))
        
        def on_leave(e):
            btn.configure(bg=color)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
    
    def darken_color(self, color):
        """تغميق اللون للتأثير"""
        color_map = {
            self.colors['success']: '#059669',
            self.colors['warning']: '#d97706',
            self.colors['danger']: '#dc2626',
            self.colors['info']: '#0891b2',
            self.colors['primary']: '#4f46e5'
        }
        return color_map.get(color, color)
    
    def ensure_repairs_table(self):
        """التأكد من وجود جدول الصيانة"""
        try:
            print("🔨 التحقق من جدول الصيانة...")

            # التحقق من وجود الجدول أولاً
            check_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='repairs'"
            result = self.db_manager.fetch_one(check_query)

            if result:
                print("✅ جدول الصيانة موجود بالفعل")
                return

            print("🔨 إنشاء جدول الصيانة...")
            repairs_table = '''
                CREATE TABLE IF NOT EXISTS repairs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    repair_number TEXT UNIQUE,
                    customer_name TEXT NOT NULL,
                    customer_phone TEXT NOT NULL,
                    customer_email TEXT,
                    customer_address TEXT,
                    device_type TEXT NOT NULL,
                    device_brand TEXT,
                    device_model TEXT,
                    device_color TEXT,
                    device_imei TEXT,
                    device_serial TEXT,
                    problem_description TEXT NOT NULL,
                    problem_category TEXT,
                    status TEXT DEFAULT 'pending',
                    priority TEXT DEFAULT 'normal',
                    technician_name TEXT,
                    assigned_date TIMESTAMP,
                    estimated_cost REAL DEFAULT 0,
                    actual_cost REAL DEFAULT 0,
                    parts_cost REAL DEFAULT 0,
                    labor_cost REAL DEFAULT 0,
                    total_cost REAL DEFAULT 0,
                    parts_needed TEXT,
                    parts_used TEXT,
                    warranty_period INTEGER DEFAULT 0,
                    warranty_start_date TIMESTAMP,
                    warranty_end_date TIMESTAMP,
                    technician_notes TEXT,
                    repair_notes TEXT,
                    customer_notes TEXT,
                    internal_notes TEXT,
                    images_path TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_date TIMESTAMP,
                    delivered_date TIMESTAMP,
                    created_by TEXT,
                    updated_by TEXT
                )
            '''

            self.db_manager.execute_query(repairs_table)
            print("✅ تم إنشاء جدول الصيانة بنجاح")

            # إضافة جدول قطع الغيار المستخدمة
            parts_used_table = '''
                CREATE TABLE IF NOT EXISTS repair_parts_used (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    repair_id INTEGER,
                    part_name TEXT NOT NULL,
                    part_cost REAL DEFAULT 0,
                    quantity INTEGER DEFAULT 1,
                    FOREIGN KEY (repair_id) REFERENCES repairs (id)
                )
            '''

            self.db_manager.execute_query(parts_used_table)

        except Exception as e:
            print(f"خطأ في إنشاء جدول الصيانة: {e}")
    
    def load_repairs(self):
        """تحميل قائمة الصيانة"""
        try:
            print("🔄 بدء تحميل بيانات الصيانة...")

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # جلب أوامر الصيانة من قاعدة البيانات
            query = """
                SELECT id, customer_name, customer_phone, device_type, device_model, device_brand,
                       problem_description, status, priority, technician_name, estimated_cost,
                       actual_cost, created_date, updated_date
                FROM repairs ORDER BY id DESC
            """

            print(f"📋 تنفيذ الاستعلام: {query}")
            try:
                repairs = self.db_manager.fetch_all(query)
                print(f"📊 تم جلب {len(repairs) if repairs else 0} أمر صيانة")
            except Exception as query_error:
                print(f"خطأ في تنفيذ الاستعلام: {query_error}")
                # جرب استعلام أبسط
                simple_query = "SELECT * FROM repairs ORDER BY id DESC"
                print(f"📋 جرب استعلام أبسط: {simple_query}")
                repairs = self.db_manager.fetch_all(simple_query)
                print(f"📊 تم جلب {len(repairs) if repairs else 0} أمر صيانة بالاستعلام البديل")
            
            # التحقق من وجود بيانات
            if not repairs:
                print("⚠️ لا توجد بيانات صيانة في قاعدة البيانات")
                self.status_label.configure(text="لا توجد أوامر صيانة")
                self.count_label.configure(text="إجمالي: 0")
                return

            # إضافة البيانات للجدول
            print(f"📝 إضافة {len(repairs)} أمر صيانة للجدول...")
            for i, repair in enumerate(repairs):
                print(f"  - معالجة أمر رقم {i+1}: {repair.get('id', 'غير محدد')}")
                # تنسيق التاريخ
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'
                
                # تحديد لون الصف
                status = repair['status'] or 'pending'
                priority = repair['priority'] or 'normal'

                # تحويل الحالات الإنجليزية للعربية للعرض
                status_display = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }.get(status, status)

                # تحويل الأولويات للعربية
                priority_display = {
                    'low': 'منخفضة',
                    'normal': 'عادية',
                    'high': 'عالية',
                    'urgent': 'عاجلة'
                }.get(priority, priority)

                # تحديد التاغ للتلوين
                if priority == 'urgent':
                    tag = 'urgent'
                elif status == 'pending':
                    tag = 'pending'
                elif status == 'in_progress':
                    tag = 'in_progress'
                elif status == 'completed':
                    tag = 'completed'
                elif status == 'delivered':
                    tag = 'delivered'
                elif status == 'cancelled':
                    tag = 'cancelled'
                else:
                    tag = 'pending'
                
                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    f"{repair['device_type']} {repair['device_model'] or ''}".strip(),
                    repair['problem_description'][:50] + "..." if repair['problem_description'] and len(repair['problem_description']) > 50 else (repair['problem_description'] or ''),
                    status_display,
                    priority_display,
                    created_date,
                    f"{repair['estimated_cost']:.2f} ₪" if repair['estimated_cost'] else "غير محدد"
                ), tags=(tag,))
            
            # تحديث الإحصائيات
            count = len(repairs)
            pending_count = len([r for r in repairs if r['status'] == 'pending'])
            in_progress_count = len([r for r in repairs if r['status'] == 'in_progress'])
            completed_count = len([r for r in repairs if r['status'] == 'completed'])
            
            self.count_label.configure(
                text=f"إجمالي: {count} | انتظار: {pending_count} | إصلاح: {in_progress_count} | مكتمل: {completed_count}"
            )
            self.status_label.configure(text="تم تحميل البيانات بنجاح")
            
        except Exception as e:
            print(f"خطأ في تحميل الصيانة: {e}")
            self.status_label.configure(text="خطأ في تحميل البيانات")

    def on_search(self, *args):
        """البحث والفلترة في الصيانة"""
        search_term = self.search_var.get().strip()
        status_filter = self.status_var.get()
        priority_filter = self.priority_var.get()

        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # بناء الاستعلام
            query = "SELECT * FROM repairs WHERE 1=1"
            params = []

            # إضافة شرط البحث
            if search_term:
                query += " AND (customer_name LIKE ? OR customer_phone LIKE ? OR device_type LIKE ? OR problem_description LIKE ?)"
                search_pattern = f'%{search_term}%'
                params.extend([search_pattern, search_pattern, search_pattern, search_pattern])

            # إضافة شرط الحالة
            if status_filter and status_filter != 'جميع الحالات':
                # تحويل الحالة العربية للإنجليزية
                status_map = {
                    'قيد الانتظار': 'pending',
                    'قيد الإصلاح': 'in_progress',
                    'انتظار قطع غيار': 'waiting_parts',
                    'قيد الاختبار': 'testing',
                    'تم الإصلاح': 'completed',
                    'تم التسليم': 'delivered',
                    'ملغي': 'cancelled'
                }
                status_en = status_map.get(status_filter, status_filter)
                query += " AND status = ?"
                params.append(status_en)

            # إضافة شرط الأولوية
            if priority_filter and priority_filter != 'جميع الأولويات':
                # تحويل الأولوية العربية للإنجليزية
                priority_map = {
                    'منخفضة': 'low',
                    'عادية': 'normal',
                    'عالية': 'high',
                    'عاجلة': 'urgent'
                }
                priority_en = priority_map.get(priority_filter, priority_filter)
                query += " AND priority = ?"
                params.append(priority_en)

            query += " ORDER BY created_date DESC"

            # تنفيذ الاستعلام
            repairs = self.db_manager.fetch_all(query, params)

            # إضافة النتائج للجدول
            for repair in repairs:
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'

                status = repair['status'] or 'pending'
                priority = repair['priority'] or 'normal'

                # تحويل الحالات للعربية
                status_display = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }.get(status, status)

                # تحويل الأولويات للعربية
                priority_display = {
                    'low': 'منخفضة',
                    'normal': 'عادية',
                    'high': 'عالية',
                    'urgent': 'عاجلة'
                }.get(priority, priority)

                # تحديد التاغ
                if priority == 'urgent':
                    tag = 'urgent'
                elif status == 'pending':
                    tag = 'pending'
                elif status == 'in_progress':
                    tag = 'in_progress'
                elif status == 'completed':
                    tag = 'completed'
                elif status == 'delivered':
                    tag = 'delivered'
                elif status == 'cancelled':
                    tag = 'cancelled'
                else:
                    tag = 'pending'

                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    f"{repair['device_type']} {repair['device_model'] or ''}".strip(),
                    repair['problem_description'][:50] + "..." if repair['problem_description'] and len(repair['problem_description']) > 50 else (repair['problem_description'] or ''),
                    status_display,
                    priority_display,
                    created_date,
                    f"{repair['estimated_cost']:.2f} ₪" if repair['estimated_cost'] else "غير محدد"
                ), tags=(tag,))

            # تحديث العداد
            count = len(repairs)
            self.count_label.configure(text=f"نتائج البحث: {count}")

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            self.status_label.configure(text="خطأ في البحث")

    def on_select(self, event):
        """معالجة اختيار صيانة من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_repair = {
                    'id': values[0],
                    'customer_name': values[1],
                    'device': values[2],
                    'problem': values[3],
                    'status': values[4],
                    'priority': values[5],
                    'date': values[6],
                    'cost': values[7]
                }
                self.status_label.configure(text=f"تم اختيار أمر الصيانة رقم: {values[0]}")

    def add_repair(self):
        """إضافة أمر صيانة جديد"""
        RepairForm(self)

    def edit_repair(self):
        """تعديل أمر صيانة محدد"""
        if not self.selected_repair:
            self.status_label.configure(text="يرجى اختيار أمر صيانة للتعديل")
            return

        # جلب البيانات الكاملة
        repair_data = self.db_manager.fetch_one(
            "SELECT * FROM repairs WHERE id = ?",
            (self.selected_repair['id'],)
        )

        if repair_data:
            RepairForm(self, repair_data)

    def delete_repair(self):
        """حذف أمر صيانة محدد"""
        if not self.selected_repair:
            self.status_label.configure(text="يرجى اختيار أمر صيانة للحذف")
            return

        try:
            # حذف أمر الصيانة من قاعدة البيانات
            self.db_manager.execute_query(
                "DELETE FROM repairs WHERE id = ?",
                (self.selected_repair['id'],)
            )

            self.selected_repair = None
            self.load_repairs()
            self.status_label.configure(text="تم حذف أمر الصيانة بنجاح")

        except Exception as e:
            print(f"خطأ في حذف أمر الصيانة: {e}")
            self.status_label.configure(text="فشل في حذف أمر الصيانة")

    def update_status(self):
        """تحديث حالة الصيانة"""
        if not self.selected_repair:
            self.status_label.configure(text="يرجى اختيار أمر صيانة لتحديث حالته")
            return

        StatusUpdateDialog(self, self.selected_repair['id'])

class RepairForm:
    """نموذج إضافة/تعديل أمر صيانة"""

    def __init__(self, parent_manager, repair_data=None):
        self.parent_manager = parent_manager
        self.repair_data = repair_data
        self.is_edit_mode = repair_data is not None

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تعديل أمر صيانة" if self.is_edit_mode else "أمر صيانة جديد")
        self.window.geometry("700x600")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_form()

        if self.is_edit_mode:
            self.fill_form_data()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_form(self):
        """إعداد نموذج الصيانة"""
        # العنوان العصري
        title_frame = tk.Frame(self.window, bg='#6366f1', height=80)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        title_content = tk.Frame(title_frame, bg='#6366f1')
        title_content.pack(expand=True, fill=tk.BOTH)

        title_text = "✏️ تعديل أمر صيانة" if self.is_edit_mode else "➕ أمر صيانة جديد"
        title_label = tk.Label(
            title_content,
            text=title_text,
            bg='#6366f1',
            fg='white',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(expand=True)

        # نموذج البيانات
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # معلومات العميل
        customer_section = tk.LabelFrame(
            form_frame,
            text="👤 معلومات العميل",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        customer_section.pack(fill=tk.X, pady=(0, 15))

        # اسم العميل
        self.create_field(customer_section, "اسم العميل:", "customer_name_var", 0)

        # رقم الهاتف
        self.create_field(customer_section, "رقم الهاتف:", "customer_phone_var", 1)

        # معلومات الجهاز
        device_section = tk.LabelFrame(
            form_frame,
            text="📱 معلومات الجهاز",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        device_section.pack(fill=tk.X, pady=(0, 15))

        # نوع الجهاز
        self.create_device_field(device_section, 0)

        # موديل الجهاز
        self.create_field(device_section, "موديل الجهاز:", "device_model_var", 1)

        # ماركة الجهاز
        self.create_field(device_section, "ماركة الجهاز:", "device_brand_var", 2)

        # لون الجهاز
        self.create_field(device_section, "لون الجهاز:", "device_color_var", 3)

        # رقم IMEI
        self.create_field(device_section, "رقم IMEI:", "device_imei_var", 4)

        # تفاصيل الصيانة
        repair_section = tk.LabelFrame(
            form_frame,
            text="🔧 تفاصيل الصيانة",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        repair_section.pack(fill=tk.X, pady=(0, 15))

        # وصف المشكلة
        self.create_field(repair_section, "وصف المشكلة:", "problem_var", 0, is_text=True)

        # الحالة والأولوية
        status_frame = tk.Frame(repair_section, bg='white')
        status_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=(10, 0))

        # الحالة
        self.create_status_field(status_frame, 0)

        # الأولوية
        self.create_priority_field(status_frame, 1)

        # اسم الفني
        self.create_field(repair_section, "اسم الفني:", "technician_name_var", 2)

        # قطع الغيار المطلوبة
        self.create_field(repair_section, "قطع الغيار المطلوبة:", "parts_needed_var", 3, is_text=True)

        # التكلفة المقدرة
        self.create_field(repair_section, "التكلفة المقدرة (₪):", "estimated_cost_var", 4, field_type="number")

        # فترة الضمان (بالأيام)
        self.create_field(repair_section, "فترة الضمان (أيام):", "warranty_period_var", 5, field_type="number")

        # ملاحظات الفني
        self.create_field(repair_section, "ملاحظات الفني:", "technician_notes_var", 6, is_text=True)

        # ملاحظات الإصلاح
        self.create_field(repair_section, "ملاحظات الإصلاح:", "repair_notes_var", 7, is_text=True)

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=30, pady=20)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ" if self.is_edit_mode else "➕ إضافة",
            command=self.save_repair,
            bg='#10b981',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#ef4444',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def create_field(self, parent, label_text, var_name, row, field_type="text", is_text=False):
        """إنشاء حقل في النموذج"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(5, 5), padx=(0, 10))

        # المتغير
        var = tk.StringVar()
        setattr(self, var_name, var)

        if is_text:
            # حقل نص متعدد الأسطر
            text_widget = tk.Text(
                parent,
                height=3,
                width=50,
                font=('Arial', 10),
                relief='solid',
                bd=1,
                wrap=tk.WORD
            )
            text_widget.grid(row=row, column=1, sticky='ew', pady=(5, 5))
            setattr(self, var_name.replace('_var', '_widget'), text_widget)
        else:
            # حقل إدخال عادي
            entry = tk.Entry(
                parent,
                textvariable=var,
                font=('Arial', 11),
                relief='solid',
                bd=1,
                width=40
            )
            entry.grid(row=row, column=1, sticky='ew', pady=(5, 5))

            # التحقق من النوع
            if field_type == "number":
                entry.configure(validate='key', validatecommand=(self.window.register(self.validate_number), '%P'))

        parent.grid_columnconfigure(1, weight=1)

    def create_device_field(self, parent, row):
        """إنشاء حقل نوع الجهاز"""
        label = tk.Label(
            parent,
            text="نوع الجهاز:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(5, 5), padx=(0, 10))

        self.device_type_var = tk.StringVar()

        device_types = [
            "iPhone", "Samsung Galaxy", "Huawei", "Xiaomi", "Oppo", "Vivo",
            "OnePlus", "Sony", "LG", "Nokia", "iPad", "Tablet", "أخرى"
        ]

        device_combo = ttk.Combobox(
            parent,
            textvariable=self.device_type_var,
            values=device_types,
            font=('Arial', 11),
            width=38,
            state='normal'
        )
        device_combo.grid(row=row, column=1, sticky='ew', pady=(5, 5))

    def create_status_field(self, parent, column):
        """إنشاء حقل الحالة"""
        label = tk.Label(
            parent,
            text="الحالة:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=0, column=column*2, sticky='w', padx=(0, 10))

        self.status_var = tk.StringVar(value="قيد الانتظار")

        status_combo = ttk.Combobox(
            parent,
            textvariable=self.status_var,
            values=['قيد الانتظار', 'قيد الإصلاح', 'تم الإصلاح', 'تم التسليم', 'ملغي'],
            font=('Arial', 10),
            width=15,
            state='readonly'
        )
        status_combo.grid(row=0, column=column*2+1, padx=(0, 20))

    def create_priority_field(self, parent, column):
        """إنشاء حقل الأولوية"""
        label = tk.Label(
            parent,
            text="الأولوية:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=0, column=column*2, sticky='w', padx=(0, 10))

        self.priority_var = tk.StringVar(value="عادي")

        priority_combo = ttk.Combobox(
            parent,
            textvariable=self.priority_var,
            values=['عادي', 'مهم', 'عاجل', 'طارئ'],
            font=('Arial', 10),
            width=12,
            state='readonly'
        )
        priority_combo.grid(row=0, column=column*2+1)

    def validate_number(self, value):
        """التحقق من صحة الأرقام"""
        if value == "":
            return True
        try:
            float(value)
            return True
        except ValueError:
            return False

    def fill_form_data(self):
        """ملء النموذج ببيانات الصيانة للتعديل"""
        if self.repair_data:
            self.customer_name_var.set(self.repair_data['customer_name'])
            self.customer_phone_var.set(self.repair_data['customer_phone'])
            self.device_type_var.set(self.repair_data['device_type'])
            self.device_model_var.set(self.repair_data.get('device_model', ''))
            self.device_brand_var.set(self.repair_data.get('device_brand', ''))
            self.device_color_var.set(self.repair_data.get('device_color', ''))
            self.device_imei_var.set(self.repair_data.get('device_imei', ''))
            self.problem_widget.insert('1.0', self.repair_data['problem_description'])
            self.status_var.set(self.repair_data['status'])
            self.priority_var.set(self.repair_data['priority'])
            self.technician_name_var.set(self.repair_data.get('technician_name', ''))
            if hasattr(self, 'parts_needed_widget'):
                self.parts_needed_widget.insert('1.0', self.repair_data.get('parts_needed', ''))
            self.estimated_cost_var.set(str(self.repair_data['estimated_cost']) if self.repair_data['estimated_cost'] else '')
            self.warranty_period_var.set(str(self.repair_data.get('warranty_period', '0')))
            if hasattr(self, 'technician_notes_widget'):
                self.technician_notes_widget.insert('1.0', self.repair_data.get('technician_notes', ''))
            if hasattr(self, 'repair_notes_widget'):
                self.repair_notes_widget.insert('1.0', self.repair_data.get('repair_notes', ''))

    def save_repair(self):
        """حفظ بيانات الصيانة"""
        # جمع البيانات
        customer_name = self.customer_name_var.get().strip()
        customer_phone = self.customer_phone_var.get().strip()
        device_type = self.device_type_var.get().strip()
        device_model = self.device_model_var.get().strip()
        device_brand = self.device_brand_var.get().strip()
        device_color = self.device_color_var.get().strip()
        device_imei = self.device_imei_var.get().strip()
        problem_description = self.problem_widget.get('1.0', tk.END).strip()
        status = self.status_var.get()
        priority = self.priority_var.get()
        technician_name = self.technician_name_var.get().strip()
        parts_needed = self.parts_needed_widget.get('1.0', tk.END).strip() if hasattr(self, 'parts_needed_widget') else ''
        estimated_cost_str = self.estimated_cost_var.get().strip()
        warranty_period_str = self.warranty_period_var.get().strip()
        technician_notes = self.technician_notes_widget.get('1.0', tk.END).strip() if hasattr(self, 'technician_notes_widget') else ''
        repair_notes = self.repair_notes_widget.get('1.0', tk.END).strip() if hasattr(self, 'repair_notes_widget') else ''

        # التحقق من البيانات
        if not customer_name:
            return

        if not customer_phone:
            return

        if not device_type:
            return

        if not problem_description:
            return

        try:
            estimated_cost = float(estimated_cost_str) if estimated_cost_str else 0.0
            warranty_period = int(warranty_period_str) if warranty_period_str else 0
        except ValueError:
            return

        try:
            if self.is_edit_mode:
                # تحديث أمر الصيانة
                query = """
                    UPDATE repairs
                    SET customer_name = ?, customer_phone = ?, device_type = ?, device_model = ?,
                        device_brand = ?, device_color = ?, device_imei = ?,
                        problem_description = ?, status = ?, priority = ?, technician_name = ?,
                        parts_needed = ?, estimated_cost = ?, warranty_period = ?,
                        technician_notes = ?, repair_notes = ?, updated_date = ?
                    WHERE id = ?
                """
                params = (customer_name, customer_phone, device_type, device_model,
                         device_brand, device_color, device_imei, problem_description,
                         status, priority, technician_name, parts_needed, estimated_cost,
                         warranty_period, technician_notes, repair_notes, datetime.now(),
                         self.repair_data['id'])
                self.parent_manager.db_manager.execute_query(query, params)
            else:
                # إضافة أمر صيانة جديد
                query = """
                    INSERT INTO repairs (customer_name, customer_phone, device_type, device_model,
                                       device_brand, device_color, device_imei, problem_description,
                                       status, priority, technician_name, parts_needed, estimated_cost,
                                       warranty_period, technician_notes, repair_notes, created_date, updated_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (customer_name, customer_phone, device_type, device_model,
                         device_brand, device_color, device_imei, problem_description,
                         status, priority, technician_name, parts_needed, estimated_cost,
                         warranty_period, technician_notes, repair_notes, datetime.now(), datetime.now())
                self.parent_manager.db_manager.execute_query(query, params)

            # تحديث قائمة الصيانة
            self.parent_manager.load_repairs()
            self.window.destroy()

        except Exception as e:
            print(f"خطأ في حفظ بيانات الصيانة: {e}")
            messagebox.showerror("خطأ", "فشل في حفظ البيانات")

class StatusUpdateDialog:
    """نافذة تحديث حالة الصيانة"""

    def __init__(self, parent_manager, repair_id):
        self.parent_manager = parent_manager
        self.repair_id = repair_id

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تحديث حالة الصيانة")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_dialog()
        self.load_repair_info()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_dialog(self):
        """إعداد نافذة تحديث الحالة"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#6366f1', height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text="🔄 تحديث حالة الصيانة",
            bg='#6366f1',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        title_label.pack(expand=True)

        # معلومات الصيانة
        self.info_frame = tk.Frame(self.window, bg='#f8fafc', relief='solid', bd=1)
        self.info_frame.pack(fill=tk.X, padx=20, pady=20)

        # نموذج التحديث
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        # الحالة الجديدة
        status_label = tk.Label(
            form_frame,
            text="الحالة الجديدة:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        status_label.pack(anchor='w')

        self.new_status_var = tk.StringVar()
        status_combo = ttk.Combobox(
            form_frame,
            textvariable=self.new_status_var,
            values=['قيد الانتظار', 'قيد الإصلاح', 'تم الإصلاح', 'تم التسليم', 'ملغي'],
            font=('Arial', 11),
            width=20,
            state='readonly'
        )
        status_combo.pack(anchor='w', pady=(5, 15))

        # التكلفة الفعلية
        cost_label = tk.Label(
            form_frame,
            text="التكلفة الفعلية (₪):",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        cost_label.pack(anchor='w')

        self.actual_cost_var = tk.StringVar()
        cost_entry = tk.Entry(
            form_frame,
            textvariable=self.actual_cost_var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=25,
            validate='key',
            validatecommand=(self.window.register(self.validate_number), '%P')
        )
        cost_entry.pack(anchor='w', pady=(5, 15))

        # ملاحظات
        notes_label = tk.Label(
            form_frame,
            text="ملاحظات إضافية:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        notes_label.pack(anchor='w')

        self.notes_text = tk.Text(
            form_frame,
            height=3,
            width=40,
            font=('Arial', 10),
            relief='solid',
            bd=1
        )
        self.notes_text.pack(anchor='w', pady=(5, 0))

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر التحديث
        update_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث الحالة",
            command=self.update_status,
            bg='#2563eb',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        update_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#6b7280',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def validate_number(self, value):
        """التحقق من صحة الأرقام"""
        if value == "":
            return True
        try:
            float(value)
            return True
        except ValueError:
            return False

    def load_repair_info(self):
        """تحميل معلومات الصيانة"""
        try:
            repair = self.parent_manager.db_manager.fetch_one(
                "SELECT * FROM repairs WHERE id = ?",
                (self.repair_id,)
            )

            if repair:
                # عرض معلومات الصيانة
                info_content = tk.Frame(self.info_frame, bg='#f8fafc')
                info_content.pack(fill=tk.X, padx=15, pady=15)

                info_text = f"""
📋 أمر الصيانة رقم: {repair['id']}
👤 العميل: {repair['customer_name']}
📱 الجهاز: {repair['device_type']} {repair['device_model'] or ''}
📊 الحالة الحالية: {repair['status']}
💰 التكلفة المقدرة: {repair['estimated_cost']:.2f} ₪
                """.strip()

                info_label = tk.Label(
                    info_content,
                    text=info_text,
                    bg='#f8fafc',
                    fg='#1f2937',
                    font=('Arial', 10),
                    justify=tk.LEFT
                )
                info_label.pack(anchor='w')

                # تعيين الحالة الحالية
                self.new_status_var.set(repair['status'])
                self.actual_cost_var.set(str(repair['actual_cost']) if repair['actual_cost'] else '')

        except Exception as e:
            print(f"خطأ في تحميل معلومات الصيانة: {e}")

    def update_status(self):
        """تحديث حالة الصيانة"""
        new_status = self.new_status_var.get()
        actual_cost_str = self.actual_cost_var.get().strip()
        notes = self.notes_text.get('1.0', tk.END).strip()

        if not new_status:
            messagebox.showerror("خطأ", "يرجى اختيار الحالة الجديدة")
            return

        try:
            actual_cost = float(actual_cost_str) if actual_cost_str else 0.0
        except ValueError:
            return

        try:
            # تحديث الحالة في قاعدة البيانات
            update_query = """
                UPDATE repairs
                SET status = ?, actual_cost = ?, technician_notes = ?, updated_date = ?
            """
            params = [new_status, actual_cost, notes, datetime.now()]

            # تحويل الحالة للإنجليزية
            status_map = {
                'قيد الانتظار': 'pending',
                'قيد الإصلاح': 'in_progress',
                'انتظار قطع غيار': 'waiting_parts',
                'قيد الاختبار': 'testing',
                'تم الإصلاح': 'completed',
                'تم التسليم': 'delivered',
                'ملغي': 'cancelled'
            }
            status_en = status_map.get(new_status, new_status)
            params[0] = status_en  # تحديث الحالة في المعاملات

            # إضافة تاريخ الإنجاز أو التسليم
            if status_en == 'completed':
                update_query += ", completed_date = ?"
                params.append(datetime.now())
            elif status_en == 'delivered':
                update_query += ", delivered_date = ?"
                params.append(datetime.now())

            update_query += " WHERE id = ?"
            params.append(self.repair_id)

            self.parent_manager.db_manager.execute_query(update_query, params)

            # تحديث قائمة الصيانة
            self.parent_manager.load_repairs()
            self.parent_manager.status_label.configure(text=f"تم تحديث حالة الصيانة إلى: {new_status}")
            self.window.destroy()

        except Exception as e:
            print(f"خطأ في تحديث حالة الصيانة: {e}")
            self.parent_manager.status_label.configure(text="فشل في تحديث الحالة")
