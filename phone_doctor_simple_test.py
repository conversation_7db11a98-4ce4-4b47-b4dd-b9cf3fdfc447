#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 - اختبار مبسط
Simple Test Version

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import sys

class PhoneDoctorSimple:
    """نسخة مبسطة من Phone Doctor للاختبار"""
    
    def __init__(self):
        print("🚀 تشغيل Phone Doctor v2.0 - نسخة الاختبار")
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - نظام إدارة محل الهواتف")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f8fafc')
        
        # تعيين الأيقونة (اختياري)
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
        
        self.setup_ui()
        self.check_database()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الهيدر
        header_frame = tk.Frame(self.root, bg='#1e40af', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # عنوان البرنامج
        title_label = tk.Label(
            header_frame,
            text="🔧 Phone Doctor v2.0 - نظام إدارة محل الهواتف",
            bg='#1e40af',
            fg='white',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f8fafc')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # بطاقة الترحيب
        welcome_card = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        welcome_card.pack(fill=tk.X, pady=(0, 20))
        
        welcome_label = tk.Label(
            welcome_card,
            text="🎉 مرحباً بك في Phone Doctor v2.0",
            bg='white',
            fg='#1e40af',
            font=('Arial', 16, 'bold')
        )
        welcome_label.pack(pady=20)
        
        # معلومات النظام
        info_text = """
✅ نظام إدارة الصيانة المتطور
✅ نظام إدارة العملاء والحسابات المالية  
✅ نظام إدارة الشيكات الشامل
✅ نظام التقارير المتقدم
✅ واجهة مستخدم احترافية
        """
        
        info_label = tk.Label(
            welcome_card,
            text=info_text,
            bg='white',
            fg='#374151',
            font=('Arial', 12),
            justify=tk.LEFT
        )
        info_label.pack(pady=(0, 20))
        
        # أزرار الاختبار
        buttons_frame = tk.Frame(main_frame, bg='#f8fafc')
        buttons_frame.pack(fill=tk.X, pady=20)
        
        # زر اختبار قاعدة البيانات
        db_button = tk.Button(
            buttons_frame,
            text="🗄️ اختبار قاعدة البيانات",
            command=self.test_database,
            bg='#10b981',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=10
        )
        db_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر اختبار الأنظمة
        systems_button = tk.Button(
            buttons_frame,
            text="⚙️ اختبار الأنظمة",
            command=self.test_systems,
            bg='#3b82f6',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=10
        )
        systems_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تشغيل البرنامج الكامل
        full_button = tk.Button(
            buttons_frame,
            text="🚀 تشغيل البرنامج الكامل",
            command=self.launch_full_program,
            bg='#dc2626',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=10
        )
        full_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر الإغلاق
        close_button = tk.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=self.close_app,
            bg='#6b7280',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=10
        )
        close_button.pack(side=tk.RIGHT)
        
        # منطقة النتائج
        self.results_frame = tk.Frame(main_frame, bg='#f8fafc')
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=20)
        
        # شريط الحالة
        status_frame = tk.Frame(self.root, bg='#e5e7eb', height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="✅ النظام جاهز للاختبار",
            bg='#e5e7eb',
            fg='#374151',
            font=('Arial', 10)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
    
    def check_database(self):
        """فحص قاعدة البيانات عند بدء التشغيل"""
        try:
            if os.path.exists('database/phone_doctor.db'):
                conn = sqlite3.connect('database/phone_doctor.db')
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                conn.close()
                
                self.update_status(f"✅ قاعدة البيانات متاحة - {len(tables)} جدول")
            else:
                self.update_status("⚠️ قاعدة البيانات غير موجودة")
        except Exception as e:
            self.update_status(f"❌ خطأ في قاعدة البيانات: {str(e)[:50]}")
    
    def test_database(self):
        """اختبار قاعدة البيانات"""
        try:
            self.clear_results()
            self.add_result("🔍 اختبار قاعدة البيانات...")
            
            if not os.path.exists('database/phone_doctor.db'):
                self.add_result("❌ ملف قاعدة البيانات غير موجود")
                return
            
            conn = sqlite3.connect('database/phone_doctor.db')
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            self.add_result(f"📊 عدد الجداول: {len(tables)}")
            
            for table in tables:
                self.add_result(f"  ✅ {table[0]}")
            
            # فحص البيانات
            try:
                cursor.execute("SELECT COUNT(*) FROM repairs")
                repairs_count = cursor.fetchone()[0]
                self.add_result(f"🔧 أوامر الصيانة: {repairs_count}")
            except:
                self.add_result("⚠️ جدول الصيانة غير متاح")
            
            try:
                cursor.execute("SELECT COUNT(*) FROM customers")
                customers_count = cursor.fetchone()[0]
                self.add_result(f"👥 العملاء: {customers_count}")
            except:
                self.add_result("⚠️ جدول العملاء غير متاح")
            
            try:
                cursor.execute("SELECT COUNT(*) FROM checks")
                checks_count = cursor.fetchone()[0]
                self.add_result(f"💳 الشيكات: {checks_count}")
            except:
                self.add_result("⚠️ جدول الشيكات غير متاح")
            
            conn.close()
            self.add_result("✅ اختبار قاعدة البيانات مكتمل")
            self.update_status("✅ تم اختبار قاعدة البيانات بنجاح")
            
        except Exception as e:
            self.add_result(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
            self.update_status("❌ فشل اختبار قاعدة البيانات")
    
    def test_systems(self):
        """اختبار الأنظمة"""
        try:
            self.clear_results()
            self.add_result("🔍 اختبار أنظمة البرنامج...")
            
            # اختبار استيراد الوحدات
            try:
                from ui.theme_manager import ThemeManager
                self.add_result("✅ ThemeManager")
            except Exception as e:
                self.add_result(f"❌ ThemeManager: {e}")
            
            try:
                from ui.customers_manager_advanced import CustomersManager
                self.add_result("✅ نظام العملاء المتطور")
            except Exception as e:
                self.add_result(f"❌ نظام العملاء: {e}")
            
            try:
                from ui.checks_manager import ChecksManager
                self.add_result("✅ نظام إدارة الشيكات")
            except Exception as e:
                self.add_result(f"❌ نظام الشيكات: {e}")
            
            try:
                from ui.repairs_manager_simple import RepairsManager
                self.add_result("✅ نظام الصيانة المحسن")
            except Exception as e:
                self.add_result(f"❌ نظام الصيانة: {e}")
            
            self.add_result("✅ اختبار الأنظمة مكتمل")
            self.update_status("✅ تم اختبار الأنظمة بنجاح")
            
        except Exception as e:
            self.add_result(f"❌ خطأ في اختبار الأنظمة: {e}")
            self.update_status("❌ فشل اختبار الأنظمة")
    
    def launch_full_program(self):
        """تشغيل البرنامج الكامل"""
        try:
            self.add_result("🚀 تشغيل البرنامج الكامل...")
            self.root.destroy()
            
            # تشغيل البرنامج الكامل
            import subprocess
            subprocess.Popen([sys.executable, 'phone_doctor_working.py'])
            
        except Exception as e:
            self.add_result(f"❌ خطأ في تشغيل البرنامج: {e}")
    
    def clear_results(self):
        """مسح النتائج"""
        for widget in self.results_frame.winfo_children():
            widget.destroy()
    
    def add_result(self, text):
        """إضافة نتيجة"""
        result_label = tk.Label(
            self.results_frame,
            text=text,
            bg='#f8fafc',
            fg='#374151',
            font=('Arial', 11),
            anchor='w'
        )
        result_label.pack(fill=tk.X, pady=2)
        self.root.update()
    
    def update_status(self, text):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=text)
    
    def close_app(self):
        """إغلاق التطبيق"""
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            print("✅ نافذة الاختبار جاهزة")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل النافذة: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        print("🔧 Phone Doctor v2.0 - نسخة الاختبار")
        print("=" * 50)
        
        app = PhoneDoctorSimple()
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للإغلاق...")

if __name__ == "__main__":
    main()
