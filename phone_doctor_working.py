#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 - نظام إدارة محلات صيانة الهواتف المتطور
Advanced Phone Repair Shop Management System with License Management

المطور: محمد الشوامرة - **********
جميع الحقوق محفوظة © 2024
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
import sqlite3
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إضافة مدير التراخيص
try:
    from license_manager import LicenseManager
    LICENSE_AVAILABLE = True
except ImportError:
    print("تحذير: لم يتم العثور على مدير التراخيص")
    LICENSE_AVAILABLE = False

# إضافة مدير الثيمات المتطور
try:
    from ui.theme_manager import ThemeManager
    THEME_AVAILABLE = True
except ImportError:
    print("تحذير: لم يتم العثور على مدير الثيمات")
    THEME_AVAILABLE = False

class DatabaseManager:
    """مدير قاعدة البيانات البسيط"""
    
    def __init__(self, db_path="phone_doctor.db"):
        self.db_path = db_path
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            if not self.connection:
                if not self.connect():
                    return None
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return cursor
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchall()
        return []
    
    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchone()
        return None
    
    def initialize_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        if not self.connect():
            return False
        
        # جدول المستخدمين
        users_table = '''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        '''
        
        # جدول العملاء
        customers_table = '''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT NOT NULL,
                email TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        '''
        
        # جدول المخزون
        inventory_table = '''
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_name TEXT NOT NULL,
                category TEXT NOT NULL,
                quantity INTEGER DEFAULT 0,
                selling_price REAL DEFAULT 0,
                barcode TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        '''

        # جدول الصيانة المتطور
        repairs_table = '''
            CREATE TABLE IF NOT EXISTS repairs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                repair_number TEXT UNIQUE,
                customer_name TEXT NOT NULL,
                customer_phone TEXT NOT NULL,
                customer_email TEXT,
                customer_address TEXT,
                device_type TEXT NOT NULL,
                device_brand TEXT,
                device_model TEXT,
                device_color TEXT,
                device_imei TEXT,
                device_serial TEXT,
                problem_description TEXT NOT NULL,
                problem_category TEXT,
                status TEXT DEFAULT 'pending',
                priority TEXT DEFAULT 'normal',
                technician_name TEXT,
                assigned_date TIMESTAMP,
                estimated_cost REAL DEFAULT 0,
                actual_cost REAL DEFAULT 0,
                parts_cost REAL DEFAULT 0,
                labor_cost REAL DEFAULT 0,
                total_cost REAL DEFAULT 0,
                parts_needed TEXT,
                parts_used TEXT,
                warranty_period INTEGER DEFAULT 0,
                warranty_start_date TIMESTAMP,
                warranty_end_date TIMESTAMP,
                technician_notes TEXT,
                repair_notes TEXT,
                customer_notes TEXT,
                internal_notes TEXT,
                images_path TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_date TIMESTAMP,
                delivered_date TIMESTAMP,
                created_by TEXT,
                updated_by TEXT
            )
        '''
        
        try:
            self.execute_query(users_table)
            self.execute_query(customers_table)
            self.execute_query(inventory_table)
            self.execute_query(repairs_table)
            
            # إنشاء المستخدمين الافتراضيين
            self.create_default_users()
            self.create_sample_data()
            
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    def create_default_users(self):
        """إنشاء المستخدمين الافتراضيين"""
        try:
            # التحقق من وجود المستخدمين
            existing = self.fetch_one("SELECT COUNT(*) as count FROM users")
            if existing and existing['count'] > 0:
                return
            
            # إنشاء المدير
            self.execute_query('''
                INSERT INTO users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ('admin', 'admin123', 'مدير النظام', 'admin'))
            
            # إنشاء موظف المبيعات
            self.execute_query('''
                INSERT INTO users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ('sales', 'sales123', 'موظف المبيعات', 'sales'))
            
            print("✅ تم إنشاء المستخدمين الافتراضيين")
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدمين: {e}")
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية"""
        try:
            # التحقق من وجود بيانات
            existing = self.fetch_one("SELECT COUNT(*) as count FROM customers")
            if existing and existing['count'] > 0:
                return
            
            # إضافة عملاء تجريبيين
            customers = [
                ('أحمد محمد', '0501234567', '<EMAIL>', 'الرياض'),
                ('فاطمة أحمد', '0507654321', '<EMAIL>', 'جدة'),
                ('محمد عبدالله', '0509876543', '<EMAIL>', 'الدمام')
            ]
            
            for name, phone, email, address in customers:
                self.execute_query(
                    'INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)',
                    (name, phone, email, address)
                )
            
            # إضافة منتجات تجريبية
            products = [
                ('شاشة iPhone 14', 'شاشات', 10, 200.0, '123456789001'),
                ('بطارية Samsung', 'بطاريات', 15, 120.0, '123456789002'),
                ('كفر حماية', 'إكسسوارات', 50, 25.0, '123456789003')
            ]
            
            for name, category, quantity, price, barcode in products:
                self.execute_query('''
                    INSERT INTO inventory (item_name, category, quantity, selling_price, barcode) 
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, category, quantity, price, barcode))
            
            print("✅ تم إنشاء البيانات التجريبية")
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")

class AuthManager:
    """مدير المصادقة"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self.current_user = None
    
    def authenticate(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            user = self.db.fetch_one(
                "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
                (username, password)
            )
            
            if user:
                self.current_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role']
                }
                print(f"✅ تم تسجيل دخول المستخدم: {user['full_name']}")
                return True
            
            print("❌ فشل في تسجيل الدخول")
            return False
        except Exception as e:
            print(f"❌ خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

class AILoginWindow:
    """نافذة تسجيل دخول بتصميم الذكاء الاصطناعي"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        
        # ألوان الذكاء الاصطناعي
        self.colors = {
            'bg_primary': '#0a0a0a',
            'bg_card': '#1a1a1a',
            'bg_input': '#2a2a2a',
            'accent': '#00d4ff',
            'success': '#00ff88',
            'danger': '#ff4757',
            'text_primary': '#ffffff',
            'text_secondary': '#a0a0a0',
            'border': '#333333'
        }
        
        self.setup_window()
        self.setup_ui()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root = tk.Tk()
        self.root.title("🤖 AI Login - Phone Doctor")
        self.root.geometry("500x650")
        self.root.resizable(False, False)
        self.root.configure(bg=self.colors['bg_primary'])
        
        # توسيط النافذة
        self.center_window()
        
        # منع إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)
        
        # رأس التطبيق
        header_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # أيقونة AI
        ai_icon = tk.Label(
            header_frame,
            text="🤖",
            bg=self.colors['bg_primary'],
            fg=self.colors['accent'],
            font=('Arial', 48)
        )
        ai_icon.pack(pady=(0, 10))
        
        # اسم التطبيق
        title_label = tk.Label(
            header_frame,
            text="PHONE DOCTOR AI",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            font=('Arial', 20, 'bold')
        )
        title_label.pack(pady=(0, 5))
        
        # خط فاصل
        separator = tk.Frame(header_frame, bg=self.colors['accent'], height=2)
        separator.pack(fill=tk.X, pady=(10, 0))
        
        # وصف النظام
        desc_label = tk.Label(
            header_frame,
            text="⚡ نظام إدارة ذكي لمحلات صيانة الهواتف ⚡",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 11)
        )
        desc_label.pack(pady=(15, 0))
        
        # بطاقة تسجيل الدخول
        login_card = tk.Frame(
            main_frame,
            bg=self.colors['bg_card'],
            relief='solid',
            bd=1
        )
        login_card.pack(fill=tk.X, pady=(20, 0))
        
        # محتوى البطاقة
        content_frame = tk.Frame(login_card, bg=self.colors['bg_card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(
            content_frame,
            text="🔐 SYSTEM ACCESS",
            bg=self.colors['bg_card'],
            fg=self.colors['accent'],
            font=('Arial', 14, 'bold')
        )
        login_title.pack(pady=(0, 25))
        
        # حقل اسم المستخدم
        self.create_input_field(content_frame, "👤 USER ID:", "username")
        
        # حقل كلمة المرور
        self.create_input_field(content_frame, "🔑 PASSWORD:", "password", show="●")
        
        # أزرار العمل
        self.create_buttons(content_frame)
        
        # معلومات النظام
        self.create_info_section(main_frame)
        
        # ربط مفاتيح الاختصار
        self.root.bind('<Return>', lambda e: self.login())
        
        # التركيز على حقل اسم المستخدم
        self.root.after(100, lambda: self.username_entry.focus())
    
    def create_input_field(self, parent, label_text, field_name, show=None):
        """إنشاء حقل إدخال"""
        # إطار الحقل
        field_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        field_frame.pack(fill=tk.X, pady=(0, 20))
        
        # التسمية
        label = tk.Label(
            field_frame,
            text=label_text,
            bg=self.colors['bg_card'],
            fg=self.colors['text_primary'],
            font=('Arial', 11, 'bold')
        )
        label.pack(anchor='w', pady=(0, 8))
        
        # حقل الإدخال
        entry = tk.Entry(
            field_frame,
            bg=self.colors['bg_input'],
            fg=self.colors['text_primary'],
            font=('Arial', 12),
            relief='flat',
            bd=0,
            insertbackground=self.colors['accent'],
            selectbackground=self.colors['accent'],
            selectforeground=self.colors['bg_primary']
        )
        
        if show:
            entry.configure(show=show)
        
        entry.pack(fill=tk.X, ipady=10)
        
        # حفظ مرجع الحقل
        if field_name == "username":
            self.username_entry = entry
        elif field_name == "password":
            self.password_entry = entry
        
        # إطار حدود
        border = tk.Frame(field_frame, bg=self.colors['border'], height=1)
        border.pack(fill=tk.X, pady=(2, 0))
    
    def create_buttons(self, parent):
        """إنشاء الأزرار"""
        buttons_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # زر تسجيل الدخول - كبير وواضح
        self.login_button = tk.Button(
            buttons_frame,
            text="🚀 دخول النظام",
            command=self.login,
            bg=self.colors['accent'],
            fg='white',
            font=('Arial', 20, 'bold'),
            relief='flat',
            bd=0,
            padx=40,
            pady=20,
            cursor='hand2',
            activebackground='#00b8e6',
            activeforeground='white',
            width=25
        )
        self.login_button.pack(fill=tk.X, pady=(0, 20), ipady=10)
        
        # زر الخروج
        exit_button = tk.Button(
            buttons_frame,
            text="❌ خروج",
            command=self.exit_app,
            bg=self.colors['danger'],
            fg=self.colors['text_primary'],
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            pady=10,
            cursor='hand2'
        )
        exit_button.pack(fill=tk.X)
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        info_frame.pack(fill=tk.X, pady=(25, 0))
        
        # معلومات المطور
        dev_label = tk.Label(
            info_frame,
            text="💻 المطور: محمد الشوامرة - 📞 **********",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 9)
        )
        dev_label.pack(pady=(0, 10))
        
        # معلومات تسجيل الدخول
        creds_frame = tk.Frame(
            info_frame,
            bg=self.colors['bg_input'],
            relief='solid',
            bd=1
        )
        creds_frame.pack(fill=tk.X)
        
        creds_content = tk.Frame(creds_frame, bg=self.colors['bg_input'])
        creds_content.pack(fill=tk.X, padx=15, pady=10)
        
        creds_title = tk.Label(
            creds_content,
            text="🔐 بيانات تسجيل الدخول:",
            bg=self.colors['bg_input'],
            fg=self.colors['success'],
            font=('Arial', 11, 'bold')
        )
        creds_title.pack()

        admin_label = tk.Label(
            creds_content,
            text="👨‍💼 المدير: admin / admin123 (بدون ترخيص)",
            bg=self.colors['bg_input'],
            fg='#00ff88',
            font=('Arial', 10, 'bold')
        )
        admin_label.pack(pady=(5, 2))

        sales_label = tk.Label(
            creds_content,
            text="👨‍💻 المبيعات: sales / sales123 (يحتاج ترخيص)",
            bg=self.colors['bg_input'],
            fg='#ffaa00',
            font=('Arial', 10, 'bold')
        )
        sales_label.pack(pady=(2, 0))

        # ملاحظة مهمة
        note_label = tk.Label(
            creds_content,
            text="⚠️ المستخدمون العاديون يحتاجون فترة تجريبية أو ترخيص كامل",
            bg=self.colors['bg_input'],
            fg='#ff6b6b',
            font=('Arial', 9, 'bold')
        )
        note_label.pack(pady=(8, 2))

        # تعليمات الاستخدام
        instructions_label = tk.Label(
            creds_content,
            text="💡 انقر على الزر الأزرق الكبير أعلاه لتسجيل الدخول",
            bg=self.colors['bg_input'],
            fg=self.colors['accent'],
            font=('Arial', 9, 'italic')
        )
        instructions_label.pack(pady=(10, 0))
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        print(f"🔄 محاولة تسجيل دخول: {username}")
        
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # تأثير تحميل
        self.login_button.configure(text="🔄 جاري التحقق...", state='disabled', bg='#f59e0b')
        self.root.update()

        # محاولة المصادقة
        if self.auth_manager.authenticate(username, password):
            user_name = self.auth_manager.current_user.get('full_name', 'المستخدم')
            self.login_button.configure(text="✅ تم الدخول بنجاح", bg=self.colors['success'])
            self.root.update()

            # انتظار قصير ثم الانتقال
            self.root.after(1500, lambda: [
                messagebox.showinfo("🎉 مرحباً", f"أهلاً وسهلاً {user_name}!\n\nسيتم فتح النظام الآن..."),
                self.root.destroy(),
                self.on_success_callback()
            ])
        else:
            self.login_button.configure(text="❌ خطأ في البيانات", bg=self.colors['danger'])
            self.root.update()

            # إعادة تعيين الزر
            self.root.after(2000, lambda: [
                self.login_button.configure(text="🚀 دخول النظام", bg=self.colors['accent'], state='normal'),
                messagebox.showerror("⚠️ خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة\n\nتأكد من البيانات وحاول مرة أخرى"),
                self.password_entry.delete(0, tk.END),
                self.username_entry.focus()
            ])
    
    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد", "هل تريد الخروج من التطبيق؟"):
            self.root.quit()
            sys.exit()
    
    def on_closing(self):
        """معالجة إغلاق النافذة"""
        self.exit_app()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.root.mainloop()

class MainApplication:
    """التطبيق الرئيسي المتطور"""

    def __init__(self, auth_manager, db_manager):
        self.auth_manager = auth_manager
        self.db_manager = db_manager

        # تهيئة مدير الثيمات
        if THEME_AVAILABLE:
            self.theme = ThemeManager()
        else:
            self.theme = None

        self.setup_main_window()
        self.setup_ui()
        self.load_dashboard()

    def setup_main_window(self):
        """إعداد النافذة الرئيسية المتطورة"""
        self.root = tk.Tk()
        self.root.title("🔧 Phone Doctor v2.0 - نظام إدارة محلات صيانة الهواتف المتطور")
        self.root.geometry("1500x1000")
        self.root.state('zoomed')

        # تطبيق الثيم المتطور
        if self.theme:
            self.root.configure(bg=self.theme.colors['bg_primary'])
            # تطبيق الأنماط المتطورة
            self.theme.apply_modern_style(self.root)
        else:
            self.root.configure(bg='#f8fafc')

        # تحسين مظهر النافذة
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)

        # إضافة أيقونة للنافذة
        try:
            # يمكن إضافة أيقونة مخصصة هنا
            self.root.iconname("Phone Doctor")
        except:
            pass

        # منع إغلاق النافذة بدون تأكيد
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # إضافة متغيرات الحالة
        self.current_page = "dashboard"
        self.sidebar_collapsed = False

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الشريط العلوي
        self.create_header()

        # المحتوى الرئيسي
        content_frame = tk.Frame(self.root, bg='#f8fafc')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # الشريط الجانبي
        self.create_sidebar(content_frame)

        # منطقة المحتوى
        self.content_area = tk.Frame(
            content_frame,
            bg='white',
            relief='solid',
            bd=1
        )
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

    def create_header(self):
        """إنشاء الشريط العلوي المتطور مع تدرج لوني"""
        # إنشاء هيدر بتدرج لوني
        if self.theme:
            header_frame = self.theme.create_gradient_frame(
                self.root,
                self.theme.colors['gradient_primary'],
                height=90
            )
            bg_color = 'transparent'
            text_color = self.theme.colors['text_white']
            subtitle_color = '#e2e8f0'
        else:
            header_frame = tk.Frame(self.root, bg='#6366f1', height=90)
            bg_color = '#6366f1'
            text_color = 'white'
            subtitle_color = '#e2e8f0'

        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # محتوى الشريط العلوي
        header_content = tk.Frame(header_frame, bg=bg_color)
        header_content.pack(fill=tk.BOTH, expand=True, padx=30, pady=15)

        # الجانب الأيسر - شعار التطبيق
        logo_frame = tk.Frame(header_content, bg=bg_color)
        logo_frame.pack(side=tk.LEFT)

        # أيقونة التطبيق المتطورة
        app_icon = tk.Label(
            logo_frame,
            text="🔧" if self.theme else "📱",
            bg=bg_color,
            fg=text_color,
            font=('Arial', 28)
        )
        app_icon.pack(side=tk.LEFT, padx=(0, 20))

        # معلومات التطبيق
        app_info_frame = tk.Frame(logo_frame, bg=bg_color)
        app_info_frame.pack(side=tk.LEFT)

        app_title = tk.Label(
            app_info_frame,
            text="Phone Doctor v2.0",
            bg=bg_color,
            fg=text_color,
            font=self.theme.fonts['title_medium'] if self.theme else ('Arial', 20, 'bold')
        )
        app_title.pack(anchor='w')

        app_subtitle = tk.Label(
            app_info_frame,
            text="نظام إدارة محلات الهواتف المتطور",
            bg=bg_color,
            fg=subtitle_color,
            font=self.theme.fonts['body_medium'] if self.theme else ('Arial', 12)
        )
        app_subtitle.pack(anchor='w')

        # الوسط - شريط البحث السريع
        search_frame = tk.Frame(header_content, bg=bg_color)
        search_frame.pack(side=tk.LEFT, expand=True, padx=(50, 50))

        search_container = tk.Frame(search_frame, bg='white', relief='solid', bd=1)
        search_container.pack(expand=True, fill=tk.X, ipady=8)

        search_icon = tk.Label(
            search_container,
            text="🔍",
            bg='white',
            fg='#64748b',
            font=('Arial', 14)
        )
        search_icon.pack(side=tk.LEFT, padx=(15, 5))

        self.global_search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_container,
            textvariable=self.global_search_var,
            bg='white',
            fg='#1f2937',
            font=self.theme.fonts['body_medium'] if self.theme else ('Arial', 12),
            relief='flat',
            bd=0
        )
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        search_entry.insert(0, "البحث السريع في جميع البيانات...")
        search_entry.configure(fg='#9ca3af')

        def on_search_focus_in(event):
            if search_entry.get() == "البحث السريع في جميع البيانات...":
                search_entry.delete(0, tk.END)
                search_entry.configure(fg='#1f2937')

        def on_search_focus_out(event):
            if not search_entry.get():
                search_entry.insert(0, "البحث السريع في جميع البيانات...")
                search_entry.configure(fg='#9ca3af')

        search_entry.bind('<FocusIn>', on_search_focus_in)
        search_entry.bind('<FocusOut>', on_search_focus_out)

        # الجانب الأيمن - معلومات المستخدم والإشعارات
        user_frame = tk.Frame(header_content, bg=bg_color)
        user_frame.pack(side=tk.RIGHT)

        # الإشعارات
        notifications_btn = tk.Button(
            user_frame,
            text="🔔",
            bg=bg_color,
            fg=text_color,
            font=('Arial', 18),
            relief='flat',
            bd=0,
            padx=15,
            pady=5,
            cursor='hand2',
            command=self.show_notifications
        )
        notifications_btn.pack(side=tk.LEFT, padx=(0, 20))

        # معلومات المستخدم المتطورة
        if self.auth_manager.current_user:
            user_name = self.auth_manager.current_user['full_name']
            user_role = 'مدير النظام' if self.auth_manager.current_user.get('role') == 'admin' else 'مستخدم'

            # إطار معلومات المستخدم
            user_info_frame = tk.Frame(user_frame, bg=bg_color)
            user_info_frame.pack(side=tk.LEFT, padx=(0, 20))

            # اسم المستخدم
            user_name_label = tk.Label(
                user_info_frame,
                text=user_name,
                bg=bg_color,
                fg=text_color,
                font=self.theme.fonts['heading_small'] if self.theme else ('Arial', 13, 'bold')
            )
            user_name_label.pack(anchor='e')

            # دور المستخدم
            user_role_label = tk.Label(
                user_info_frame,
                text=user_role,
                bg=bg_color,
                fg=subtitle_color,
                font=self.theme.fonts['body_small'] if self.theme else ('Arial', 10)
            )
            user_role_label.pack(anchor='e')

            # أيقونة المستخدم
            user_icon = tk.Label(
                user_frame,
                text="👨‍💼" if self.auth_manager.current_user.get('role') == 'admin' else "👤",
                bg=bg_color,
                fg=text_color,
                font=('Arial', 24)
            )
            user_icon.pack(side=tk.LEFT, padx=(0, 15))

        # زر تسجيل الخروج المتطور
        if self.theme:
            logout_btn = self.theme.create_modern_button(
                user_frame,
                "تسجيل الخروج",
                command=self.logout,
                style='danger',
                size='medium',
                icon='logout'
            )
            logout_btn.pack(side=tk.RIGHT)
        else:
            logout_btn = tk.Button(
                user_frame,
                text="🚪 تسجيل الخروج",
                command=self.logout,
                bg='#ef4444',
                fg='white',
                font=('Arial', 11, 'bold'),
                relief='flat',
                bd=0,
                padx=18,
                pady=8,
                cursor='hand2'
            )
            logout_btn.pack(side=tk.RIGHT)

            # تأثير hover للزر
            def on_enter(e):
                logout_btn.configure(bg='#dc2626')

            def on_leave(e):
                logout_btn.configure(bg='#ef4444')

            logout_btn.bind("<Enter>", on_enter)
            logout_btn.bind("<Leave>", on_leave)

            logout_btn.bind("<Enter>", on_enter)
            logout_btn.bind("<Leave>", on_leave)

    def show_notifications(self):
        """عرض الإشعارات"""
        # إنشاء نافذة الإشعارات
        notifications_window = tk.Toplevel(self.root)
        notifications_window.title("الإشعارات")
        notifications_window.geometry("400x500")
        notifications_window.configure(bg=self.theme.colors['bg_primary'] if self.theme else 'white')
        notifications_window.resizable(False, False)

        # توسيط النافذة
        notifications_window.transient(self.root)
        notifications_window.grab_set()

        # الهيدر
        if self.theme:
            header_frame = self.theme.create_gradient_frame(
                notifications_window,
                self.theme.colors['gradient_info'],
                height=60
            )
        else:
            header_frame = tk.Frame(notifications_window, bg='#06b6d4', height=60)

        header_frame.pack(fill=tk.X)

        header_label = tk.Label(
            header_frame,
            text="🔔 الإشعارات",
            bg='transparent' if self.theme else '#06b6d4',
            fg='white',
            font=self.theme.fonts['title_small'] if self.theme else ('Arial', 16, 'bold')
        )
        header_label.place(relx=0.5, rely=0.5, anchor='center')

        # المحتوى
        content_frame = tk.Frame(
            notifications_window,
            bg=self.theme.colors['bg_primary'] if self.theme else 'white'
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # إشعارات تجريبية
        notifications = [
            {"title": "أمر صيانة جديد", "message": "تم إضافة أمر صيانة رقم #1234", "time": "منذ 5 دقائق", "type": "info"},
            {"title": "انتهاء الضمان", "message": "انتهى ضمان الجهاز رقم #5678", "time": "منذ ساعة", "type": "warning"},
            {"title": "تم الإصلاح", "message": "تم إصلاح الجهاز رقم #9012", "time": "منذ ساعتين", "type": "success"},
        ]

        for notification in notifications:
            self.create_notification_item(content_frame, notification)

        # زر الإغلاق
        if self.theme:
            close_btn = self.theme.create_modern_button(
                content_frame,
                "إغلاق",
                command=notifications_window.destroy,
                style='secondary',
                size='medium',
                icon='close'
            )
        else:
            close_btn = tk.Button(
                content_frame,
                text="إغلاق",
                command=notifications_window.destroy,
                bg='#6b7280',
                fg='white',
                font=('Arial', 11, 'bold'),
                relief='flat',
                padx=20,
                pady=8
            )

        close_btn.pack(pady=(20, 0))

    def create_notification_item(self, parent, notification):
        """إنشاء عنصر إشعار"""
        # إطار الإشعار
        if self.theme:
            item_frame = tk.Frame(parent, bg=self.theme.colors['bg_card'], relief='solid', bd=1)
        else:
            item_frame = tk.Frame(parent, bg='#f8fafc', relief='solid', bd=1)

        item_frame.pack(fill=tk.X, pady=(0, 10))

        # محتوى الإشعار
        content = tk.Frame(item_frame, bg=item_frame['bg'])
        content.pack(fill=tk.X, padx=15, pady=12)

        # أيقونة حسب النوع
        icons = {"info": "ℹ️", "warning": "⚠️", "success": "✅", "error": "❌"}
        colors = {"info": "#06b6d4", "warning": "#f59e0b", "success": "#10b981", "error": "#ef4444"}

        icon_label = tk.Label(
            content,
            text=icons.get(notification['type'], "ℹ️"),
            bg=content['bg'],
            font=('Arial', 16)
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 10))

        # النص
        text_frame = tk.Frame(content, bg=content['bg'])
        text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        title_label = tk.Label(
            text_frame,
            text=notification['title'],
            bg=content['bg'],
            fg=self.theme.colors['text_primary'] if self.theme else '#1f2937',
            font=self.theme.fonts['heading_small'] if self.theme else ('Arial', 12, 'bold'),
            anchor='w'
        )
        title_label.pack(fill=tk.X)

        message_label = tk.Label(
            text_frame,
            text=notification['message'],
            bg=content['bg'],
            fg=self.theme.colors['text_secondary'] if self.theme else '#6b7280',
            font=self.theme.fonts['body_small'] if self.theme else ('Arial', 10),
            anchor='w',
            wraplength=250
        )
        message_label.pack(fill=tk.X)

        time_label = tk.Label(
            text_frame,
            text=notification['time'],
            bg=content['bg'],
            fg=self.theme.colors['text_tertiary'] if self.theme else '#9ca3af',
            font=self.theme.fonts['caption'] if self.theme else ('Arial', 9),
            anchor='w'
        )
        time_label.pack(fill=tk.X, pady=(5, 0))

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي المتطور مع تأثيرات ديناميكية"""
        # الشريط الجانبي الرئيسي
        sidebar_bg = self.theme.colors['bg_dark'] if self.theme else '#1e293b'
        sidebar_frame = tk.Frame(parent, bg=sidebar_bg, width=320)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # هيدر الشريط الجانبي مع تدرج
        if self.theme:
            header_frame = self.theme.create_gradient_frame(
                sidebar_frame,
                ['#334155', '#475569'],
                height=80
            )
        else:
            header_frame = tk.Frame(sidebar_frame, bg='#334155', height=80)

        header_frame.pack(fill=tk.X, padx=15, pady=(20, 15))
        header_frame.pack_propagate(False)

        # عنوان الشريط الجانبي
        header_content = tk.Frame(header_frame, bg='transparent' if self.theme else '#334155')
        header_content.place(relx=0.5, rely=0.5, anchor='center')

        sidebar_icon = tk.Label(
            header_content,
            text="📋",
            bg='transparent' if self.theme else '#334155',
            fg='white',
            font=('Arial', 24)
        )
        sidebar_icon.pack()

        sidebar_title = tk.Label(
            header_content,
            text="القوائم الرئيسية",
            bg='transparent' if self.theme else '#334155',
            fg='white',
            font=self.theme.fonts['heading_medium'] if self.theme else ('Arial', 14, 'bold')
        )
        sidebar_title.pack(pady=(5, 0))

        # زر طي/توسيع الشريط الجانبي
        collapse_btn = tk.Button(
            sidebar_frame,
            text="◀" if not self.sidebar_collapsed else "▶",
            command=self.toggle_sidebar,
            bg=sidebar_bg,
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=10,
            pady=5,
            cursor='hand2'
        )
        collapse_btn.pack(anchor='e', padx=10)

        # قائمة الأزرار المتطورة
        menu_items = [
            ("dashboard", "لوحة التحكم", "🏠", "primary"),
            ("sales", "إدارة المبيعات", "💰", "success"),
            ("repairs", "إدارة الصيانة", "🔧", "info"),
            ("inventory", "إدارة المخزون", "📦", "warning"),
            ("customers", "إدارة العملاء", "👥", "secondary"),
            ("reports", "التقارير", "📊", "info"),
            ("settings", "الإعدادات", "⚙️", "secondary")
        ]

        self.menu_buttons = {}

        # إطار الأزرار
        buttons_frame = tk.Frame(sidebar_frame, bg=sidebar_bg)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(10, 20))

        for page_id, text, icon, style in menu_items:
            # إطار الزر مع تأثيرات
            btn_container = tk.Frame(buttons_frame, bg=sidebar_bg)
            btn_container.pack(fill=tk.X, pady=8)

            # تحديد الألوان حسب الحالة
            is_active = page_id == self.current_page

            if is_active:
                if self.theme:
                    btn_bg = self.theme.colors['primary']
                    btn_fg = self.theme.colors['text_white']
                    border_color = self.theme.colors['primary_light']
                else:
                    btn_bg = '#6366f1'
                    btn_fg = 'white'
                    border_color = '#8b5cf6'
            else:
                btn_bg = sidebar_bg
                btn_fg = '#e2e8f0'
                border_color = 'transparent'

            # الزر الرئيسي
            btn = tk.Button(
                btn_container,
                text=f"{icon}  {text}",
                command=lambda p=page_id: self.change_page(p),
                bg=btn_bg,
                fg=btn_fg,
                font=self.theme.fonts['body_large'] if self.theme else ('Arial', 13, 'bold'),
                relief='flat',
                bd=0,
                padx=25,
                pady=18,
                cursor='hand2',
                anchor='w'
            )
            btn.pack(fill=tk.X)

            # إضافة تأثيرات hover
            def create_hover_effects(button, page, default_bg, hover_bg, default_fg, hover_fg):
                def on_enter(e):
                    if page != self.current_page:
                        button.configure(bg=hover_bg, fg=hover_fg)

                def on_leave(e):
                    if page != self.current_page:
                        button.configure(bg=default_bg, fg=default_fg)

                button.bind("<Enter>", on_enter)
                button.bind("<Leave>", on_leave)

            # تطبيق التأثيرات
            if not is_active:
                hover_bg = self.theme.colors['secondary'] if self.theme else '#374151'
                hover_fg = 'white'
                create_hover_effects(btn, page_id, btn_bg, hover_bg, btn_fg, hover_fg)

            # حفظ مرجع الزر
            self.menu_buttons[page_id] = btn

        # معلومات إضافية في أسفل الشريط الجانبي
        footer_frame = tk.Frame(sidebar_frame, bg=sidebar_bg)
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=15)

        # معلومات النسخة
        version_label = tk.Label(
            footer_frame,
            text="Phone Doctor v2.0",
            bg=sidebar_bg,
            fg='#64748b',
            font=self.theme.fonts['caption'] if self.theme else ('Arial', 9)
        )
        version_label.pack()

        # معلومات المطور
        developer_label = tk.Label(
            footer_frame,
            text="© محمد الشوامرة",
            bg=sidebar_bg,
            fg='#64748b',
            font=self.theme.fonts['caption'] if self.theme else ('Arial', 8)
        )
        developer_label.pack(pady=(5, 0))

        return sidebar_frame

    def toggle_sidebar(self):
        """طي/توسيع الشريط الجانبي"""
        self.sidebar_collapsed = not self.sidebar_collapsed
        # يمكن إضافة منطق طي الشريط الجانبي هنا
        print(f"الشريط الجانبي {'مطوي' if self.sidebar_collapsed else 'موسع'}")

            # تأثيرات hover للأزرار
            def on_enter(e, button=btn, page=page_id):
                if page != self.current_page:
                    button.configure(bg='#475569')

            def on_leave(e, button=btn, page=page_id):
                if page != self.current_page:
                    button.configure(bg='#1e293b')

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

            self.menu_buttons[page_id] = btn

    def change_page(self, page_id):
        """تغيير الصفحة مع تأثيرات متطورة"""
        print(f"🔄 تغيير الصفحة إلى: {page_id}")

        # إعادة تعيين ألوان الأزرار مع التأثيرات
        sidebar_bg = self.theme.colors['bg_dark'] if self.theme else '#1e293b'
        active_bg = self.theme.colors['primary'] if self.theme else '#6366f1'

        for btn_id, btn in self.menu_buttons.items():
            if btn_id == page_id:
                btn.configure(
                    bg=active_bg,
                    fg=self.theme.colors['text_white'] if self.theme else 'white'
                )
            else:
                btn.configure(
                    bg=sidebar_bg,
                    fg='#e2e8f0'
                )

        # تحديث الصفحة الحالية
        old_page = self.current_page
        self.current_page = page_id

        # إضافة تأثير انتقال (محاكاة)
        if self.theme:
            self.show_loading_indicator()

        # تحميل محتوى الصفحة
        try:
            if page_id == "dashboard":
                self.load_dashboard()
            elif page_id == "sales":
                self.load_sales_page()
            elif page_id == "customers":
                self.load_customers_page()
            elif page_id == "inventory":
                self.load_inventory_page()
            elif page_id == "repairs":
                self.load_repairs_page()
            elif page_id == "reports":
                self.load_reports_page()
            elif page_id == "settings":
                self.load_settings_page()
            else:
                self.load_placeholder_page(page_id)

            # إخفاء مؤشر التحميل
            if self.theme:
                self.hide_loading_indicator()

        except Exception as e:
            print(f"❌ خطأ في تحميل الصفحة {page_id}: {e}")
            if self.theme:
                self.hide_loading_indicator()
            self.show_error_page(page_id, str(e))

    def show_loading_indicator(self):
        """عرض مؤشر التحميل"""
        # مسح المحتوى الحالي
        self.clear_content_area()

        # إنشاء مؤشر التحميل
        loading_frame = tk.Frame(
            self.content_area,
            bg=self.theme.colors['bg_primary'] if self.theme else 'white'
        )
        loading_frame.pack(fill=tk.BOTH, expand=True)

        # محتوى التحميل
        loading_content = tk.Frame(loading_frame, bg=loading_frame['bg'])
        loading_content.place(relx=0.5, rely=0.5, anchor='center')

        # أيقونة التحميل
        loading_icon = tk.Label(
            loading_content,
            text="⏳",
            bg=loading_frame['bg'],
            fg=self.theme.colors['primary'] if self.theme else '#6366f1',
            font=('Arial', 48)
        )
        loading_icon.pack()

        # نص التحميل
        loading_text = tk.Label(
            loading_content,
            text="جاري التحميل...",
            bg=loading_frame['bg'],
            fg=self.theme.colors['text_secondary'] if self.theme else '#6b7280',
            font=self.theme.fonts['heading_medium'] if self.theme else ('Arial', 16)
        )
        loading_text.pack(pady=(20, 0))

        # تحديث الواجهة
        self.root.update()

        # حفظ مرجع للإطار
        self.loading_frame = loading_frame

    def hide_loading_indicator(self):
        """إخفاء مؤشر التحميل"""
        if hasattr(self, 'loading_frame'):
            self.loading_frame.destroy()
            delattr(self, 'loading_frame')

    def show_error_page(self, page_id, error_message):
        """عرض صفحة الخطأ"""
        self.clear_content_area()

        # إطار الخطأ
        error_frame = tk.Frame(
            self.content_area,
            bg=self.theme.colors['bg_primary'] if self.theme else 'white'
        )
        error_frame.pack(fill=tk.BOTH, expand=True)

        # محتوى الخطأ
        error_content = tk.Frame(error_frame, bg=error_frame['bg'])
        error_content.place(relx=0.5, rely=0.5, anchor='center')

        # أيقونة الخطأ
        error_icon = tk.Label(
            error_content,
            text="❌",
            bg=error_frame['bg'],
            fg=self.theme.colors['danger'] if self.theme else '#ef4444',
            font=('Arial', 48)
        )
        error_icon.pack()

        # عنوان الخطأ
        error_title = tk.Label(
            error_content,
            text=f"خطأ في تحميل صفحة {page_id}",
            bg=error_frame['bg'],
            fg=self.theme.colors['text_primary'] if self.theme else '#1f2937',
            font=self.theme.fonts['heading_medium'] if self.theme else ('Arial', 18, 'bold')
        )
        error_title.pack(pady=(20, 10))

        # رسالة الخطأ
        error_message_label = tk.Label(
            error_content,
            text=error_message,
            bg=error_frame['bg'],
            fg=self.theme.colors['text_secondary'] if self.theme else '#6b7280',
            font=self.theme.fonts['body_medium'] if self.theme else ('Arial', 12),
            wraplength=400,
            justify='center'
        )
        error_message_label.pack(pady=(0, 30))

        # زر إعادة المحاولة
        if self.theme:
            retry_btn = self.theme.create_modern_button(
                error_content,
                "إعادة المحاولة",
                command=lambda: self.change_page(page_id),
                style='primary',
                size='medium',
                icon='refresh'
            )
        else:
            retry_btn = tk.Button(
                error_content,
                text="🔄 إعادة المحاولة",
                command=lambda: self.change_page(page_id),
                bg='#6366f1',
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                padx=20,
                pady=10,
                cursor='hand2'
            )

        retry_btn.pack()

    def clear_content_area(self):
        """مسح منطقة المحتوى"""
        for widget in self.content_area.winfo_children():
            widget.destroy()

    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        try:
            from ui.welcome_screen import WelcomeScreen
            WelcomeScreen(self.content_area, self.db_manager, self.auth_manager)
        except Exception as e:
            print(f"خطأ في تحميل شاشة الترحيب: {e}")
            # استخدم الواجهة البسيطة
            self.load_simple_dashboard()

    def load_simple_dashboard(self):
        """تحميل لوحة التحكم البسيطة"""
        self.clear_content_area()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="🏠 لوحة التحكم",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(anchor='w')

        # الإحصائيات
        stats_frame = tk.Frame(self.content_area, bg='white')
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        # جلب الإحصائيات
        customers_count = len(self.db_manager.fetch_all("SELECT * FROM customers"))
        inventory_count = len(self.db_manager.fetch_all("SELECT * FROM inventory"))

        # بطاقات الإحصائيات
        stats_data = [
            ("👥", "العملاء", customers_count, "#2563eb"),
            ("📦", "المنتجات", inventory_count, "#10b981"),
            ("💰", "المبيعات اليوم", "0", "#f59e0b"),
            ("🔧", "الصيانات المعلقة", "0", "#ef4444")
        ]

        # إنشاء الشبكة
        for i, (icon, title, value, color) in enumerate(stats_data):
            col = i % 2
            row = i // 2

            card_frame = tk.Frame(stats_frame, bg=color, relief='solid', bd=1)
            card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

            # محتوى البطاقة
            card_content = tk.Frame(card_frame, bg=color)
            card_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # الأيقونة
            icon_label = tk.Label(
                card_content,
                text=icon,
                bg=color,
                fg='white',
                font=('Arial', 24)
            )
            icon_label.pack()

            # القيمة
            value_label = tk.Label(
                card_content,
                text=str(value),
                bg=color,
                fg='white',
                font=('Arial', 20, 'bold')
            )
            value_label.pack()

            # العنوان
            title_label = tk.Label(
                card_content,
                text=title,
                bg=color,
                fg='white',
                font=('Arial', 12)
            )
            title_label.pack()

        # تكوين الأعمدة
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)

        # رسالة ترحيب
        welcome_frame = tk.Frame(self.content_area, bg='white')
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        welcome_label = tk.Label(
            welcome_frame,
            text=f"مرحباً {self.auth_manager.current_user['full_name']}!\n\nمرحباً بك في نظام Phone Doctor\nلإدارة محلات صيانة الهواتف",
            bg='white',
            fg='#1f2937',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        welcome_label.pack(expand=True)

    def load_sales_page(self):
        """تحميل صفحة المبيعات"""
        try:
            from ui.sales_manager import SalesManager
            SalesManager(self.content_area, self.db_manager)
        except Exception as e:
            print(f"خطأ في تحميل صفحة المبيعات: {e}")
            # استخدم الواجهة البسيطة
            self.load_simple_sales_page()

    def load_simple_sales_page(self):
        """تحميل صفحة المبيعات البسيطة"""
        self.clear_content_area()

        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="💰 إدارة المبيعات",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(anchor='w')

        # أزرار العمل
        buttons_frame = tk.Frame(self.content_area, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        new_sale_btn = tk.Button(
            buttons_frame,
            text="🛒 بيع جديد",
            command=lambda: messagebox.showinfo("قريباً", "نافذة البيع الجديد قيد التطوير"),
            bg='#10b981',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        new_sale_btn.pack(side=tk.LEFT)

        # محتوى الصفحة
        content_label = tk.Label(
            self.content_area,
            text="📊 صفحة المبيعات\n\nهنا ستظهر جميع المبيعات والعمليات المالية",
            bg='white',
            fg='#6b7280',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        content_label.pack(expand=True)

    def load_customers_page(self):
        """تحميل صفحة العملاء"""
        try:
            from ui.customers_manager import CustomersManager
            CustomersManager(self.content_area, self.db_manager)
        except Exception as e:
            print(f"خطأ في تحميل صفحة العملاء: {e}")
            # استخدم الواجهة البسيطة
            self.load_simple_customers_page()

    def load_simple_customers_page(self):
        """تحميل صفحة العملاء البسيطة"""
        self.clear_content_area()

        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="👥 إدارة العملاء",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(anchor='w')

        # جدول العملاء
        table_frame = tk.Frame(self.content_area, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء الجدول
        columns = ("الاسم", "الهاتف", "البريد الإلكتروني", "العنوان")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعيين العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # جلب البيانات
        customers = self.db_manager.fetch_all("SELECT * FROM customers")

        # إضافة البيانات
        for customer in customers:
            tree.insert('', 'end', values=(
                customer['name'],
                customer['phone'],
                customer['email'] or 'غير محدد',
                customer['address'] or 'غير محدد'
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_inventory_page(self):
        """تحميل صفحة المخزون"""
        try:
            from ui.inventory_manager import InventoryManager
            InventoryManager(self.content_area, self.db_manager)
        except Exception as e:
            print(f"خطأ في تحميل صفحة المخزون: {e}")
            # استخدم الواجهة البسيطة
            self.load_simple_inventory_page()

    def load_simple_inventory_page(self):
        """تحميل صفحة المخزون البسيطة"""
        self.clear_content_area()

        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="📦 إدارة المخزون",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(anchor='w')

        # جدول المخزون
        table_frame = tk.Frame(self.content_area, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء الجدول
        columns = ("المنتج", "الفئة", "الكمية", "السعر", "الباركود")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعيين العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # جلب البيانات
        inventory = self.db_manager.fetch_all("SELECT * FROM inventory")

        # إضافة البيانات
        for item in inventory:
            tree.insert('', 'end', values=(
                item['item_name'],
                item['category'],
                item['quantity'],
                f"{item['selling_price']:.2f} ₪",
                item['barcode'] or 'غير محدد'
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_repairs_page(self):
        """تحميل صفحة الصيانة"""
        try:
            print("🔧 تحميل صفحة إدارة الصيانة...")
            from ui.repairs_manager_simple import RepairsManager
            current_user = self.auth_manager.current_user if hasattr(self.auth_manager, 'current_user') else {'username': 'admin', 'full_name': 'المدير'}
            RepairsManager(self.content_area, self.db_manager, current_user)
            print("✅ تم تحميل صفحة الصيانة بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل صفحة الصيانة: {e}")
            import traceback
            traceback.print_exc()
            self.load_placeholder_page("الصيانة")

    def load_reports_page(self):
        """تحميل صفحة التقارير"""
        try:
            from ui.reports_manager import ReportsManager
            ReportsManager(self.content_area, self.db_manager)
        except Exception as e:
            print(f"خطأ في تحميل صفحة التقارير: {e}")
            self.load_placeholder_page("التقارير")

    def load_settings_page(self):
        """تحميل صفحة الإعدادات"""
        try:
            from ui.settings_manager import SettingsManager
            SettingsManager(self.content_area, self.db_manager, self.auth_manager)
        except Exception as e:
            print(f"خطأ في تحميل صفحة الإعدادات: {e}")
            self.load_placeholder_page("الإعدادات")

    def load_placeholder_page(self, page_id):
        """تحميل صفحة مؤقتة"""
        self.clear_content_area()

        placeholder_label = tk.Label(
            self.content_area,
            text=f"🚧 صفحة {page_id} قيد التطوير\n\nسيتم إضافة المزيد من الميزات قريباً",
            bg='white',
            fg='#6b7280',
            font=('Arial', 16),
            justify=tk.CENTER
        )
        placeholder_label.pack(expand=True)

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد تسجيل الخروج؟"):
            self.auth_manager.logout()
            self.root.destroy()
            # إعادة تشغيل نافذة تسجيل الدخول
            main()

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد إغلاق التطبيق؟"):
            self.root.destroy()
            sys.exit()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """نقطة الدخول الرئيسية - تبدأ بتسجيل الدخول"""
    print("🚀 بدء تشغيل Phone Doctor v2.0...")

    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        if not db_manager.initialize_database():
            messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
            sys.exit(1)

        # بدء تسجيل الدخول مباشرة
        start_login_process(db_manager)

    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل البرنامج:\n{e}")
        sys.exit(1)

def start_login_process(db_manager):
    """بدء عملية تسجيل الدخول مع فحص الترخيص حسب نوع المستخدم"""
    try:
        # إنشاء مدير المصادقة
        auth_manager = AuthManager(db_manager)

        def on_login_success():
            """معالجة نجاح تسجيل الدخول مع فحص الترخيص"""
            print("✅ تم تسجيل الدخول بنجاح...")

            # التحقق من نوع المستخدم
            current_user = auth_manager.current_user
            if not current_user:
                messagebox.showerror("خطأ", "فشل في الحصول على معلومات المستخدم")
                return

            user_role = current_user.get('role', 'user')
            username = current_user.get('username', '')

            print(f"👤 المستخدم: {username} - الدور: {user_role}")

            # إذا كان المستخدم مدير النظام، تخطي فحص الترخيص
            if user_role == 'admin' or username == 'admin':
                print("🔑 مدير النظام - تخطي فحص الترخيص")
                start_main_application(auth_manager, db_manager)
                return

            # للمستخدمين العاديين، فحص الترخيص
            print("👤 مستخدم عادي - فحص الترخيص...")
            check_license_for_user(auth_manager, db_manager)

        # عرض نافذة تسجيل الدخول
        login_window = AILoginWindow(auth_manager, on_login_success)
        login_window.show()

    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        messagebox.showerror("خطأ", f"فشل في تسجيل الدخول:\n{e}")

def check_license_for_user(auth_manager, db_manager):
    """فحص الترخيص للمستخدمين العاديين"""
    try:
        if not LICENSE_AVAILABLE:
            print("⚠️ نظام الترخيص غير متوفر - السماح بالدخول")
            start_main_application(auth_manager, db_manager)
            return

        # إنشاء مدير التراخيص
        license_manager = LicenseManager(db_manager)

        # فحص حالة الترخيص
        status, message = license_manager.check_license_status()
        print(f"📋 حالة الترخيص: {status} - {message}")

        if status in ["trial", "full"]:
            # الترخيص صالح - السماح بالدخول
            print("✅ الترخيص صالح - السماح بالدخول")
            start_main_application(auth_manager, db_manager)
        else:
            # الترخيص غير صالح - عرض نافذة الترخيص
            print("❌ الترخيص غير صالح - عرض نافذة التفعيل")
            show_license_dialog(license_manager, auth_manager, db_manager)

    except Exception as e:
        print(f"❌ خطأ في فحص الترخيص: {e}")
        # في حالة الخطأ، السماح بالدخول
        start_main_application(auth_manager, db_manager)

def show_license_dialog(license_manager, auth_manager, db_manager):
    """عرض نافذة الترخيص"""
    try:
        from license_dialog import LicenseDialog

        def on_license_success():
            print("✅ تم تفعيل الترخيص بنجاح")
            start_main_application(auth_manager, db_manager)

        license_dialog = LicenseDialog(license_manager, on_license_success)
        license_dialog.show()

    except Exception as e:
        print(f"❌ خطأ في عرض نافذة الترخيص: {e}")
        messagebox.showerror("خطأ", f"فشل في عرض نافذة الترخيص:\n{e}")

def start_main_application(auth_manager, db_manager):
    """بدء التطبيق الرئيسي"""
    try:
        print("🚀 بدء التطبيق الرئيسي...")
        app = MainApplication(auth_manager, db_manager)
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق الرئيسي: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق:\n{e}")

if __name__ == "__main__":
    main()
