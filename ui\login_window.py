#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.modern_styles import ModernStyles

class LoginWindow:
    """نافذة تسجيل الدخول العصرية"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.styles = ModernStyles()
        
        self.window = tk.Toplevel()
        self.window.title("تسجيل الدخول - Phone Doctor")
        self.window.geometry("450x600")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # منع إغلاق النافذة بالطرق العادية
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.window.grab_set()  # جعل النافذة modal
        
        self.setup_ui()
        
        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        # تكوين النافذة
        self.window.configure(bg=self.styles.colors['bg_primary'])
        
        # الإطار الرئيسي
        main_frame = tk.Frame(
            self.window,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)
        
        # شعار التطبيق
        logo_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        logo_frame.pack(fill=tk.X, pady=(0, 40))
        
        # أيقونة التطبيق
        logo_icon = tk.Label(
            logo_frame,
            text="📱",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['primary'],
            font=('Segoe UI Emoji', 48)
        )
        logo_icon.pack()
        
        # اسم التطبيق
        app_name = tk.Label(
            logo_frame,
            text="Phone Doctor",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            font=('Segoe UI', 24, 'bold')
        )
        app_name.pack(pady=(10, 5))
        
        # وصف التطبيق
        app_desc = tk.Label(
            logo_frame,
            text="نظام إدارة محلات صيانة الهواتف",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_secondary'],
            font=('Segoe UI', 12)
        )
        app_desc.pack()
        
        # بطاقة تسجيل الدخول
        login_card = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        login_card.pack(fill=tk.X, pady=(20, 0))
        
        # محتوى البطاقة
        card_content = tk.Frame(
            login_card,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        card_content.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(
            card_content,
            text="🔐 تسجيل الدخول",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_medium']
        )
        login_title.pack(pady=(0, 25))
        
        # حقل اسم المستخدم
        username_frame = tk.Frame(
            card_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        username_frame.pack(fill=tk.X, pady=(0, 15))
        
        username_label = tk.Label(
            username_frame,
            text="👤 اسم المستخدم:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium'],
            anchor='w'
        )
        username_label.pack(fill=tk.X, pady=(0, 5))
        
        self.username_entry = tk.Entry(
            username_frame,
            **self.styles.get_entry_style(),
            font=self.styles.fonts['body_medium']
        )
        self.username_entry.pack(fill=tk.X)
        
        # حقل كلمة المرور
        password_frame = tk.Frame(
            card_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        password_frame.pack(fill=tk.X, pady=(0, 25))
        
        password_label = tk.Label(
            password_frame,
            text="🔑 كلمة المرور:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium'],
            anchor='w'
        )
        password_label.pack(fill=tk.X, pady=(0, 5))
        
        self.password_entry = tk.Entry(
            password_frame,
            show="*",
            **self.styles.get_entry_style(),
            font=self.styles.fonts['body_medium']
        )
        self.password_entry.pack(fill=tk.X)
        
        # خيار إظهار كلمة المرور
        show_password_frame = tk.Frame(
            card_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        show_password_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.show_password_var = tk.BooleanVar()
        show_password_check = tk.Checkbutton(
            show_password_frame,
            text="إظهار كلمة المرور",
            variable=self.show_password_var,
            command=self.toggle_password_visibility,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_secondary'],
            font=self.styles.fonts['body_small'],
            selectcolor=self.styles.colors['bg_card'],
            activebackground=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        show_password_check.pack(anchor='w')
        
        # أزرار العمل
        buttons_frame = tk.Frame(
            card_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        buttons_frame.pack(fill=tk.X)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(
            buttons_frame,
            text="🚀 تسجيل الدخول",
            command=self.login,
            **self.styles.get_button_style('primary'),
            font=self.styles.fonts['body_medium']
        )
        login_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر الخروج
        exit_btn = tk.Button(
            buttons_frame,
            text="❌ خروج",
            command=self.exit_app,
            **self.styles.get_button_style('danger'),
            font=self.styles.fonts['body_medium']
        )
        exit_btn.pack(fill=tk.X)
        
        # معلومات المطور
        dev_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        dev_frame.pack(fill=tk.X, pady=(30, 0))
        
        dev_label = tk.Label(
            dev_frame,
            text="المطور: محمد الشوامرة - 📞 0566000140",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['caption']
        )
        dev_label.pack()
        
        # معلومات افتراضية للمدير
        default_info = tk.Label(
            dev_frame,
            text="المدير الافتراضي - المستخدم: admin | كلمة المرور: admin123",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['warning'],
            font=self.styles.fonts['caption']
        )
        default_info.pack(pady=(5, 0))
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda event: self.login())
        self.username_entry.bind('<Return>', lambda event: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda event: self.login())
    
    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.show_password_var.get():
            self.password_entry.configure(show="")
        else:
            self.password_entry.configure(show="*")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # محاولة المصادقة
        if self.auth_manager.authenticate(username, password):
            messagebox.showinfo("نجح", f"مرحباً {self.auth_manager.current_user['full_name']}")
            self.window.destroy()
            self.on_success_callback()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)
            self.username_entry.focus()
    
    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد", "هل تريد الخروج من التطبيق؟"):
            self.window.quit()
            sys.exit()
    
    def on_closing(self):
        """معالجة إغلاق النافذة"""
        self.exit_app()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window.mainloop()
