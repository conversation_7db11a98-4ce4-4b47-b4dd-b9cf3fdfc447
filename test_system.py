#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام Phone Doctor
System Test

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    if not os.path.exists('phone_doctor.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('phone_doctor.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📊 عدد الجداول: {len(tables)}")
        
        # فحص البيانات الأساسية
        cursor.execute('SELECT COUNT(*) FROM users')
        users_count = cursor.fetchone()[0]
        print(f"👥 عدد المستخدمين: {users_count}")
        
        cursor.execute('SELECT COUNT(*) FROM customers')
        customers_count = cursor.fetchone()[0]
        print(f"🏪 عدد العملاء: {customers_count}")
        
        cursor.execute('SELECT COUNT(*) FROM inventory')
        inventory_count = cursor.fetchone()[0]
        print(f"📦 عدد المنتجات: {inventory_count}")
        
        conn.close()
        
        if users_count >= 2 and customers_count > 0 and inventory_count > 0:
            print("✅ قاعدة البيانات تحتوي على البيانات المطلوبة")
            return True
        else:
            print("⚠️ قاعدة البيانات تحتاج إلى بيانات إضافية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_authentication():
    """اختبار نظام المصادقة"""
    print("\n🔐 اختبار نظام المصادقة...")
    
    try:
        from phone_doctor_working import DatabaseManager, AuthManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        if not db_manager.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # إنشاء مدير المصادقة
        auth_manager = AuthManager(db_manager)
        
        # اختبار تسجيل دخول المدير
        print("🔄 اختبار تسجيل دخول المدير...")
        if auth_manager.authenticate('admin', 'admin123'):
            user_name = auth_manager.current_user.get('full_name', 'غير محدد')
            print(f"✅ نجح تسجيل دخول المدير: {user_name}")
            auth_manager.logout()
        else:
            print("❌ فشل تسجيل دخول المدير")
            return False
        
        # اختبار تسجيل دخول موظف المبيعات
        print("🔄 اختبار تسجيل دخول موظف المبيعات...")
        if auth_manager.authenticate('sales', 'sales123'):
            user_name = auth_manager.current_user.get('full_name', 'غير محدد')
            print(f"✅ نجح تسجيل دخول موظف المبيعات: {user_name}")
            auth_manager.logout()
        else:
            print("❌ فشل تسجيل دخول موظف المبيعات")
            return False
        
        # اختبار كلمة مرور خاطئة
        print("🔄 اختبار كلمة مرور خاطئة...")
        if auth_manager.authenticate('admin', 'wrong_password'):
            print("❌ خطأ: تم قبول كلمة مرور خاطئة")
            return False
        else:
            print("✅ تم رفض كلمة المرور الخاطئة بشكل صحيح")
        
        print("✅ جميع اختبارات المصادقة نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المصادقة: {e}")
        return False

def test_imports():
    """اختبار استيراد الوحدات"""
    print("\n📦 اختبار استيراد الوحدات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
        
        import sqlite3
        print("✅ sqlite3 متوفر")
        
        from phone_doctor_working import DatabaseManager, AuthManager, AILoginWindow, MainApplication
        print("✅ جميع وحدات النظام متوفرة")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🚀 بدء اختبار نظام Phone Doctor...")
    print("=" * 50)
    
    # اختبار استيراد الوحدات
    imports_ok = test_imports()
    
    # اختبار قاعدة البيانات
    database_ok = test_database()
    
    # اختبار نظام المصادقة
    auth_ok = test_authentication()
    
    print("\n" + "=" * 50)
    print("📋 نتائج الاختبار:")
    print(f"📦 استيراد الوحدات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"🗄️ قاعدة البيانات: {'✅ نجح' if database_ok else '❌ فشل'}")
    print(f"🔐 نظام المصادقة: {'✅ نجح' if auth_ok else '❌ فشل'}")
    
    if imports_ok and database_ok and auth_ok:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        print("\n🚀 لتشغيل النظام:")
        print("   python phone_doctor_working.py")
        print("\n🔐 بيانات تسجيل الدخول:")
        print("   المدير: admin / admin123")
        print("   المبيعات: sales / sales123")
        return True
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
