#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 Professional Edition - Magic Version
نسخة سحرية مع تأثيرات متطورة وبيانات حقيقية

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import sys
import os
from datetime import datetime
import random

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MagicEffects:
    """كلاس التأثيرات السحرية"""
    
    @staticmethod
    def animate_button_hover(button, enter_color, leave_color, enter_scale=1.05, leave_scale=1.0):
        """تأثير hover للأزرار مع تكبير"""
        def on_enter(e):
            button.configure(bg=enter_color, relief='raised', bd=2)
            # تأثير التكبير (محاكاة)
            button.configure(font=(button['font'].split()[0], int(button['font'].split()[1]) + 1, 'bold'))
        
        def on_leave(e):
            button.configure(bg=leave_color, relief='flat', bd=0)
            # إعادة الحجم الطبيعي
            button.configure(font=(button['font'].split()[0], int(button['font'].split()[1]) - 1, 'normal'))
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
    
    @staticmethod
    def animate_card_hover(frame, shadow_color='#e2e8f0'):
        """تأثير hover للبطاقات"""
        original_relief = frame['relief']
        original_bd = frame['bd']
        
        def on_enter(e):
            frame.configure(relief='raised', bd=3, bg=shadow_color)
        
        def on_leave(e):
            frame.configure(relief=original_relief, bd=original_bd, bg='white')
        
        frame.bind("<Enter>", on_enter)
        frame.bind("<Leave>", on_leave)
    
    @staticmethod
    def pulse_effect(widget, color1, color2, duration=1000):
        """تأثير النبض للعناصر"""
        def pulse():
            current_color = widget['bg']
            new_color = color2 if current_color == color1 else color1
            widget.configure(bg=new_color)
            widget.after(duration, pulse)
        pulse()
    
    @staticmethod
    def fade_in_text(label, text, delay=50):
        """تأثير ظهور النص تدريجياً"""
        def show_char(index=0):
            if index <= len(text):
                label.configure(text=text[:index])
                label.after(delay, lambda: show_char(index + 1))
        show_char()

class DatabaseManager:
    """مدير قاعدة البيانات المحسن"""
    
    def __init__(self):
        self.db_path = 'database/phone_doctor.db'
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if not os.path.exists('database'):
                os.makedirs('database')
            
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        return self.execute_query(query, params)
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة المستخدمين الافتراضيين
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('admin', 'admin123', 'مدير النظام', 'admin'))
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('sales', 'sales123', 'موظف المبيعات', 'sales'))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False

class AuthManager:
    """مدير المصادقة"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.current_user = None
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            users = self.db_manager.fetch_all('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                                            (username, password))
            
            if users:
                user = users[0]
                self.current_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role']
                }
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

class MagicLoginWindow:
    """نافذة تسجيل الدخول السحرية"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.root = None
        self.magic = MagicEffects()
    
    def show(self):
        """عرض نافذة تسجيل الدخول السحرية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Magic Login")
        self.root.geometry("650x750")
        self.root.configure(bg='#0f172a')
        self.root.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # تأثير التدرج في الخلفية
        self.create_gradient_background()

        # العنوان السحري
        self.create_magic_title()

        # نموذج تسجيل الدخول السحري
        self.create_magic_login_form()

        # معلومات تسجيل الدخول السحرية
        self.create_magic_login_info()

        # معلومات المطور السحرية
        self.create_magic_developer_info()

        self.root.mainloop()

    def center_window(self):
        """توسيط النافذة السحرية"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_gradient_background(self):
        """إنشاء خلفية متدرجة"""
        # إطار التدرج الرئيسي
        gradient_frame = tk.Frame(self.root, bg='#1e293b')
        gradient_frame.pack(fill=tk.BOTH, expand=True)
        
        # تأثير النبض للخلفية
        self.magic.pulse_effect(gradient_frame, '#1e293b', '#334155', 3000)
        
        self.main_frame = gradient_frame
    
    def create_magic_title(self):
        """إنشاء العنوان السحري المحسن"""
        title_frame = tk.Frame(self.main_frame, bg='#1e293b')
        title_frame.pack(pady=40)

        # أيقونة سحرية
        magic_icon = tk.Label(
            title_frame,
            text="🔮✨📱✨🔮",
            bg='#1e293b',
            font=('Arial', 32)
        )
        magic_icon.pack()

        # العنوان الرئيسي السحري
        title_label = tk.Label(
            title_frame,
            text="Phone Doctor v2.0",
            bg='#1e293b',
            fg='#60a5fa',
            font=('Arial', 28, 'bold')
        )
        title_label.pack(pady=(10, 0))

        # العنوان الفرعي السحري
        subtitle_label = tk.Label(
            title_frame,
            text="✨ Magic Professional Edition ✨",
            bg='#1e293b',
            fg='#94a3b8',
            font=('Arial', 14, 'italic')
        )
        subtitle_label.pack(pady=(5, 0))

        # خط فاصل سحري
        separator = tk.Frame(title_frame, bg='#8b5cf6', height=3, width=400)
        separator.pack(pady=(15, 0))

        # تأثير ظهور النص تدريجياً
        self.magic.fade_in_text(subtitle_label, "✨ Magic Professional Edition ✨", 80)

        # تأثير النبض للأيقونة
        self.magic.pulse_effect(magic_icon, 'white', '#fbbf24', 2000)
    
    def create_magic_login_form(self):
        """إنشاء نموذج تسجيل الدخول السحري المحسن"""
        # بطاقة سحرية للنموذج
        form_card = tk.Frame(self.main_frame, bg='#1e293b', relief='raised', bd=4)
        form_card.pack(pady=30, padx=40, fill=tk.X)

        # تأثير hover سحري للبطاقة
        self.magic.animate_card_hover(form_card, '#2d3748')

        # عنوان النموذج السحري
        form_title = tk.Label(
            form_card,
            text="🔐✨ بوابة السحر ✨🔐",
            bg='#1e293b',
            fg='#fbbf24',
            font=('Arial', 18, 'bold')
        )
        form_title.pack(pady=(25, 20))

        # حقل اسم المستخدم السحري
        self.create_magic_input_field(form_card, "🧙‍♂️ اسم الساحر:", "username")

        # حقل كلمة المرور السحرية
        self.create_magic_input_field(form_card, "🔮 كلمة السر السحرية:", "password", is_password=True)

        # أزرار سحرية
        self.create_magic_buttons(form_card)

    def create_magic_input_field(self, parent, label_text, field_type, is_password=False):
        """إنشاء حقل إدخال سحري"""
        # إطار الحقل السحري
        field_frame = tk.Frame(parent, bg='#1e293b')
        field_frame.pack(pady=15, padx=40, fill=tk.X)

        # تسمية سحرية
        label = tk.Label(
            field_frame,
            text=label_text,
            bg='#1e293b',
            fg='#e879f9',
            font=('Arial', 13, 'bold'),
            anchor='w'
        )
        label.pack(fill=tk.X, pady=(0, 8))

        # حاوية الإدخال السحرية
        entry_container = tk.Frame(field_frame, bg='#8b5cf6', relief='flat', bd=3)
        entry_container.pack(fill=tk.X, ipady=3)

        # حقل الإدخال السحري
        entry = tk.Entry(
            entry_container,
            font=('Arial', 14, 'bold'),
            bg='#374151',
            fg='#fbbf24',
            relief='flat',
            bd=0,
            insertbackground='#60a5fa',
            show="*" if is_password else ""
        )
        entry.pack(fill=tk.X, padx=12, pady=10)

        # حفظ المرجع
        if field_type == "username":
            self.username_entry = entry
        else:
            self.password_entry = entry

        # تأثيرات سحرية للحقل
        self.add_magic_entry_effects(entry, entry_container, label)

    def add_magic_entry_effects(self, entry, container, label):
        """إضافة تأثيرات سحرية للحقول"""
        def on_focus_in(event):
            container.configure(bg='#fbbf24', bd=4)
            entry.configure(bg='#1e293b', fg='#60a5fa')
            label.configure(fg='#fbbf24')
            # تأثير النبض
            self.magic.pulse_effect(container, '#fbbf24', '#f59e0b', 1000)

        def on_focus_out(event):
            container.configure(bg='#8b5cf6', bd=3)
            entry.configure(bg='#374151', fg='#fbbf24')
            label.configure(fg='#e879f9')

        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)

    def create_magic_buttons(self, parent):
        """إنشاء الأزرار السحرية"""
        buttons_frame = tk.Frame(parent, bg='#1e293b')
        buttons_frame.pack(pady=(25, 35), padx=40, fill=tk.X)

        # زر الدخول السحري الرئيسي
        self.login_btn = tk.Button(
            buttons_frame,
            text="🌟 دخول سحري 🌟",
            command=self.magic_login,
            bg='#8b5cf6',
            fg='white',
            font=('Arial', 16, 'bold'),
            relief='flat',
            bd=0,
            padx=50,
            pady=15,
            cursor='hand2'
        )
        self.login_btn.pack(fill=tk.X, pady=(0, 15))

        # تأثيرات سحرية للزر الرئيسي
        self.magic.animate_button_hover(self.login_btn, '#7c3aed', '#8b5cf6')

        # أزرار سحرية سريعة
        quick_frame = tk.Frame(buttons_frame, bg='#1e293b')
        quick_frame.pack(fill=tk.X)

        # زر الساحر الأعظم
        admin_btn = tk.Button(
            quick_frame,
            text="👑 الساحر الأعظم",
            command=lambda: self.quick_magic_login('admin', 'admin123'),
            bg='#10b981',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        admin_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 8))

        # زر ساحر المبيعات
        sales_btn = tk.Button(
            quick_frame,
            text="🧙‍♀️ ساحر المبيعات",
            command=lambda: self.quick_magic_login('sales', 'sales123'),
            bg='#f59e0b',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        sales_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(8, 0))

        # تأثيرات سحرية للأزرار السريعة
        self.magic.animate_button_hover(admin_btn, '#059669', '#10b981')
        self.magic.animate_button_hover(sales_btn, '#d97706', '#f59e0b')

        # ربط Enter بالدخول السحري
        self.username_entry.focus()
        self.root.bind('<Return>', lambda e: self.magic_login())

    def quick_magic_login(self, username, password):
        """دخول سحري سريع"""
        self.username_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.username_entry.insert(0, username)
        self.password_entry.insert(0, password)
        self.magic_login()
    
    def create_magic_login_info(self):
        """إنشاء معلومات تسجيل الدخول السحرية"""
        info_card = tk.Frame(self.main_frame, bg='#0f172a', relief='raised', bd=3)
        info_card.pack(pady=20, padx=40, fill=tk.X)

        # عنوان المعلومات السحرية
        info_title = tk.Label(
            info_card,
            text="🔮 معلومات البوابة السحرية 🔮",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 16, 'bold')
        )
        info_title.pack(pady=(20, 15))

        # معلومات الساحر الأعظم
        admin_frame = tk.Frame(info_card, bg='#1e293b', relief='flat', bd=2)
        admin_frame.pack(fill=tk.X, padx=20, pady=8)

        admin_info = tk.Label(
            admin_frame,
            text="👑 الساحر الأعظم: admin / admin123\n   ✨ القوى السحرية: كاملة بلا حدود ✨",
            bg='#1e293b',
            fg='#10b981',
            font=('Arial', 12, 'bold'),
            justify=tk.LEFT
        )
        admin_info.pack(pady=12, padx=15, anchor='w')

        # معلومات ساحر المبيعات
        sales_frame = tk.Frame(info_card, bg='#1e293b', relief='flat', bd=2)
        sales_frame.pack(fill=tk.X, padx=20, pady=(8, 20))

        sales_info = tk.Label(
            sales_frame,
            text="🧙‍♀️ ساحر المبيعات: sales / sales123\n   🔮 القوى السحرية: محدودة (تحتاج تعويذة ترخيص) 🔮",
            bg='#1e293b',
            fg='#f59e0b',
            font=('Arial', 12, 'bold'),
            justify=tk.LEFT
        )
        sales_info.pack(pady=12, padx=15, anchor='w')

        # تأثيرات سحرية للمعلومات
        self.magic.pulse_effect(info_title, '#60a5fa', '#8b5cf6', 3000)

    def create_magic_developer_info(self):
        """إنشاء معلومات المطور السحرية"""
        dev_frame = tk.Frame(self.main_frame, bg='#1e293b')
        dev_frame.pack(pady=(15, 0))

        dev_info = tk.Label(
            dev_frame,
            text="🌟 الساحر المطور: محمد الشوامرة - 0566000140 🌟",
            bg='#1e293b',
            fg='#8b5cf6',
            font=('Arial', 11, 'bold', 'italic')
        )
        dev_info.pack()

        version_info = tk.Label(
            dev_frame,
            text="✨ Phone Doctor v2.0 Magic Professional Edition ✨",
            bg='#1e293b',
            fg='#6b7280',
            font=('Arial', 10, 'italic')
        )
        version_info.pack(pady=(5, 0))

        # تأثير سحري لمعلومات المطور
        self.magic.fade_in_text(dev_info, "🌟 الساحر المطور: محمد الشوامرة - 0566000140 🌟", 60)
    
    def magic_login(self):
        """تسجيل الدخول السحري"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            self.show_magic_error("يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # تأثير التحميل
        self.login_btn.configure(text="جاري السحر...", bg='#f59e0b', state='disabled')
        self.root.update()
        
        # محاكاة وقت التحميل
        self.root.after(1000, lambda: self.complete_login(username, password))
    
    def complete_login(self, username, password):
        """إكمال تسجيل الدخول"""
        if self.auth_manager.authenticate(username, password):
            self.login_btn.configure(text="نجح السحر!", bg='#10b981')
            self.root.update()
            self.root.after(500, self.root.destroy)
            self.root.after(600, self.on_success_callback)
        else:
            self.login_btn.configure(text="فشل السحر!", bg='#ef4444')
            self.root.update()
            self.root.after(1500, lambda: [
                self.login_btn.configure(text="دخول سحري", bg='#3b82f6', state='normal'),
                self.show_magic_error("اسم المستخدم أو كلمة المرور غير صحيحة"),
                self.password_entry.delete(0, tk.END)
            ])
    
    def show_magic_error(self, message):
        """عرض رسالة خطأ سحرية"""
        error_window = tk.Toplevel(self.root)
        error_window.title("تنبيه سحري")
        error_window.geometry("300x150")
        error_window.configure(bg='#7f1d1d')
        error_window.transient(self.root)
        error_window.grab_set()
        
        tk.Label(error_window, text="تنبيه!", bg='#7f1d1d', fg='white', 
                font=('Arial', 14, 'bold')).pack(pady=20)
        
        tk.Label(error_window, text=message, bg='#7f1d1d', fg='white', 
                font=('Arial', 11), wraplength=250).pack(pady=10)
        
        ok_btn = tk.Button(error_window, text="حسناً", command=error_window.destroy,
                          bg='#ef4444', fg='white', font=('Arial', 10, 'bold'))
        ok_btn.pack(pady=10)
        
        # تأثير النبض للنافذة
        self.magic.pulse_effect(error_window, '#7f1d1d', '#991b1b', 1000)

class MagicMainApplication:
    """التطبيق الرئيسي السحري"""

    def __init__(self, auth_manager, db_manager):
        self.auth_manager = auth_manager
        self.db_manager = db_manager
        self.root = None
        self.current_page = 'dashboard'
        self.magic = MagicEffects()
        self.content_area = None

    def run(self):
        """تشغيل التطبيق"""
        self.setup_main_window()
        self.root.mainloop()

    def setup_main_window(self):
        """إعداد النافذة الرئيسية السحرية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Magic Professional Edition")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0f172a')
        self.root.state('zoomed')

        # الهيدر السحري
        self.create_magic_header()

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#0f172a')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # الشريط الجانبي السحري
        self.create_magic_sidebar(main_frame)

        # منطقة المحتوى السحرية
        self.content_area = tk.Frame(main_frame, bg='#1e293b', relief='raised', bd=2)
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تحميل لوحة التحكم
        self.load_magic_dashboard()

    def create_magic_header(self):
        """إنشاء الهيدر السحري"""
        header_frame = tk.Frame(self.root, bg='#1e40af', height=80, relief='raised', bd=3)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # تأثير التدرج للهيدر
        self.magic.pulse_effect(header_frame, '#1e40af', '#2563eb', 4000)

        # العنوان
        title_label = tk.Label(
            header_frame,
            text="Phone Doctor v2.0 - Magic Professional Edition",
            bg='transparent',
            fg='white',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=30, pady=20)

        # معلومات المستخدم السحرية
        if self.auth_manager.current_user:
            user_frame = tk.Frame(header_frame, bg='transparent')
            user_frame.pack(side=tk.RIGHT, padx=30, pady=20)

            user_label = tk.Label(
                user_frame,
                text=f"مرحباً، {self.auth_manager.current_user['full_name']}",
                bg='transparent',
                fg='#fbbf24',
                font=('Arial', 14, 'bold')
            )
            user_label.pack()

            role_label = tk.Label(
                user_frame,
                text=f"الدور: {self.auth_manager.current_user['role']}",
                bg='transparent',
                fg='#94a3b8',
                font=('Arial', 10)
            )
            role_label.pack()

            # تأثير ظهور النص
            self.magic.fade_in_text(user_label, f"مرحباً، {self.auth_manager.current_user['full_name']}", 50)

    def create_magic_sidebar(self, parent):
        """إنشاء الشريط الجانبي السحري"""
        sidebar_frame = tk.Frame(parent, bg='#334155', width=280, relief='raised', bd=3)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # عنوان القائمة السحري
        menu_title = tk.Label(
            sidebar_frame,
            text="القائمة السحرية",
            bg='#334155',
            fg='#60a5fa',
            font=('Arial', 16, 'bold')
        )
        menu_title.pack(pady=25)

        # أزرار القائمة السحرية
        menu_items = [
            ("dashboard", "لوحة التحكم", "#3b82f6"),
            ("repairs", "إدارة الصيانة", "#10b981"),
            ("customers", "إدارة العملاء", "#f59e0b"),
            ("checks", "إدارة الشيكات", "#8b5cf6"),
            ("sales", "إدارة المبيعات", "#06b6d4"),
            ("inventory", "إدارة المخزون", "#84cc16"),
            ("reports", "التقارير", "#f97316"),
            ("settings", "الإعدادات", "#6b7280")
        ]

        self.menu_buttons = {}

        for page_id, title, color in menu_items:
            btn_frame = tk.Frame(sidebar_frame, bg='#334155')
            btn_frame.pack(fill=tk.X, padx=15, pady=3)

            btn = tk.Button(
                btn_frame,
                text=title,
                command=lambda p=page_id: self.change_page_with_magic(p),
                bg=color if page_id == self.current_page else '#475569',
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=12,
                anchor='w',
                cursor='hand2'
            )
            btn.pack(fill=tk.X)

            # تأثير hover سحري
            self.magic.animate_button_hover(btn, '#1f2937', color if page_id == self.current_page else '#475569')

            self.menu_buttons[page_id] = btn

        # زر تسجيل الخروج السحري
        logout_frame = tk.Frame(sidebar_frame, bg='#334155')
        logout_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=15)

        logout_btn = tk.Button(
            logout_frame,
            text="تسجيل الخروج",
            command=self.magic_logout,
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=12,
            cursor='hand2'
        )
        logout_btn.pack(fill=tk.X)

        # تأثير hover للخروج
        self.magic.animate_button_hover(logout_btn, '#dc2626', '#ef4444')

    def change_page_with_magic(self, page_id):
        """تغيير الصفحة مع تأثيرات سحرية"""
        # تحديث ألوان الأزرار
        for btn_id, btn in self.menu_buttons.items():
            if btn_id == page_id:
                btn.configure(bg='#3b82f6')
            else:
                btn.configure(bg='#475569')

        self.current_page = page_id

        # تأثير انتقال سحري
        self.fade_out_content()
        self.root.after(300, lambda: self.load_page_content(page_id))

    def fade_out_content(self):
        """تأثير إخفاء المحتوى"""
        for widget in self.content_area.winfo_children():
            widget.configure(bg='#0f172a')
            self.root.after(100, widget.destroy)

    def load_page_content(self, page_id):
        """تحميل محتوى الصفحة"""
        if page_id == "dashboard":
            self.load_magic_dashboard()
        elif page_id == "repairs":
            self.load_magic_repairs()
        elif page_id == "customers":
            self.load_magic_customers()
        elif page_id == "checks":
            self.load_magic_checks()
        else:
            self.load_magic_placeholder(page_id)

    def load_magic_dashboard(self):
        """تحميل لوحة التحكم السحرية"""
        # العنوان السحري
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="لوحة التحكم السحرية",
            bg='#1e293b',
            fg='#60a5fa',
            font=('Arial', 24, 'bold')
        )
        title_label.pack()

        # تأثير ظهور العنوان
        self.magic.fade_in_text(title_label, "لوحة التحكم السحرية", 100)

        # بطاقات الإحصائيات السحرية
        stats_frame = tk.Frame(self.content_area, bg='#1e293b')
        stats_frame.pack(fill=tk.X, padx=20, pady=20)

        # جلب الإحصائيات الحقيقية
        repairs_count = len(self.db_manager.fetch_all("SELECT * FROM repairs"))
        customers_count = len(self.db_manager.fetch_all("SELECT * FROM customers"))
        checks_count = len(self.db_manager.fetch_all("SELECT * FROM checks"))

        stats_data = [
            ("أوامر الصيانة", repairs_count, "#10b981", "🔧"),
            ("العملاء", customers_count, "#3b82f6", "👥"),
            ("الشيكات", checks_count, "#8b5cf6", "💳"),
            ("المبيعات اليوم", "12", "#f59e0b", "💰")
        ]

        for i, (title, value, color, icon) in enumerate(stats_data):
            col = i % 2
            row = i // 2

            card_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew', ipadx=20, ipady=20)

            # تأثير hover للبطاقة
            self.magic.animate_card_hover(card_frame, color)

            # محتوى البطاقة
            icon_label = tk.Label(card_frame, text=icon, bg=color, fg='white', font=('Arial', 32))
            icon_label.pack()

            value_label = tk.Label(card_frame, text=str(value), bg=color, fg='white', font=('Arial', 28, 'bold'))
            value_label.pack()

            title_label = tk.Label(card_frame, text=title, bg=color, fg='white', font=('Arial', 14, 'bold'))
            title_label.pack()

            # تأثير النبض للقيم
            self.magic.pulse_effect(value_label, 'white', '#f1f5f9', 2000)

        # تكوين الشبكة
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)

        # رسالة ترحيب سحرية
        welcome_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        welcome_text = f"""
مرحباً بك في Phone Doctor v2.0 Magic Edition

المستخدم: {self.auth_manager.current_user['full_name']}
الدور: {self.auth_manager.current_user['role']}

النظام يحتوي على:
• {repairs_count} أمر صيانة
• {customers_count} عميل
• {checks_count} شيك

جميع البيانات حقيقية ومتاحة للعرض والتعديل
        """

        welcome_label = tk.Label(
            welcome_frame,
            text=welcome_text,
            bg='#374151',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        welcome_label.pack(expand=True)

        # تأثير ظهور النص التدريجي
        self.magic.fade_in_text(welcome_label, welcome_text, 30)

    def load_magic_repairs(self):
        """تحميل صفحة الصيانة السحرية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="🔧 إدارة الصيانة السحرية",
            bg='#1e293b',
            fg='#10b981',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات السحرية
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة صيانة", "#10b981", self.add_repair),
            ("تعديل", "#3b82f6", self.edit_repair),
            ("حذف", "#ef4444", self.delete_repair),
            ("تقارير", "#8b5cf6", self.show_reports)
        ]

        for text, color, command in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

            # تأثير hover سحري
            self.magic.animate_button_hover(btn, '#1f2937', color)

        # جدول الصيانة السحري
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان الجدول
        table_title = tk.Label(
            table_frame,
            text="قائمة أوامر الصيانة",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول مع التمرير
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "العميل", "الجهاز", "المشكلة", "الحالة", "التاريخ")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تعيين العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        repairs = self.db_manager.fetch_all("""
            SELECT id, customer_name, device_type, device_brand, problem_description,
                   status, created_date
            FROM repairs
            ORDER BY id DESC
            LIMIT 20
        """)

        # إضافة البيانات للجدول
        for repair in repairs:
            device = f"{repair['device_brand'] or ''} {repair['device_type']}".strip()
            problem = repair['problem_description'][:30] + "..." if len(repair['problem_description']) > 30 else repair['problem_description']
            date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'

            tree.insert('', 'end', values=(
                repair['id'],
                repair['customer_name'],
                device,
                problem,
                repair['status'],
                date
            ))

        # إحصائيات سريعة
        stats_label = tk.Label(
            table_frame,
            text=f"إجمالي أوامر الصيانة: {len(repairs)} (عرض أحدث 20)",
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_magic_customers(self):
        """تحميل صفحة العملاء السحرية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="👥 إدارة العملاء السحرية",
            bg='#1e293b',
            fg='#3b82f6',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة عميل", "#3b82f6", self.add_customer),
            ("تعديل", "#10b981", self.edit_customer),
            ("حذف", "#ef4444", self.delete_customer),
            ("الحسابات المالية", "#f59e0b", self.show_financial_accounts)
        ]

        for text, color, command in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))
            self.magic.animate_button_hover(btn, '#1f2937', color)

        # جدول العملاء
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة العملاء والحسابات المالية",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "الاسم", "الهاتف", "البريد", "إجمالي المشتريات", "المدفوع", "الدين")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        customers = self.db_manager.fetch_all("""
            SELECT c.*,
                   COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END), 0) as total_purchases,
                   COALESCE(SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as total_paid
            FROM customers c
            LEFT JOIN customer_transactions ct ON c.id = ct.customer_id
            GROUP BY c.id
            ORDER BY c.id
        """)

        total_debt = 0
        total_paid = 0

        for customer in customers:
            purchases = customer['total_purchases'] or 0
            paid = customer['total_paid'] or 0
            debt = max(0, purchases - paid)

            total_debt += debt
            total_paid += paid

            tree.insert('', 'end', values=(
                customer['id'],
                customer['name'],
                customer['phone'],
                customer['email'] or 'غير محدد',
                f"{purchases:.2f} ₪",
                f"{paid:.2f} ₪",
                f"{debt:.2f} ₪" if debt > 0 else "لا يوجد"
            ))

        # إحصائيات العملاء
        stats_text = f"إجمالي العملاء: {len(customers)} | إجمالي الديون: {total_debt:.2f} ₪ | إجمالي المدفوع: {total_paid:.2f} ₪"
        stats_label = tk.Label(
            table_frame,
            text=stats_text,
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_magic_checks(self):
        """تحميل صفحة الشيكات السحرية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="💳 إدارة الشيكات السحرية",
            bg='#1e293b',
            fg='#8b5cf6',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة شيك", "#8b5cf6", self.add_check),
            ("تحصيل", "#10b981", self.cash_check),
            ("مرتد", "#ef4444", self.bounce_check),
            ("تقرير الشيكات", "#f59e0b", self.checks_report)
        ]

        for text, color, command in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))
            self.magic.animate_button_hover(btn, '#1f2937', color)

        # جدول الشيكات
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة الشيكات",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "رقم الشيك", "المبلغ", "البنك", "تاريخ الاستحقاق", "المستفيد", "الدافع", "الحالة")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        checks = self.db_manager.fetch_all("""
            SELECT * FROM checks ORDER BY id DESC
        """)

        total_amount = 0
        pending_count = 0
        cashed_count = 0

        for check in checks:
            total_amount += check['amount']
            if check['status'] == 'معلق':
                pending_count += 1
            elif check['status'] == 'محصل':
                cashed_count += 1

            tree.insert('', 'end', values=(
                check['id'],
                check['check_number'],
                f"{check['amount']:.2f} ₪",
                check['bank_name'],
                check['due_date'][:10] if check['due_date'] else 'غير محدد',
                check['payee'],
                check['payer'],
                check['status']
            ))

        # إحصائيات الشيكات
        stats_text = f"إجمالي الشيكات: {len(checks)} | معلقة: {pending_count} | محصلة: {cashed_count} | إجمالي المبلغ: {total_amount:.2f} ₪"
        stats_label = tk.Label(
            table_frame,
            text=stats_text,
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_magic_placeholder(self, page_name):
        """تحميل صفحة مؤقتة سحرية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text=f"صفحة {page_name} السحرية",
            bg='#1e293b',
            fg='#f59e0b',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # محتوى الصفحة
        content_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        content_text = f"""
صفحة {page_name} قيد التطوير

هذه الصفحة ستحتوي على:
• واجهة متطورة مع تأثيرات سحرية
• إدارة شاملة للبيانات
• تقارير تفاعلية
• تصميم احترافي

سيتم إضافة المزيد من الميزات قريباً...
        """

        content_label = tk.Label(
            content_frame,
            text=content_text,
            bg='#374151',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        content_label.pack(expand=True)

        # تأثير ظهور النص
        self.magic.fade_in_text(content_label, content_text, 50)

    # دوال العمليات (مؤقتة)
    def add_repair(self):
        self.show_magic_message("إضافة صيانة", "ميزة إضافة أوامر الصيانة قيد التطوير")

    def edit_repair(self):
        self.show_magic_message("تعديل صيانة", "ميزة تعديل أوامر الصيانة قيد التطوير")

    def delete_repair(self):
        self.show_magic_message("حذف صيانة", "ميزة حذف أوامر الصيانة قيد التطوير")

    def show_reports(self):
        self.show_magic_message("التقارير", "نظام التقارير المتقدم قيد التطوير")

    def add_customer(self):
        self.show_magic_message("إضافة عميل", "ميزة إضافة العملاء قيد التطوير")

    def edit_customer(self):
        self.show_magic_message("تعديل عميل", "ميزة تعديل العملاء قيد التطوير")

    def delete_customer(self):
        self.show_magic_message("حذف عميل", "ميزة حذف العملاء قيد التطوير")

    def show_financial_accounts(self):
        self.show_magic_message("الحسابات المالية", "نظام الحسابات المالية المتقدم قيد التطوير")

    def add_check(self):
        self.show_magic_message("إضافة شيك", "ميزة إضافة الشيكات قيد التطوير")

    def cash_check(self):
        self.show_magic_message("تحصيل شيك", "ميزة تحصيل الشيكات قيد التطوير")

    def bounce_check(self):
        self.show_magic_message("شيك مرتد", "ميزة الشيكات المرتدة قيد التطوير")

    def checks_report(self):
        self.show_magic_message("تقرير الشيكات", "تقارير الشيكات المتقدمة قيد التطوير")

    def show_magic_message(self, title, message):
        """عرض رسالة سحرية"""
        msg_window = tk.Toplevel(self.root)
        msg_window.title(title)
        msg_window.geometry("400x200")
        msg_window.configure(bg='#1e293b')
        msg_window.transient(self.root)
        msg_window.grab_set()

        # تأثير النبض للنافذة
        self.magic.pulse_effect(msg_window, '#1e293b', '#374151', 2000)

        title_label = tk.Label(
            msg_window,
            text=title,
            bg='#1e293b',
            fg='#60a5fa',
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=20)

        message_label = tk.Label(
            msg_window,
            text=message,
            bg='#1e293b',
            fg='white',
            font=('Arial', 12),
            wraplength=350
        )
        message_label.pack(pady=20)

        ok_btn = tk.Button(
            msg_window,
            text="حسناً",
            command=msg_window.destroy,
            bg='#3b82f6',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        ok_btn.pack(pady=20)

        # تأثير hover للزر
        self.magic.animate_button_hover(ok_btn, '#2563eb', '#3b82f6')

    def magic_logout(self):
        """تسجيل الخروج السحري"""
        logout_window = tk.Toplevel(self.root)
        logout_window.title("تسجيل الخروج")
        logout_window.geometry("300x150")
        logout_window.configure(bg='#7f1d1d')
        logout_window.transient(self.root)
        logout_window.grab_set()

        tk.Label(
            logout_window,
            text="هل تريد تسجيل الخروج؟",
            bg='#7f1d1d',
            fg='white',
            font=('Arial', 14, 'bold')
        ).pack(pady=20)

        buttons_frame = tk.Frame(logout_window, bg='#7f1d1d')
        buttons_frame.pack(pady=20)

        yes_btn = tk.Button(
            buttons_frame,
            text="نعم",
            command=lambda: [logout_window.destroy(), self.confirm_logout()],
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=5
        )
        yes_btn.pack(side=tk.LEFT, padx=10)

        no_btn = tk.Button(
            buttons_frame,
            text="لا",
            command=logout_window.destroy,
            bg='#6b7280',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=5
        )
        no_btn.pack(side=tk.LEFT, padx=10)

        # تأثيرات hover
        self.magic.animate_button_hover(yes_btn, '#dc2626', '#ef4444')
        self.magic.animate_button_hover(no_btn, '#4b5563', '#6b7280')

    def confirm_logout(self):
        """تأكيد تسجيل الخروج"""
        self.auth_manager.logout()
        self.root.destroy()
        main()

def main():
    """الدالة الرئيسية السحرية"""
    print("بدء تشغيل Phone Doctor v2.0 Magic Edition...")

    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    if not db_manager.initialize_database():
        messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
        return

    # إنشاء مدير المصادقة
    auth_manager = AuthManager(db_manager)

    def on_login_success():
        """عند نجاح تسجيل الدخول"""
        app = MagicMainApplication(auth_manager, db_manager)
        app.run()

    # عرض نافذة تسجيل الدخول السحرية
    login_window = MagicLoginWindow(auth_manager, on_login_success)
    login_window.show()

if __name__ == "__main__":
    main()
