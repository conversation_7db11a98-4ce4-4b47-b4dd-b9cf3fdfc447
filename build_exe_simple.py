#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تجميع بسيط لـ Phone Doctor EXE
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✅ تم تثبيت PyInstaller")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def build_exe():
    """تجميع EXE"""
    print("🔨 تجميع الملف التنفيذي...")
    
    # تنظيف الملفات المؤقتة
    for folder in ['build', 'dist']:
        if os.path.exists(folder):
            shutil.rmtree(folder)
    
    # أمر PyInstaller
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--windowed',
        '--name=PhoneDoctor_Modern',
        '--add-data=ui;ui',
        '--add-data=database;database', 
        '--add-data=utils;utils',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        'main_modern.py'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم تجميع الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في التجميع:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 تجميع Phone Doctor إلى EXE")
    print("=" * 40)
    
    # التحقق من PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__}")
    except ImportError:
        if not install_pyinstaller():
            return False
    
    # تجميع EXE
    if build_exe():
        print("\n🎉 تم التجميع بنجاح!")
        print("📁 الملف في: dist/PhoneDoctor_Modern.exe")
    else:
        print("\n❌ فشل في التجميع")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
