#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الصيانة المبسطة - Phone Doctor v2.0
Simple Repairs Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3

class RepairsManager:
    """مدير الصيانة المبسط"""
    
    def __init__(self, parent_frame, db_manager, current_user=None):
        print("🔧 تهيئة مدير الصيانة المبسط...")
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.current_user = current_user or {'username': 'admin', 'full_name': 'المدير'}
        self.selected_repair = None
        
        # ألوان النظام
        self.colors = {
            'primary': '#6366f1',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'info': '#06b6d4',
            'bg_light': '#f8fafc',
            'text_dark': '#1f2937'
        }
        
        self.setup_ui()
        self.load_repairs()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المبسطة"""
        # تنظيف الإطار
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي
        header_frame = tk.Frame(self.parent_frame, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(
            header_frame,
            text="🔧 إدارة الصيانة",
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 20, 'bold')
        )
        title_label.pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent_frame, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # شريط الأدوات
        toolbar_frame = tk.Frame(main_frame, bg='white')
        toolbar_frame.pack(fill=tk.X, pady=(0, 20))
        
        # أزرار الأدوات
        add_btn = tk.Button(
            toolbar_frame,
            text="➕ إضافة صيانة",
            command=self.add_repair,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        edit_btn = tk.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_repair,
            bg=self.colors['info'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        delete_btn = tk.Button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_repair,
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        refresh_btn = tk.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_repairs,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.LEFT)
        
        # البحث
        search_frame = tk.Frame(main_frame, bg='white')
        search_frame.pack(fill=tk.X, pady=(0, 20))
        
        search_label = tk.Label(
            search_frame,
            text="🔍 البحث:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Arial', 12),
            width=30
        )
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # جدول الصيانة
        table_frame = tk.Frame(main_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ("ID", "العميل", "الهاتف", "الجهاز", "المشكلة", "الحالة", "التاريخ")
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين العناوين
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.edit_repair)
        
        # شريط الحالة
        status_frame = tk.Frame(self.parent_frame, bg=self.colors['bg_light'], height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            bg=self.colors['bg_light'],
            fg=self.colors['text_dark'],
            font=('Arial', 10)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.count_label = tk.Label(
            status_frame,
            text="",
            bg=self.colors['bg_light'],
            fg=self.colors['primary'],
            font=('Arial', 10, 'bold')
        )
        self.count_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def load_repairs(self):
        """تحميل قائمة الصيانة"""
        try:
            print("🔄 تحميل بيانات الصيانة...")
            
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # الاتصال المباشر بقاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = """
                SELECT id, customer_name, customer_phone, device_type, 
                       problem_description, status, created_date
                FROM repairs ORDER BY id DESC
            """
            
            cursor.execute(query)
            repairs = cursor.fetchall()
            conn.close()
            
            print(f"📊 تم جلب {len(repairs)} أمر صيانة")
            
            # إضافة البيانات للجدول
            for repair in repairs:
                # تحويل الحالة للعربية
                status_map = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }
                
                status_display = status_map.get(repair['status'], repair['status'])
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'
                
                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    repair['customer_phone'],
                    repair['device_type'],
                    repair['problem_description'][:30] + "..." if len(repair['problem_description']) > 30 else repair['problem_description'],
                    status_display,
                    created_date
                ))
            
            # تحديث العداد
            self.count_label.configure(text=f"إجمالي: {len(repairs)} أمر صيانة")
            self.status_label.configure(text="تم تحميل البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل الصيانة: {e}")
            self.status_label.configure(text="خطأ في تحميل البيانات")
    
    def on_search(self, event=None):
        """البحث في الصيانة"""
        search_term = self.search_var.get().strip().lower()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not search_term:
            self.load_repairs()
            return
        
        try:
            # البحث في قاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = """
                SELECT id, customer_name, customer_phone, device_type, 
                       problem_description, status, created_date
                FROM repairs 
                WHERE LOWER(customer_name) LIKE ? 
                   OR LOWER(customer_phone) LIKE ?
                   OR LOWER(device_type) LIKE ?
                   OR LOWER(problem_description) LIKE ?
                ORDER BY id DESC
            """
            
            search_pattern = f"%{search_term}%"
            cursor.execute(query, (search_pattern, search_pattern, search_pattern, search_pattern))
            repairs = cursor.fetchall()
            conn.close()
            
            # إضافة النتائج للجدول
            for repair in repairs:
                status_map = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }
                
                status_display = status_map.get(repair['status'], repair['status'])
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'
                
                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    repair['customer_phone'],
                    repair['device_type'],
                    repair['problem_description'][:30] + "..." if len(repair['problem_description']) > 30 else repair['problem_description'],
                    status_display,
                    created_date
                ))
            
            self.count_label.configure(text=f"نتائج البحث: {len(repairs)}")
            
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            self.status_label.configure(text="خطأ في البحث")
    
    def on_select(self, event):
        """معالجة اختيار صيانة من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_repair = {
                    'id': values[0],
                    'customer_name': values[1],
                    'customer_phone': values[2],
                    'device_type': values[3],
                    'problem_description': values[4],
                    'status': values[5]
                }
                self.status_label.configure(text=f"تم اختيار أمر الصيانة رقم: {values[0]}")
    
    def add_repair(self):
        """إضافة أمر صيانة جديد"""
        self.status_label.configure(text="ميزة إضافة الصيانة قيد التطوير")
    
    def edit_repair(self, event=None):
        """تعديل أمر صيانة"""
        if not self.selected_repair:
            self.status_label.configure(text="يرجى اختيار أمر صيانة للتعديل")
            return
        
        self.status_label.configure(text="ميزة تعديل الصيانة قيد التطوير")
    
    def delete_repair(self):
        """حذف أمر صيانة"""
        if not self.selected_repair:
            self.status_label.configure(text="يرجى اختيار أمر صيانة للحذف")
            return
        
        try:
            # حذف من قاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repairs WHERE id = ?", (self.selected_repair['id'],))
            conn.commit()
            conn.close()
            
            self.selected_repair = None
            self.load_repairs()
            self.status_label.configure(text="تم حذف أمر الصيانة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في حذف الصيانة: {e}")
            self.status_label.configure(text="فشل في حذف أمر الصيانة")
