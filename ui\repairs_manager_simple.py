#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الصيانة المتطورة - Phone Doctor v2.0
Advanced Repairs Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.theme_manager import ThemeManager

class RepairsManager:
    """مدير الصيانة المتطور"""

    def __init__(self, parent_frame, db_manager, current_user=None):
        print("🔧 تهيئة مدير الصيانة المتطور...")
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.current_user = current_user or {'username': 'admin', 'full_name': 'المدير'}
        self.selected_repair = None

        # تهيئة مدير الثيمات
        self.theme = ThemeManager()

        # متغيرات الحالة
        self.current_filter = 'all'
        self.current_sort = 'newest'
        self.is_loading = False

        self.setup_ui()
        self.load_repairs()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتطورة"""
        # تنظيف الإطار
        for widget in self.parent_frame.winfo_children():
            widget.destroy()

        # تطبيق النمط العصري
        self.parent_frame.configure(bg=self.theme.colors['bg_primary'])

        # الهيدر المتطور مع تدرج لوني
        header_frame = self.theme.create_gradient_frame(
            self.parent_frame,
            self.theme.colors['gradient_primary'],
            height=100
        )
        header_frame.pack(fill=tk.X)

        # محتوى الهيدر
        header_content = tk.Frame(header_frame, bg='transparent')
        header_content.place(relx=0.5, rely=0.5, anchor='center')

        # أيقونة وعنوان
        title_frame = tk.Frame(header_content, bg='transparent')
        title_frame.pack()

        icon_label = tk.Label(
            title_frame,
            text=self.theme.icons['repairs'],
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=('Arial', 32)
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 15))

        title_label = tk.Label(
            title_frame,
            text="إدارة الصيانة المتطورة",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['title_large']
        )
        title_label.pack(side=tk.LEFT)

        # معلومات المستخدم
        user_info = tk.Label(
            header_content,
            text=f"{self.theme.icons['user']} {self.current_user.get('full_name', 'المستخدم')}",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['body_medium']
        )
        user_info.pack(pady=(10, 0))
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent_frame, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)

        # بطاقة شريط الأدوات
        toolbar_card, toolbar_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['settings']} أدوات الإدارة"
        )
        toolbar_card.pack(fill=tk.X, pady=(0, 20))

        # الصف الأول من الأزرار
        buttons_row1 = tk.Frame(toolbar_content, bg=self.theme.colors['bg_card'])
        buttons_row1.pack(fill=tk.X, pady=(0, 10))

        # أزرار العمليات الأساسية
        add_btn = self.theme.create_modern_button(
            buttons_row1,
            "إضافة صيانة",
            command=self.add_repair,
            style='success',
            size='medium',
            icon='add'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        edit_btn = self.theme.create_modern_button(
            buttons_row1,
            "تعديل",
            command=self.edit_repair,
            style='info',
            size='medium',
            icon='edit'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        delete_btn = self.theme.create_modern_button(
            buttons_row1,
            "حذف",
            command=self.delete_repair,
            style='danger',
            size='medium',
            icon='delete'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # الصف الثاني من الأزرار
        buttons_row2 = tk.Frame(toolbar_content, bg=self.theme.colors['bg_card'])
        buttons_row2.pack(fill=tk.X)

        refresh_btn = self.theme.create_modern_button(
            buttons_row2,
            "تحديث",
            command=self.load_repairs,
            style='warning',
            size='medium',
            icon='refresh'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        export_btn = self.theme.create_modern_button(
            buttons_row2,
            "تصدير",
            command=self.export_data,
            style='secondary',
            size='medium',
            icon='export'
        )
        export_btn.pack(side=tk.LEFT, padx=(0, 10))

        print_btn = self.theme.create_modern_button(
            buttons_row2,
            "طباعة",
            command=self.print_report,
            style='secondary',
            size='medium',
            icon='print'
        )
        print_btn.pack(side=tk.LEFT)
        
        # بطاقة البحث والفلترة المتطورة
        search_card, search_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['search']} البحث والفلترة المتقدمة"
        )
        search_card.pack(fill=tk.X, pady=(0, 20))

        # الصف الأول - البحث الرئيسي
        search_row1 = tk.Frame(search_content, bg=self.theme.colors['bg_card'])
        search_row1.pack(fill=tk.X, pady=(0, 15))

        # حقل البحث المتطور
        search_label = tk.Label(
            search_row1,
            text=f"{self.theme.icons['search']} البحث السريع:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['heading_small']
        )
        search_label.pack(side=tk.LEFT, padx=(0, 15))

        search_input_frame, self.search_entry = self.theme.create_modern_input(
            search_row1,
            placeholder="ابحث في الاسم، الهاتف، نوع الجهاز، أو المشكلة...",
            size='large'
        )
        search_input_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))

        # زر البحث
        search_btn = self.theme.create_modern_button(
            search_row1,
            "بحث",
            command=self.perform_search,
            style='primary',
            size='medium',
            icon='search'
        )
        search_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر مسح البحث
        clear_btn = self.theme.create_modern_button(
            search_row1,
            "مسح",
            command=self.clear_search,
            style='secondary',
            size='medium',
            icon='cancel'
        )
        clear_btn.pack(side=tk.LEFT)

        # الصف الثاني - الفلاتر المتقدمة
        filters_row = tk.Frame(search_content, bg=self.theme.colors['bg_card'])
        filters_row.pack(fill=tk.X)

        # فلتر الحالة
        status_label = tk.Label(
            filters_row,
            text=f"{self.theme.icons['filter']} الحالة:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        status_label.pack(side=tk.LEFT, padx=(0, 10))

        self.status_filter_var = tk.StringVar(value="الكل")
        status_combo = ttk.Combobox(
            filters_row,
            textvariable=self.status_filter_var,
            values=["الكل", "قيد الانتظار", "قيد الإصلاح", "انتظار قطع غيار", "قيد الاختبار", "تم الإصلاح", "تم التسليم", "ملغي"],
            font=self.theme.fonts['body_medium'],
            width=15,
            state='readonly'
        )
        status_combo.pack(side=tk.LEFT, padx=(0, 20))
        status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)

        # فلتر الأولوية
        priority_label = tk.Label(
            filters_row,
            text=f"{self.theme.icons['warning']} الأولوية:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        priority_label.pack(side=tk.LEFT, padx=(0, 10))

        self.priority_filter_var = tk.StringVar(value="الكل")
        priority_combo = ttk.Combobox(
            filters_row,
            textvariable=self.priority_filter_var,
            values=["الكل", "منخفضة", "عادية", "عالية", "عاجلة"],
            font=self.theme.fonts['body_medium'],
            width=12,
            state='readonly'
        )
        priority_combo.pack(side=tk.LEFT, padx=(0, 20))
        priority_combo.bind('<<ComboboxSelected>>', self.on_filter_change)

        # فلتر التاريخ
        date_label = tk.Label(
            filters_row,
            text=f"{self.theme.icons['calendar']} الفترة:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        date_label.pack(side=tk.LEFT, padx=(0, 10))

        self.date_filter_var = tk.StringVar(value="الكل")
        date_combo = ttk.Combobox(
            filters_row,
            textvariable=self.date_filter_var,
            values=["الكل", "اليوم", "هذا الأسبوع", "هذا الشهر", "آخر 3 أشهر"],
            font=self.theme.fonts['body_medium'],
            width=12,
            state='readonly'
        )
        date_combo.pack(side=tk.LEFT)
        date_combo.bind('<<ComboboxSelected>>', self.on_filter_change)

        # ربط البحث الفوري
        self.search_var = tk.StringVar()
        self.search_entry.configure(textvariable=self.search_var)
        self.search_var.trace('w', self.on_search_change)
        
        # بطاقة الإحصائيات السريعة
        stats_card, stats_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['dashboard']} الإحصائيات السريعة"
        )
        stats_card.pack(fill=tk.X, pady=(0, 20))

        # إطار الإحصائيات
        stats_frame = tk.Frame(stats_content, bg=self.theme.colors['bg_card'])
        stats_frame.pack(fill=tk.X)

        # إحصائيات مختلفة
        self.stats_labels = {}
        stats_data = [
            ("total", "الإجمالي", "info", "dashboard"),
            ("pending", "قيد الانتظار", "warning", "pending"),
            ("in_progress", "قيد الإصلاح", "info", "loading"),
            ("completed", "مكتملة", "success", "completed"),
        ]

        for i, (key, label, style, icon) in enumerate(stats_data):
            stat_frame = tk.Frame(stats_frame, bg=self.theme.colors['bg_card'])
            stat_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10 if i < len(stats_data)-1 else 0))

            # أيقونة الإحصائية
            icon_label = tk.Label(
                stat_frame,
                text=self.theme.icons[icon],
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors[style],
                font=('Arial', 20)
            )
            icon_label.pack()

            # قيمة الإحصائية
            value_label = tk.Label(
                stat_frame,
                text="0",
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_primary'],
                font=self.theme.fonts['title_medium']
            )
            value_label.pack()

            # تسمية الإحصائية
            name_label = tk.Label(
                stat_frame,
                text=label,
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_secondary'],
                font=self.theme.fonts['body_small']
            )
            name_label.pack()

            self.stats_labels[key] = value_label

        # بطاقة جدول الصيانة
        table_card, table_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['repairs']} قائمة أوامر الصيانة"
        )
        table_card.pack(fill=tk.BOTH, expand=True)

        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(table_content, bg=self.theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الجدول المتطور
        columns = ("ID", "العميل", "الهاتف", "الجهاز", "المشكلة", "الحالة", "الأولوية", "التاريخ")
        self.tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15,
            style='Modern.Treeview'
        )

        # تعيين العناوين والعرض
        column_widths = [60, 120, 100, 120, 200, 100, 80, 100]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=f"{self.get_column_icon(col)} {col}")
            self.tree.column(col, width=column_widths[i], anchor='center')

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # تلوين الصفوف حسب الحالة
        self.tree.tag_configure('pending', background='#fef3c7', foreground='#92400e')
        self.tree.tag_configure('in_progress', background='#dbeafe', foreground='#1e40af')
        self.tree.tag_configure('completed', background='#d1fae5', foreground='#065f46')
        self.tree.tag_configure('cancelled', background='#fee2e2', foreground='#991b1b')
        self.tree.tag_configure('urgent', background='#fecaca', foreground='#7f1d1d')

        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.edit_repair)
        self.tree.bind('<Button-3>', self.show_context_menu)  # قائمة السياق
        
        # شريط الحالة المتطور
        status_frame = tk.Frame(
            self.parent_frame,
            bg=self.theme.colors['bg_tertiary'],
            height=50
        )
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)

        # محتوى شريط الحالة
        status_content = tk.Frame(status_frame, bg=self.theme.colors['bg_tertiary'])
        status_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # حالة النظام
        self.status_label = tk.Label(
            status_content,
            text=f"{self.theme.icons['success']} النظام جاهز",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        self.status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        info_frame = tk.Frame(status_content, bg=self.theme.colors['bg_tertiary'])
        info_frame.pack(side=tk.RIGHT)

        # وقت آخر تحديث
        self.last_update_label = tk.Label(
            info_frame,
            text=f"{self.theme.icons['clock']} آخر تحديث: --",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_secondary'],
            font=self.theme.fonts['body_small']
        )
        self.last_update_label.pack(side=tk.RIGHT, padx=(20, 0))

        # عداد النتائج
        self.count_label = tk.Label(
            info_frame,
            text="",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['primary'],
            font=self.theme.fonts['body_medium']
        )
        self.count_label.pack(side=tk.RIGHT, padx=(20, 0))

    def get_column_icon(self, column):
        """الحصول على أيقونة العمود"""
        icons = {
            "ID": self.theme.icons['info'],
            "العميل": self.theme.icons['customer'],
            "الهاتف": self.theme.icons['phone_number'],
            "الجهاز": self.theme.icons['phone'],
            "المشكلة": self.theme.icons['warning'],
            "الحالة": self.theme.icons['info'],
            "الأولوية": self.theme.icons['star'],
            "التاريخ": self.theme.icons['calendar']
        }
        return icons.get(column, "")
    
    def load_repairs(self):
        """تحميل قائمة الصيانة مع الإحصائيات"""
        try:
            print("🔄 تحميل بيانات الصيانة...")
            self.is_loading = True
            self.update_status(f"{self.theme.icons['loading']} جاري تحميل البيانات...", "info")

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # الاتصال المباشر بقاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = """
                SELECT id, customer_name, customer_phone, device_type, device_brand,
                       problem_description, status, priority, created_date, estimated_cost
                FROM repairs ORDER BY id DESC
            """

            cursor.execute(query)
            repairs = cursor.fetchall()
            conn.close()

            print(f"📊 تم جلب {len(repairs)} أمر صيانة")

            # حساب الإحصائيات
            stats = {
                'total': len(repairs),
                'pending': 0,
                'in_progress': 0,
                'completed': 0,
                'cancelled': 0
            }

            # إضافة البيانات للجدول وحساب الإحصائيات
            for repair in repairs:
                # تحويل الحالة للعربية
                status_map = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }

                # تحويل الأولوية للعربية
                priority_map = {
                    'low': 'منخفضة',
                    'normal': 'عادية',
                    'high': 'عالية',
                    'urgent': 'عاجلة'
                }

                status_display = status_map.get(repair['status'], repair['status'])
                priority_display = priority_map.get(repair['priority'], repair['priority'] or 'عادية')
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'

                # تحديد التاغ للتلوين
                tag = self.get_row_tag(repair['status'], repair['priority'])

                # إضافة للجدول
                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    repair['customer_phone'],
                    f"{repair['device_brand'] or ''} {repair['device_type']}".strip(),
                    repair['problem_description'][:35] + "..." if repair['problem_description'] and len(repair['problem_description']) > 35 else (repair['problem_description'] or ''),
                    status_display,
                    priority_display,
                    created_date
                ), tags=(tag,))

                # تحديث الإحصائيات
                if repair['status'] == 'pending':
                    stats['pending'] += 1
                elif repair['status'] == 'in_progress':
                    stats['in_progress'] += 1
                elif repair['status'] in ['completed', 'delivered']:
                    stats['completed'] += 1
                elif repair['status'] == 'cancelled':
                    stats['cancelled'] += 1

            # تحديث الإحصائيات في الواجهة
            self.update_statistics(stats)

            # تحديث العداد وشريط الحالة
            self.count_label.configure(text=f"{self.theme.icons['dashboard']} إجمالي: {len(repairs)} أمر صيانة")
            self.update_status(f"{self.theme.icons['success']} تم تحميل {len(repairs)} أمر صيانة بنجاح", "success")

            # تحديث وقت آخر تحديث
            current_time = datetime.now().strftime("%H:%M:%S")
            self.last_update_label.configure(text=f"{self.theme.icons['clock']} آخر تحديث: {current_time}")

            self.is_loading = False

        except Exception as e:
            print(f"❌ خطأ في تحميل الصيانة: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في تحميل البيانات: {str(e)}", "error")
            self.is_loading = False
    
    def get_row_tag(self, status, priority):
        """تحديد تاغ الصف للتلوين"""
        if priority == 'urgent':
            return 'urgent'
        elif status == 'pending':
            return 'pending'
        elif status == 'in_progress':
            return 'in_progress'
        elif status in ['completed', 'delivered']:
            return 'completed'
        elif status == 'cancelled':
            return 'cancelled'
        else:
            return ''

    def update_statistics(self, stats):
        """تحديث الإحصائيات في الواجهة"""
        for key, value in stats.items():
            if key in self.stats_labels:
                self.stats_labels[key].configure(text=str(value))

    def update_status(self, message, status_type="info"):
        """تحديث شريط الحالة"""
        icons = {
            "success": self.theme.icons['success'],
            "error": self.theme.icons['error'],
            "warning": self.theme.icons['warning'],
            "info": self.theme.icons['info'],
            "loading": self.theme.icons['loading']
        }

        colors = {
            "success": self.theme.colors['success'],
            "error": self.theme.colors['danger'],
            "warning": self.theme.colors['warning'],
            "info": self.theme.colors['info'],
            "loading": self.theme.colors['secondary']
        }

        icon = icons.get(status_type, self.theme.icons['info'])
        color = colors.get(status_type, self.theme.colors['text_primary'])

        self.status_label.configure(
            text=f"{icon} {message}",
            fg=color
        )

    def on_search_change(self, *args):
        """معالجة تغيير البحث الفوري"""
        if not self.is_loading:
            self.parent_frame.after(300, self.perform_search)  # تأخير 300ms للبحث الفوري

    def on_filter_change(self, event=None):
        """معالجة تغيير الفلاتر"""
        if not self.is_loading:
            self.perform_search()

    def perform_search(self):
        """تنفيذ البحث والفلترة"""
        if self.is_loading:
            return

        try:
            search_term = self.search_var.get().strip().lower()
            status_filter = self.status_filter_var.get()
            priority_filter = self.priority_filter_var.get()
            date_filter = self.date_filter_var.get()

            self.update_status(f"{self.theme.icons['search']} جاري البحث...", "loading")

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # بناء الاستعلام
            query = """
                SELECT id, customer_name, customer_phone, device_type, device_brand,
                       problem_description, status, priority, created_date, estimated_cost
                FROM repairs WHERE 1=1
            """
            params = []

            # إضافة شرط البحث
            if search_term:
                query += """ AND (
                    LOWER(customer_name) LIKE ? OR
                    LOWER(customer_phone) LIKE ? OR
                    LOWER(device_type) LIKE ? OR
                    LOWER(device_brand) LIKE ? OR
                    LOWER(problem_description) LIKE ?
                )"""
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern] * 5)

            # إضافة فلتر الحالة
            if status_filter and status_filter != "الكل":
                status_map_reverse = {
                    'قيد الانتظار': 'pending',
                    'قيد الإصلاح': 'in_progress',
                    'انتظار قطع غيار': 'waiting_parts',
                    'قيد الاختبار': 'testing',
                    'تم الإصلاح': 'completed',
                    'تم التسليم': 'delivered',
                    'ملغي': 'cancelled'
                }
                status_en = status_map_reverse.get(status_filter)
                if status_en:
                    query += " AND status = ?"
                    params.append(status_en)

            # إضافة فلتر الأولوية
            if priority_filter and priority_filter != "الكل":
                priority_map_reverse = {
                    'منخفضة': 'low',
                    'عادية': 'normal',
                    'عالية': 'high',
                    'عاجلة': 'urgent'
                }
                priority_en = priority_map_reverse.get(priority_filter)
                if priority_en:
                    query += " AND priority = ?"
                    params.append(priority_en)

            # إضافة فلتر التاريخ
            if date_filter and date_filter != "الكل":
                if date_filter == "اليوم":
                    query += " AND DATE(created_date) = DATE('now')"
                elif date_filter == "هذا الأسبوع":
                    query += " AND created_date >= DATE('now', '-7 days')"
                elif date_filter == "هذا الشهر":
                    query += " AND created_date >= DATE('now', '-30 days')"
                elif date_filter == "آخر 3 أشهر":
                    query += " AND created_date >= DATE('now', '-90 days')"

            query += " ORDER BY id DESC"

            # تنفيذ الاستعلام
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(query, params)
            repairs = cursor.fetchall()
            conn.close()

            # إضافة النتائج للجدول
            for repair in repairs:
                status_map = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }

                priority_map = {
                    'low': 'منخفضة',
                    'normal': 'عادية',
                    'high': 'عالية',
                    'urgent': 'عاجلة'
                }

                status_display = status_map.get(repair['status'], repair['status'])
                priority_display = priority_map.get(repair['priority'], repair['priority'] or 'عادية')
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'

                tag = self.get_row_tag(repair['status'], repair['priority'])

                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    repair['customer_phone'],
                    f"{repair['device_brand'] or ''} {repair['device_type']}".strip(),
                    repair['problem_description'][:35] + "..." if repair['problem_description'] and len(repair['problem_description']) > 35 else (repair['problem_description'] or ''),
                    status_display,
                    priority_display,
                    created_date
                ), tags=(tag,))

            # تحديث العداد
            self.count_label.configure(text=f"{self.theme.icons['search']} نتائج البحث: {len(repairs)}")

            if len(repairs) > 0:
                self.update_status(f"{self.theme.icons['success']} تم العثور على {len(repairs)} نتيجة", "success")
            else:
                self.update_status(f"{self.theme.icons['warning']} لم يتم العثور على نتائج", "warning")

        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في البحث: {str(e)}", "error")

    def clear_search(self):
        """مسح البحث والفلاتر"""
        self.search_var.set("")
        self.status_filter_var.set("الكل")
        self.priority_filter_var.set("الكل")
        self.date_filter_var.set("الكل")
        self.load_repairs()

    def on_search(self, event=None):
        """البحث في الصيانة"""
        search_term = self.search_var.get().strip().lower()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not search_term:
            self.load_repairs()
            return
        
        try:
            # البحث في قاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = """
                SELECT id, customer_name, customer_phone, device_type, 
                       problem_description, status, created_date
                FROM repairs 
                WHERE LOWER(customer_name) LIKE ? 
                   OR LOWER(customer_phone) LIKE ?
                   OR LOWER(device_type) LIKE ?
                   OR LOWER(problem_description) LIKE ?
                ORDER BY id DESC
            """
            
            search_pattern = f"%{search_term}%"
            cursor.execute(query, (search_pattern, search_pattern, search_pattern, search_pattern))
            repairs = cursor.fetchall()
            conn.close()
            
            # إضافة النتائج للجدول
            for repair in repairs:
                status_map = {
                    'pending': 'قيد الانتظار',
                    'in_progress': 'قيد الإصلاح',
                    'waiting_parts': 'انتظار قطع غيار',
                    'testing': 'قيد الاختبار',
                    'completed': 'تم الإصلاح',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }
                
                status_display = status_map.get(repair['status'], repair['status'])
                created_date = repair['created_date'][:10] if repair['created_date'] else 'غير محدد'
                
                self.tree.insert('', 'end', values=(
                    repair['id'],
                    repair['customer_name'],
                    repair['customer_phone'],
                    repair['device_type'],
                    repair['problem_description'][:30] + "..." if len(repair['problem_description']) > 30 else repair['problem_description'],
                    status_display,
                    created_date
                ))
            
            self.count_label.configure(text=f"نتائج البحث: {len(repairs)}")
            
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            self.status_label.configure(text="خطأ في البحث")
    
    def on_select(self, event):
        """معالجة اختيار صيانة من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_repair = {
                    'id': values[0],
                    'customer_name': values[1],
                    'customer_phone': values[2],
                    'device_type': values[3],
                    'problem_description': values[4],
                    'status': values[5]
                }
                self.status_label.configure(text=f"تم اختيار أمر الصيانة رقم: {values[0]}")
    
    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            # تحديد العنصر المحدد
            item = self.tree.identify_row(event.y)
            if item:
                self.tree.selection_set(item)
                self.on_select(None)

                # إنشاء قائمة السياق
                context_menu = tk.Menu(self.parent_frame, tearoff=0)
                context_menu.add_command(
                    label=f"{self.theme.icons['edit']} تعديل",
                    command=self.edit_repair
                )
                context_menu.add_command(
                    label=f"{self.theme.icons['info']} عرض التفاصيل",
                    command=self.view_details
                )
                context_menu.add_separator()
                context_menu.add_command(
                    label=f"{self.theme.icons['delete']} حذف",
                    command=self.delete_repair
                )

                # عرض القائمة
                context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في قائمة السياق: {e}")

    def add_repair(self):
        """إضافة أمر صيانة جديد"""
        self.update_status(f"{self.theme.icons['info']} ميزة إضافة الصيانة قيد التطوير", "info")

    def edit_repair(self, event=None):
        """تعديل أمر صيانة"""
        if not self.selected_repair:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار أمر صيانة للتعديل", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة تعديل الصيانة قيد التطوير", "info")

    def view_details(self):
        """عرض تفاصيل أمر الصيانة"""
        if not self.selected_repair:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار أمر صيانة لعرض التفاصيل", "warning")
            return

        try:
            # جلب التفاصيل الكاملة من قاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repairs WHERE id = ?", (self.selected_repair['id'],))
            repair = cursor.fetchone()
            conn.close()

            if repair:
                # إنشاء نافذة التفاصيل
                details_window = tk.Toplevel(self.parent_frame)
                details_window.title(f"تفاصيل أمر الصيانة رقم {repair['id']}")
                details_window.geometry("600x700")
                details_window.configure(bg=self.theme.colors['bg_primary'])
                details_window.resizable(False, False)

                # جعل النافذة في المقدمة
                details_window.transient(self.parent_frame.winfo_toplevel())
                details_window.grab_set()

                # محتوى النافذة
                self.create_details_content(details_window, repair)

        except Exception as e:
            print(f"خطأ في عرض التفاصيل: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في عرض التفاصيل", "error")

    def create_details_content(self, window, repair):
        """إنشاء محتوى نافذة التفاصيل"""
        # العنوان
        header_frame = self.theme.create_gradient_frame(
            window,
            self.theme.colors['gradient_info'],
            height=80
        )
        header_frame.pack(fill=tk.X)

        title_label = tk.Label(
            header_frame,
            text=f"{self.theme.icons['repairs']} تفاصيل أمر الصيانة رقم {repair['id']}",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['title_medium']
        )
        title_label.place(relx=0.5, rely=0.5, anchor='center')

        # المحتوى الرئيسي
        main_frame = tk.Frame(window, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # معلومات العميل
        customer_card, customer_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['customer']} معلومات العميل"
        )
        customer_card.pack(fill=tk.X, pady=(0, 15))

        customer_info = [
            ("الاسم", repair['customer_name']),
            ("الهاتف", repair['customer_phone']),
            ("البريد الإلكتروني", repair['customer_email'] or 'غير محدد'),
            ("العنوان", repair['customer_address'] or 'غير محدد')
        ]

        for label, value in customer_info:
            info_frame = tk.Frame(customer_content, bg=self.theme.colors['bg_card'])
            info_frame.pack(fill=tk.X, pady=5)

            tk.Label(
                info_frame,
                text=f"{label}:",
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_secondary'],
                font=self.theme.fonts['body_medium']
            ).pack(side=tk.LEFT)

            tk.Label(
                info_frame,
                text=str(value),
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_primary'],
                font=self.theme.fonts['body_medium']
            ).pack(side=tk.RIGHT)

        # معلومات الجهاز
        device_card, device_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['phone']} معلومات الجهاز"
        )
        device_card.pack(fill=tk.X, pady=(0, 15))

        device_info = [
            ("النوع", repair['device_type']),
            ("الماركة", repair['device_brand'] or 'غير محدد'),
            ("الموديل", repair['device_model'] or 'غير محدد'),
            ("اللون", repair['device_color'] or 'غير محدد'),
            ("IMEI", repair['device_imei'] or 'غير محدد')
        ]

        for label, value in device_info:
            info_frame = tk.Frame(device_content, bg=self.theme.colors['bg_card'])
            info_frame.pack(fill=tk.X, pady=5)

            tk.Label(
                info_frame,
                text=f"{label}:",
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_secondary'],
                font=self.theme.fonts['body_medium']
            ).pack(side=tk.LEFT)

            tk.Label(
                info_frame,
                text=str(value),
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_primary'],
                font=self.theme.fonts['body_medium']
            ).pack(side=tk.RIGHT)

        # أزرار الإجراءات
        buttons_frame = tk.Frame(main_frame, bg=self.theme.colors['bg_primary'])
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        close_btn = self.theme.create_modern_button(
            buttons_frame,
            "إغلاق",
            command=window.destroy,
            style='secondary',
            size='medium',
            icon='close'
        )
        close_btn.pack(side=tk.RIGHT)

    def export_data(self):
        """تصدير البيانات"""
        self.update_status(f"{self.theme.icons['info']} ميزة تصدير البيانات قيد التطوير", "info")

    def print_report(self):
        """طباعة التقرير"""
        self.update_status(f"{self.theme.icons['info']} ميزة الطباعة قيد التطوير", "info")
    
    def delete_repair(self):
        """حذف أمر صيانة مع تأكيد متطور"""
        if not self.selected_repair:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار أمر صيانة للحذف", "warning")
            return

        try:
            # إنشاء نافذة تأكيد متطورة
            confirm_window = tk.Toplevel(self.parent_frame)
            confirm_window.title("تأكيد الحذف")
            confirm_window.geometry("450x300")
            confirm_window.configure(bg=self.theme.colors['bg_primary'])
            confirm_window.resizable(False, False)

            # جعل النافذة في المقدمة
            confirm_window.transient(self.parent_frame.winfo_toplevel())
            confirm_window.grab_set()

            # توسيط النافذة
            confirm_window.update_idletasks()
            x = (confirm_window.winfo_screenwidth() // 2) - (450 // 2)
            y = (confirm_window.winfo_screenheight() // 2) - (300 // 2)
            confirm_window.geometry(f"450x300+{x}+{y}")

            # الهيدر
            header_frame = self.theme.create_gradient_frame(
                confirm_window,
                self.theme.colors['gradient_danger'],
                height=80
            )
            header_frame.pack(fill=tk.X)

            header_label = tk.Label(
                header_frame,
                text=f"{self.theme.icons['warning']} تأكيد الحذف",
                bg='transparent',
                fg=self.theme.colors['text_white'],
                font=self.theme.fonts['title_medium']
            )
            header_label.place(relx=0.5, rely=0.5, anchor='center')

            # المحتوى
            content_frame = tk.Frame(confirm_window, bg=self.theme.colors['bg_primary'])
            content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

            # رسالة التأكيد
            message_label = tk.Label(
                content_frame,
                text=f"هل أنت متأكد من حذف أمر الصيانة رقم {self.selected_repair['id']}؟",
                bg=self.theme.colors['bg_primary'],
                fg=self.theme.colors['text_primary'],
                font=self.theme.fonts['heading_small'],
                wraplength=350,
                justify='center'
            )
            message_label.pack(pady=(20, 10))

            # معلومات إضافية
            info_label = tk.Label(
                content_frame,
                text=f"العميل: {self.selected_repair['customer_name']}\nالجهاز: {self.selected_repair['device_type']}",
                bg=self.theme.colors['bg_primary'],
                fg=self.theme.colors['text_secondary'],
                font=self.theme.fonts['body_medium'],
                justify='center'
            )
            info_label.pack(pady=(0, 20))

            warning_label = tk.Label(
                content_frame,
                text="⚠️ هذا الإجراء لا يمكن التراجع عنه",
                bg=self.theme.colors['bg_primary'],
                fg=self.theme.colors['danger'],
                font=self.theme.fonts['body_small']
            )
            warning_label.pack(pady=(0, 30))

            # الأزرار
            buttons_frame = tk.Frame(content_frame, bg=self.theme.colors['bg_primary'])
            buttons_frame.pack(fill=tk.X)

            def confirm_delete():
                try:
                    # حذف من قاعدة البيانات
                    conn = sqlite3.connect('database/phone_doctor.db')
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM repairs WHERE id = ?", (self.selected_repair['id'],))
                    conn.commit()
                    conn.close()

                    repair_id = self.selected_repair['id']
                    self.selected_repair = None
                    confirm_window.destroy()
                    self.load_repairs()
                    self.update_status(f"{self.theme.icons['success']} تم حذف أمر الصيانة رقم {repair_id} بنجاح", "success")

                except Exception as e:
                    print(f"❌ خطأ في حذف الصيانة: {e}")
                    self.update_status(f"{self.theme.icons['error']} فشل في حذف أمر الصيانة", "error")
                    confirm_window.destroy()

            # زر الحذف
            delete_btn = self.theme.create_modern_button(
                buttons_frame,
                "حذف",
                command=confirm_delete,
                style='danger',
                size='medium',
                icon='delete'
            )
            delete_btn.pack(side=tk.RIGHT, padx=(10, 0))

            # زر الإلغاء
            cancel_btn = self.theme.create_modern_button(
                buttons_frame,
                "إلغاء",
                command=confirm_window.destroy,
                style='secondary',
                size='medium',
                icon='cancel'
            )
            cancel_btn.pack(side=tk.RIGHT)

        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة التأكيد: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في عملية الحذف", "error")
