#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تجميع النسخة العصرية إلى EXE
Phone Doctor Modern - Build Script

المطور: محمد الشوامرة - 0566000140
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    print(f"✅ Python {sys.version}")
    
    # التحقق من PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller غير مثبت!")
        print("تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("✅ تم تثبيت PyInstaller")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت PyInstaller")
            return False
    
    # التحقق من الملفات المطلوبة
    required_files = [
        'main_modern.py',
        'main_tkinter.py',
        'ui/modern_styles.py',
        'ui/modern_sidebar.py',
        'ui/modern_header.py',
        'database/db_manager.py',
        'utils/config.py'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ ملف مفقود: {file}")
            return False
        print(f"✅ {file}")
    
    return True

def prepare_build():
    """إعداد البناء"""
    print("\n📦 إعداد البناء...")
    
    # إنشاء قاعدة بيانات فارغة إذا لم تكن موجودة
    if not os.path.exists('phone_doctor.db'):
        print("📄 إنشاء قاعدة بيانات فارغة...")
        try:
            from database.db_manager import DatabaseManager
            db = DatabaseManager('phone_doctor.db')
            if db.initialize_database():
                print("✅ تم إنشاء قاعدة البيانات")
            else:
                print("⚠️ تحذير: فشل في إنشاء قاعدة البيانات")
        except Exception as e:
            print(f"⚠️ تحذير: {e}")
    
    # إنشاء ملف إعدادات إذا لم يكن موجوداً
    if not os.path.exists('config.json'):
        print("⚙️ إنشاء ملف إعدادات افتراضي...")
        try:
            from utils.config import Config
            config = Config('config.json')
            config.save_config()
            print("✅ تم إنشاء ملف الإعدادات")
        except Exception as e:
            print(f"⚠️ تحذير: {e}")
    
    # تنظيف الملفات المؤقتة
    temp_dirs = ['build', 'dist', '__pycache__']
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            print(f"🧹 حذف مجلد مؤقت: {temp_dir}")
            shutil.rmtree(temp_dir)
    
    print("✅ اكتمل الإعداد")

def build_exe():
    """تجميع الملف التنفيذي"""
    print("\n🔨 بدء تجميع الملف التنفيذي...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'phone_doctor_modern.spec'
        ]
        
        print("🔄 تشغيل PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم تجميع الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في التجميع:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التجميع: {e}")
        return False

def test_exe():
    """اختبار الملف التنفيذي"""
    print("\n🧪 اختبار الملف التنفيذي...")
    
    exe_path = "dist/PhoneDoctor_Modern.exe"
    if not os.path.exists(exe_path):
        print(f"❌ الملف التنفيذي غير موجود: {exe_path}")
        return False
    
    print(f"✅ الملف التنفيذي موجود: {exe_path}")
    
    # التحقق من حجم الملف
    file_size = os.path.getsize(exe_path) / (1024 * 1024)  # بالميجابايت
    print(f"📏 حجم الملف: {file_size:.1f} MB")
    
    # اختبار تشغيل سريع (5 ثوان)
    print("🚀 اختبار تشغيل سريع...")
    try:
        process = subprocess.Popen([exe_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # انتظار 3 ثوان ثم إنهاء العملية
        import time
        time.sleep(3)
        
        if process.poll() is None:  # العملية ما زالت تعمل
            process.terminate()
            print("✅ الملف التنفيذي يعمل بشكل صحيح")
            return True
        else:
            print("❌ الملف التنفيذي توقف بشكل غير متوقع")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التشغيل: {e}")
        return False

def create_distribution():
    """إنشاء حزمة التوزيع"""
    print("\n📦 إنشاء حزمة التوزيع...")
    
    dist_folder = "PhoneDoctor_Modern_EXE"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    
    # نسخ الملف التنفيذي
    exe_source = "dist/PhoneDoctor_Modern.exe"
    exe_dest = os.path.join(dist_folder, "PhoneDoctor_Modern.exe")
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print("✅ تم نسخ الملف التنفيذي")
    else:
        print("❌ الملف التنفيذي غير موجود")
        return False
    
    # إنشاء قاعدة بيانات فارغة
    try:
        from database.db_manager import DatabaseManager
        db_path = os.path.join(dist_folder, "phone_doctor.db")
        db = DatabaseManager(db_path)
        if db.initialize_database():
            print("✅ تم إنشاء قاعدة البيانات للتوزيع")
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
    
    # إنشاء ملف إعدادات
    try:
        from utils.config import Config
        config_path = os.path.join(dist_folder, "config.json")
        config = Config(config_path)
        config.save_config()
        print("✅ تم إنشاء ملف الإعدادات للتوزيع")
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
    
    # إنشاء ملف README
    readme_content = """
# 🎨 Phone Doctor - النسخة العصرية (EXE)

## المطور: محمد الشوامرة - 📞 0566000140

### تعليمات التشغيل:

1. **تشغيل البرنامج:**
   - انقر نقراً مزدوجاً على PhoneDoctor_Modern.exe
   - لا يحتاج تثبيت Python أو مكتبات إضافية

2. **الملفات المهمة:**
   - PhoneDoctor_Modern.exe - الملف التنفيذي الرئيسي
   - phone_doctor.db - قاعدة البيانات
   - config.json - ملف الإعدادات

3. **الميزات:**
   - تصميم عصري متقدم
   - شريط جانبي تفاعلي
   - بطاقات إحصائية جذابة
   - واجهة عربية كاملة

4. **الدعم الفني:**
   - محمد الشوامرة: 0566000140

### ملاحظات مهمة:
- احتفظ بنسخة احتياطية من قاعدة البيانات
- تأكد من وجود صلاحيات الكتابة في المجلد
- في حالة مشاكل، تواصل مع المطور

---
Phone Doctor Modern v1.0.0 - Professional EXE Version
© 2024 Mohammad Shawamreh. All rights reserved.
"""
    
    readme_path = os.path.join(dist_folder, "README.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README")
    
    # إنشاء ملف تشغيل سريع
    batch_content = """@echo off
chcp 65001 > nul
title Phone Doctor Modern - النسخة العصرية

echo.
echo ========================================
echo   📱 Phone Doctor Modern - النسخة العصرية
echo   نظام إدارة محلات صيانة الهواتف
echo   المطور: محمد الشوامرة - 0566000140
echo ========================================
echo.

echo 🚀 تشغيل النسخة العصرية...
echo.

PhoneDoctor_Modern.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج!
    echo.
    echo تحقق من:
    echo 1. وجود جميع الملفات المطلوبة
    echo 2. صلاحيات الكتابة في المجلد
    echo 3. عدم حجب البرنامج من مكافح الفيروسات
    echo.
    echo للدعم الفني: محمد الشوامرة - 0566000140
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح.
pause
"""
    
    batch_path = os.path.join(dist_folder, "تشغيل_البرنامج.bat")
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    print(f"🎉 تم إنشاء حزمة التوزيع في: {dist_folder}")
    return True

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    temp_items = ['build', '__pycache__', '*.pyc']
    for item in temp_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"✅ تم حذف: {item}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تجميع Phone Doctor Modern إلى EXE")
    print("=" * 60)
    
    try:
        # التحقق من المتطلبات
        if not check_requirements():
            return False
        
        # إعداد البناء
        prepare_build()
        
        # تجميع الملف التنفيذي
        if not build_exe():
            return False
        
        # اختبار الملف التنفيذي
        if not test_exe():
            print("⚠️ تحذير: فشل في اختبار الملف التنفيذي")
        
        # إنشاء حزمة التوزيع
        if not create_distribution():
            return False
        
        # تنظيف الملفات المؤقتة
        cleanup()
        
        print("\n" + "=" * 60)
        print("🎉 تم تجميع Phone Doctor Modern بنجاح!")
        print("📁 ستجد الملف التنفيذي في مجلد: PhoneDoctor_Modern_EXE")
        print("🚀 يمكنك الآن تشغيل PhoneDoctor_Modern.exe")
        print("👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في عملية التجميع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
