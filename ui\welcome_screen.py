#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة الترحيب العصرية
Modern Welcome Screen

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from datetime import datetime
import calendar

class WelcomeScreen:
    """شاشة الترحيب العصرية"""
    
    def __init__(self, parent_frame, db_manager, auth_manager):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        
        # ألوان النظام العصرية
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'info': '#06b6d4',
            'bg_light': '#f8fafc',
            'bg_gradient_start': '#667eea',
            'bg_gradient_end': '#764ba2',
            'text_dark': '#1f2937',
            'text_light': '#6b7280',
            'accent': '#ec4899',
            'glass': '#ffffff20'
        }
        
        self.setup_ui()
        self.load_dashboard_data()
        self.start_time_update()
    
    def setup_ui(self):
        """إعداد واجهة الترحيب العصرية"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # الخلفية الرئيسية مع تدرج لوني
        main_canvas = tk.Canvas(
            self.parent_frame,
            bg=self.colors['bg_gradient_start'],
            highlightthickness=0
        )
        main_canvas.pack(fill=tk.BOTH, expand=True)
        
        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_canvas, bg=self.colors['bg_gradient_start'])
        main_canvas.create_window(0, 0, anchor='nw', window=content_frame)
        
        # ربط التمرير
        def configure_scroll(event):
            main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        
        content_frame.bind('<Configure>', configure_scroll)
        
        # العنوان الرئيسي مع تأثير الزجاج
        self.create_header(content_frame)
        
        # بطاقات الترحيب
        self.create_welcome_cards(content_frame)
        
        # الإحصائيات السريعة
        self.create_quick_stats(content_frame)
        
        # الأنشطة الحديثة
        self.create_recent_activities(content_frame)
        
        # الاختصارات السريعة
        self.create_quick_actions(content_frame)
        
        # معلومات النظام
        self.create_system_info(content_frame)
        
        # تحديث حجم المحتوى
        content_frame.update_idletasks()
        main_canvas.configure(scrollregion=main_canvas.bbox("all"))
    
    def create_header(self, parent):
        """إنشاء العنوان الرئيسي"""
        header_frame = tk.Frame(parent, bg=self.colors['bg_gradient_start'], height=200)
        header_frame.pack(fill=tk.X, padx=30, pady=(30, 20))
        header_frame.pack_propagate(False)
        
        # إطار زجاجي للعنوان
        glass_frame = tk.Frame(
            header_frame,
            bg=self.colors['glass'],
            relief='solid',
            bd=1
        )
        glass_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # محتوى العنوان
        header_content = tk.Frame(glass_frame, bg=self.colors['glass'])
        header_content.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # الترحيب الشخصي
        current_user = self.auth_manager.current_user
        user_name = current_user.get('full_name', 'المستخدم') if current_user else 'المستخدم'
        
        welcome_label = tk.Label(
            header_content,
            text=f"مرحباً بك، {user_name} 👋",
            bg=self.colors['glass'],
            fg='white',
            font=('Arial', 24, 'bold')
        )
        welcome_label.pack()
        
        # التاريخ والوقت
        self.datetime_label = tk.Label(
            header_content,
            text="",
            bg=self.colors['glass'],
            fg='#e2e8f0',
            font=('Arial', 14)
        )
        self.datetime_label.pack(pady=(10, 0))
        
        # رسالة ترحيبية
        welcome_message = self.get_welcome_message()
        message_label = tk.Label(
            header_content,
            text=welcome_message,
            bg=self.colors['glass'],
            fg='#e2e8f0',
            font=('Arial', 12),
            wraplength=600,
            justify=tk.CENTER
        )
        message_label.pack(pady=(10, 0))
    
    def create_welcome_cards(self, parent):
        """إنشاء بطاقات الترحيب"""
        cards_frame = tk.Frame(parent, bg=self.colors['bg_gradient_start'])
        cards_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # بطاقات الميزات الرئيسية
        features = [
            {
                'icon': '💰',
                'title': 'إدارة المبيعات',
                'description': 'تسجيل وإدارة جميع المبيعات بسهولة',
                'color': self.colors['success']
            },
            {
                'icon': '🔧',
                'title': 'إدارة الصيانة',
                'description': 'متابعة أوامر الصيانة والإصلاح',
                'color': self.colors['warning']
            },
            {
                'icon': '📦',
                'title': 'إدارة المخزون',
                'description': 'مراقبة المخزون والكميات المتوفرة',
                'color': self.colors['info']
            },
            {
                'icon': '👥',
                'title': 'إدارة العملاء',
                'description': 'قاعدة بيانات شاملة للعملاء',
                'color': self.colors['secondary']
            }
        ]
        
        for i, feature in enumerate(features):
            row = i // 2
            col = i % 2
            
            card_frame = tk.Frame(
                cards_frame,
                bg=feature['color'],
                relief='solid',
                bd=1
            )
            card_frame.grid(row=row, column=col, padx=15, pady=15, sticky="ew")
            
            # محتوى البطاقة
            card_content = tk.Frame(card_frame, bg=feature['color'])
            card_content.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
            
            # الأيقونة
            icon_label = tk.Label(
                card_content,
                text=feature['icon'],
                bg=feature['color'],
                fg='white',
                font=('Arial', 32)
            )
            icon_label.pack()
            
            # العنوان
            title_label = tk.Label(
                card_content,
                text=feature['title'],
                bg=feature['color'],
                fg='white',
                font=('Arial', 14, 'bold')
            )
            title_label.pack(pady=(10, 5))
            
            # الوصف
            desc_label = tk.Label(
                card_content,
                text=feature['description'],
                bg=feature['color'],
                fg='#e2e8f0',
                font=('Arial', 10),
                wraplength=200,
                justify=tk.CENTER
            )
            desc_label.pack()
        
        # تكوين الشبكة
        for i in range(2):
            cards_frame.grid_columnconfigure(i, weight=1)
    
    def create_quick_stats(self, parent):
        """إنشاء الإحصائيات السريعة"""
        stats_frame = tk.LabelFrame(
            parent,
            text="📊 إحصائيات سريعة",
            bg=self.colors['bg_light'],
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        stats_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # جلب الإحصائيات
        stats = self.get_quick_stats()
        
        # عرض الإحصائيات
        stats_content = tk.Frame(stats_frame, bg=self.colors['bg_light'])
        stats_content.pack(fill=tk.X, padx=20, pady=20)
        
        stats_data = [
            ('💰', 'مبيعات اليوم', f"{stats['today_sales']:.2f} ₪"),
            ('🛒', 'عدد المبيعات', str(stats['sales_count'])),
            ('🔧', 'أوامر الصيانة النشطة', str(stats['active_repairs'])),
            ('⚠️', 'منتجات بمخزون منخفض', str(stats['low_stock_items'])),
            ('👥', 'عملاء جدد هذا الشهر', str(stats['new_customers'])),
            ('📈', 'نمو المبيعات', f"{stats['sales_growth']:+.1f}%")
        ]
        
        for i, (icon, title, value) in enumerate(stats_data):
            row = i // 3
            col = i % 3
            
            stat_frame = tk.Frame(stats_content, bg='white', relief='solid', bd=1)
            stat_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            
            stat_content = tk.Frame(stat_frame, bg='white')
            stat_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            icon_label = tk.Label(stat_content, text=icon, bg='white', fg=self.colors['primary'], font=('Arial', 24))
            icon_label.pack()
            
            value_label = tk.Label(stat_content, text=value, bg='white', fg=self.colors['text_dark'], font=('Arial', 16, 'bold'))
            value_label.pack()
            
            title_label = tk.Label(stat_content, text=title, bg='white', fg=self.colors['text_light'], font=('Arial', 10))
            title_label.pack()
        
        for i in range(3):
            stats_content.grid_columnconfigure(i, weight=1)
    
    def create_recent_activities(self, parent):
        """إنشاء الأنشطة الحديثة"""
        activities_frame = tk.LabelFrame(
            parent,
            text="🕒 الأنشطة الحديثة",
            bg=self.colors['bg_light'],
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        activities_frame.pack(fill=tk.X, padx=30, pady=20)
        
        activities_content = tk.Frame(activities_frame, bg=self.colors['bg_light'])
        activities_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # جلب الأنشطة الحديثة
        activities = self.get_recent_activities()
        
        for i, activity in enumerate(activities[:5]):  # عرض آخر 5 أنشطة
            activity_frame = tk.Frame(activities_content, bg='white', relief='solid', bd=1)
            activity_frame.pack(fill=tk.X, pady=5)
            
            activity_content = tk.Frame(activity_frame, bg='white')
            activity_content.pack(fill=tk.X, padx=15, pady=10)
            
            # الأيقونة والنص
            icon_label = tk.Label(
                activity_content,
                text=activity['icon'],
                bg='white',
                fg=activity['color'],
                font=('Arial', 16)
            )
            icon_label.pack(side=tk.LEFT, padx=(0, 10))
            
            text_frame = tk.Frame(activity_content, bg='white')
            text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            desc_label = tk.Label(
                text_frame,
                text=activity['description'],
                bg='white',
                fg=self.colors['text_dark'],
                font=('Arial', 11),
                anchor='w'
            )
            desc_label.pack(anchor='w')
            
            time_label = tk.Label(
                text_frame,
                text=activity['time'],
                bg='white',
                fg=self.colors['text_light'],
                font=('Arial', 9),
                anchor='w'
            )
            time_label.pack(anchor='w')
    
    def create_quick_actions(self, parent):
        """إنشاء الاختصارات السريعة"""
        actions_frame = tk.LabelFrame(
            parent,
            text="⚡ اختصارات سريعة",
            bg=self.colors['bg_light'],
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        actions_frame.pack(fill=tk.X, padx=30, pady=20)
        
        actions_content = tk.Frame(actions_frame, bg=self.colors['bg_light'])
        actions_content.pack(fill=tk.X, padx=20, pady=20)
        
        # أزرار الاختصارات
        actions = [
            ('🛒', 'بيع جديد', self.colors['success'], lambda: print("بيع جديد")),
            ('🔧', 'صيانة جديدة', self.colors['warning'], lambda: print("صيانة جديدة")),
            ('👥', 'عميل جديد', self.colors['info'], lambda: print("عميل جديد")),
            ('📦', 'منتج جديد', self.colors['secondary'], lambda: print("منتج جديد")),
            ('📊', 'التقارير', self.colors['primary'], lambda: print("التقارير")),
            ('⚙️', 'الإعدادات', self.colors['danger'], lambda: print("الإعدادات"))
        ]
        
        for i, (icon, text, color, command) in enumerate(actions):
            row = i // 3
            col = i % 3
            
            action_btn = tk.Button(
                actions_content,
                text=f"{icon}\n{text}",
                command=command,
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=15,
                cursor='hand2'
            )
            action_btn.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            
            # تأثيرات hover
            def on_enter(e, btn=action_btn, original_color=color):
                btn.configure(bg=self.darken_color(original_color))
            
            def on_leave(e, btn=action_btn, original_color=color):
                btn.configure(bg=original_color)
            
            action_btn.bind("<Enter>", on_enter)
            action_btn.bind("<Leave>", on_leave)
        
        for i in range(3):
            actions_content.grid_columnconfigure(i, weight=1)
    
    def create_system_info(self, parent):
        """إنشاء معلومات النظام"""
        info_frame = tk.Frame(parent, bg=self.colors['bg_gradient_start'])
        info_frame.pack(fill=tk.X, padx=30, pady=(20, 30))
        
        # معلومات النظام
        system_frame = tk.Frame(info_frame, bg=self.colors['glass'], relief='solid', bd=1)
        system_frame.pack(fill=tk.X, padx=10, pady=10)
        
        system_content = tk.Frame(system_frame, bg=self.colors['glass'])
        system_content.pack(fill=tk.X, padx=20, pady=15)
        
        # معلومات المطور
        developer_info = tk.Label(
            system_content,
            text="💻 Phone Doctor v2.0 | المطور: محمد الشوامرة | 📞 0566000140",
            bg=self.colors['glass'],
            fg='white',
            font=('Arial', 11, 'bold')
        )
        developer_info.pack()
        
        # حقوق الطبع
        copyright_info = tk.Label(
            system_content,
            text=f"© {datetime.now().year} جميع الحقوق محفوظة | نظام إدارة محلات صيانة الهواتف",
            bg=self.colors['glass'],
            fg='#e2e8f0',
            font=('Arial', 9)
        )
        copyright_info.pack(pady=(5, 0))

    def get_welcome_message(self):
        """الحصول على رسالة ترحيبية حسب الوقت"""
        current_hour = datetime.now().hour

        if 5 <= current_hour < 12:
            return "🌅 صباح الخير! نتمنى لك يوماً مليئاً بالإنجازات والمبيعات الناجحة"
        elif 12 <= current_hour < 17:
            return "☀️ مساء الخير! استمر في العمل الرائع وتحقيق أهدافك"
        elif 17 <= current_hour < 21:
            return "🌆 مساء الخير! وقت مثالي لمراجعة إنجازات اليوم"
        else:
            return "🌙 مساء الخير! شكراً لك على عملك الدؤوب"

    def start_time_update(self):
        """بدء تحديث الوقت"""
        self.update_datetime()
        self.parent_frame.after(1000, self.start_time_update)

    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()

        # تنسيق التاريخ والوقت
        arabic_months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]

        arabic_days = [
            'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
        ]

        day_name = arabic_days[now.weekday()]
        month_name = arabic_months[now.month - 1]

        date_str = f"{day_name}، {now.day} {month_name} {now.year}"
        time_str = now.strftime("%I:%M:%S %p")

        datetime_text = f"📅 {date_str} | 🕐 {time_str}"

        if hasattr(self, 'datetime_label'):
            self.datetime_label.configure(text=datetime_text)

    def get_quick_stats(self):
        """جلب الإحصائيات السريعة"""
        try:
            # مبيعات اليوم
            today = datetime.now().strftime('%Y-%m-%d')
            today_sales_result = self.db_manager.fetch_one(
                "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(sale_date) = ?",
                (today,)
            )
            today_sales = today_sales_result['total'] if today_sales_result else 0

            # عدد المبيعات
            sales_count_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM sales")
            sales_count = sales_count_result['count'] if sales_count_result else 0

            # أوامر الصيانة النشطة
            active_repairs_result = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM repairs WHERE status IN ('قيد الانتظار', 'قيد الإصلاح')"
            )
            active_repairs = active_repairs_result['count'] if active_repairs_result else 0

            # منتجات بمخزون منخفض
            low_stock_result = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM inventory WHERE quantity <= 5"
            )
            low_stock_items = low_stock_result['count'] if low_stock_result else 0

            # عملاء جدد هذا الشهر
            this_month = datetime.now().strftime('%Y-%m')
            new_customers_result = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM customers WHERE strftime('%Y-%m', created_at) = ?",
                (this_month,)
            )
            new_customers = new_customers_result['count'] if new_customers_result else 0

            return {
                'today_sales': today_sales,
                'sales_count': sales_count,
                'active_repairs': active_repairs,
                'low_stock_items': low_stock_items,
                'new_customers': new_customers,
                'sales_growth': 12.5  # قيمة وهمية
            }
        except Exception as e:
            print(f"خطأ في جلب الإحصائيات السريعة: {e}")
            return {
                'today_sales': 0, 'sales_count': 0, 'active_repairs': 0,
                'low_stock_items': 0, 'new_customers': 0, 'sales_growth': 0
            }

    def get_recent_activities(self):
        """جلب الأنشطة الحديثة"""
        activities = []

        try:
            # أحدث المبيعات
            recent_sales = self.db_manager.fetch_all(
                """SELECT s.sale_date, s.total_amount, c.name as customer_name
                   FROM sales s
                   LEFT JOIN customers c ON s.customer_id = c.id
                   ORDER BY s.sale_date DESC LIMIT 3"""
            )

            for sale in recent_sales:
                customer = sale['customer_name'] or 'عميل نقدي'
                time_ago = self.get_time_ago(sale['sale_date'])
                activities.append({
                    'icon': '💰',
                    'description': f"بيع جديد للعميل {customer} بقيمة {sale['total_amount']:.2f} ₪",
                    'time': time_ago,
                    'color': self.colors['success']
                })

            # أحدث أوامر الصيانة
            recent_repairs = self.db_manager.fetch_all(
                "SELECT customer_name, device_type, created_date FROM repairs ORDER BY created_date DESC LIMIT 2"
            )

            for repair in recent_repairs:
                time_ago = self.get_time_ago(repair['created_date'])
                activities.append({
                    'icon': '🔧',
                    'description': f"أمر صيانة جديد: {repair['device_type']} للعميل {repair['customer_name']}",
                    'time': time_ago,
                    'color': self.colors['warning']
                })

        except Exception as e:
            print(f"خطأ في جلب الأنشطة الحديثة: {e}")

        # إضافة أنشطة افتراضية إذا لم توجد بيانات
        if not activities:
            activities = [
                {
                    'icon': '🎉',
                    'description': 'مرحباً بك في نظام Phone Doctor',
                    'time': 'الآن',
                    'color': self.colors['primary']
                },
                {
                    'icon': '📱',
                    'description': 'النظام جاهز لإدارة محلك بكفاءة',
                    'time': 'منذ قليل',
                    'color': self.colors['info']
                }
            ]

        return activities

    def get_time_ago(self, timestamp_str):
        """حساب الوقت المنقضي"""
        try:
            if not timestamp_str:
                return 'غير محدد'

            # تحويل النص إلى تاريخ
            timestamp = datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            diff = now - timestamp

            if diff.days > 0:
                return f"منذ {diff.days} يوم"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"منذ {hours} ساعة"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"منذ {minutes} دقيقة"
            else:
                return "منذ قليل"
        except:
            return 'غير محدد'

    def darken_color(self, color):
        """تغميق اللون للتأثير"""
        color_map = {
            self.colors['success']: '#059669',
            self.colors['warning']: '#d97706',
            self.colors['danger']: '#dc2626',
            self.colors['info']: '#0891b2',
            self.colors['primary']: '#4f46e5',
            self.colors['secondary']: '#7c3aed'
        }
        return color_map.get(color, color)

    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            # يمكن إضافة منطق تحميل البيانات هنا
            pass
        except Exception as e:
            print(f"خطأ في تحميل بيانات لوحة التحكم: {e}")
