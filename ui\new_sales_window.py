#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة المبيعات مع الباركود والمخزون
Sales Window with Barcode and Inventory

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime
import sys
import os

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.modern_styles import ModernStyles

class NewSalesWindow:
    """نافذة المبيعات العصرية مع الباركود"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.styles = ModernStyles()
        
        self.window = tk.Toplevel(parent)
        self.window.title("إضافة بيع جديد - Phone Doctor")
        self.window.geometry("900x750")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # متغيرات البيع
        self.cart_items = []  # عربة التسوق
        self.total_amount = 0.0
        self.selected_product = None
        
        self.setup_ui()
        self.load_customers()
        
        # التركيز على حقل الباركود
        self.barcode_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة المبيعات"""
        # تكوين النافذة
        self.window.configure(bg=self.styles.colors['bg_primary'])
        
        # الإطار الرئيسي مع التمرير
        canvas = tk.Canvas(
            self.window,
            bg=self.styles.colors['bg_primary'],
            highlightthickness=0
        )
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.styles.colors['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المحتوى الرئيسي
        main_frame = tk.Frame(
            scrollable_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رأس النافذة
        header_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(
            header_frame,
            text="🛒 إضافة بيع جديد",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            font=('Segoe UI', 20, 'bold')
        )
        title_label.pack()
        
        # إطار البحث والباركود
        search_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        search_frame.pack(fill=tk.X, pady=(0, 15))
        
        search_content = tk.Frame(
            search_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        search_content.pack(fill=tk.X, padx=20, pady=15)
        
        # حقل الباركود
        barcode_frame = tk.Frame(
            search_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        barcode_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            barcode_frame,
            text="📱 الباركود أو اسم المنتج:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        barcode_input_frame = tk.Frame(
            barcode_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        barcode_input_frame.pack(fill=tk.X)
        
        self.barcode_entry = tk.Entry(
            barcode_input_frame,
            **self.styles.get_entry_style(),
            font=self.styles.fonts['body_medium']
        )
        self.barcode_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        search_btn = tk.Button(
            barcode_input_frame,
            text="🔍 بحث",
            command=self.search_product,
            **self.styles.get_button_style('primary'),
            font=self.styles.fonts['body_medium']
        )
        search_btn.pack(side=tk.RIGHT)
        
        # ربط Enter بالبحث
        self.barcode_entry.bind('<Return>', lambda e: self.search_product())
        
        # معلومات المنتج المحدد
        self.product_info_frame = tk.Frame(
            search_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        self.product_info_frame.pack(fill=tk.X, pady=(10, 0))
        
        # إطار العميل
        customer_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        customer_frame.pack(fill=tk.X, pady=(0, 15))
        
        customer_content = tk.Frame(
            customer_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        customer_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(
            customer_content,
            text="👤 العميل:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['body_medium']
        ).pack(anchor='w', pady=(0, 5))
        
        self.customer_combo = ttk.Combobox(
            customer_content,
            font=self.styles.fonts['body_medium'],
            style='Modern.TCombobox',
            state='readonly'
        )
        self.customer_combo.pack(fill=tk.X)
        
        # عربة التسوق
        cart_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        cart_frame.pack(fill=tk.X, pady=(0, 15))
        
        cart_content = tk.Frame(
            cart_frame,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        cart_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(
            cart_content,
            text="🛒 عربة التسوق:",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small']
        ).pack(anchor='w', pady=(0, 10))
        
        # جدول عربة التسوق
        cart_table_frame = tk.Frame(
            cart_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        cart_table_frame.pack(fill=tk.X)
        
        columns = ("المنتج", "السعر", "الكمية", "المجموع", "المخزون المتبقي")
        self.cart_tree = ttk.Treeview(
            cart_table_frame,
            columns=columns,
            show="headings",
            height=6,
            style='Modern.Treeview'
        )
        
        for col in columns:
            self.cart_tree.heading(col, text=col)
            self.cart_tree.column(col, width=140)
        
        cart_scrollbar = ttk.Scrollbar(
            cart_table_frame,
            orient=tk.VERTICAL,
            command=self.cart_tree.yview,
            style='Modern.Vertical.TScrollbar'
        )
        self.cart_tree.configure(yscrollcommand=cart_scrollbar.set)
        
        self.cart_tree.pack(side=tk.LEFT, fill=tk.X, expand=True)
        cart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار إدارة العربة
        cart_buttons_frame = tk.Frame(
            cart_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        cart_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        remove_btn = tk.Button(
            cart_buttons_frame,
            text="🗑️ حذف المحدد",
            command=self.remove_from_cart,
            **self.styles.get_button_style('danger'),
            font=self.styles.fonts['body_small']
        )
        remove_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = tk.Button(
            cart_buttons_frame,
            text="🧹 مسح الكل",
            command=self.clear_cart,
            **self.styles.get_button_style('secondary'),
            font=self.styles.fonts['body_small']
        )
        clear_btn.pack(side=tk.LEFT)
        
        # إجمالي المبلغ
        total_frame = tk.Frame(
            cart_content,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        total_frame.pack(fill=tk.X, pady=(15, 0))
        
        self.total_label = tk.Label(
            total_frame,
            text="💰 الإجمالي: 0.00 ₪",
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['success'],
            font=('Segoe UI', 16, 'bold')
        )
        self.total_label.pack(anchor='e')
        
        # أزرار العمل
        buttons_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            **self.styles.get_button_style('secondary'),
            font=self.styles.fonts['body_medium']
        )
        cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ البيع",
            command=self.save_sale,
            **self.styles.get_button_style('success'),
            font=self.styles.fonts['body_medium']
        )
        save_btn.pack(side=tk.RIGHT)

    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            conn = self.db_manager.connect()
            if not conn:
                return

            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM customers ORDER BY name")
            customers = cursor.fetchall()

            customer_list = [f"{customer[0]} - {customer[1]}" for customer in customers]
            self.customer_combo['values'] = customer_list

            if customer_list:
                self.customer_combo.current(0)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل العملاء: {e}")

    def search_product(self):
        """البحث عن منتج بالباركود أو الاسم"""
        search_term = self.barcode_entry.get().strip()

        if not search_term:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود أو اسم المنتج")
            return

        try:
            conn = self.db_manager.connect()
            if not conn:
                return

            cursor = conn.cursor()

            # البحث بالباركود أو الاسم
            cursor.execute('''
                SELECT id, item_name, selling_price, quantity, barcode
                FROM inventory
                WHERE barcode = ? OR item_name LIKE ?
                LIMIT 1
            ''', (search_term, f"%{search_term}%"))

            product = cursor.fetchone()

            if product:
                self.selected_product = {
                    'id': product[0],
                    'name': product[1],
                    'price': product[2],
                    'stock': product[3],
                    'barcode': product[4]
                }
                self.show_product_info()
                self.add_to_cart()
            else:
                messagebox.showinfo("غير موجود", "المنتج غير موجود في المخزون")
                self.clear_product_info()

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {e}")

    def show_product_info(self):
        """عرض معلومات المنتج المحدد"""
        # مسح المعلومات السابقة
        for widget in self.product_info_frame.winfo_children():
            widget.destroy()

        if not self.selected_product:
            return

        # إطار معلومات المنتج
        info_frame = tk.Frame(
            self.product_info_frame,
            bg=self.styles.colors['info'],
            relief='flat',
            bd=0
        )
        info_frame.pack(fill=tk.X, pady=5)

        info_content = tk.Frame(
            info_frame,
            bg=self.styles.colors['info'],
            relief='flat',
            bd=0
        )
        info_content.pack(fill=tk.X, padx=15, pady=10)

        # معلومات المنتج
        product_name = tk.Label(
            info_content,
            text=f"📱 {self.selected_product['name']}",
            bg=self.styles.colors['info'],
            fg=self.styles.colors['text_white'],
            font=self.styles.fonts['body_medium']
        )
        product_name.pack(anchor='w')

        product_details = tk.Label(
            info_content,
            text=f"💰 السعر: {self.selected_product['price']:.2f} ₪ | 📦 المخزون: {self.selected_product['stock']} قطعة",
            bg=self.styles.colors['info'],
            fg=self.styles.colors['text_white'],
            font=self.styles.fonts['body_small']
        )
        product_details.pack(anchor='w')

        # حقل الكمية
        quantity_frame = tk.Frame(
            info_content,
            bg=self.styles.colors['info'],
            relief='flat',
            bd=0
        )
        quantity_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Label(
            quantity_frame,
            text="الكمية:",
            bg=self.styles.colors['info'],
            fg=self.styles.colors['text_white'],
            font=self.styles.fonts['body_small']
        ).pack(side=tk.LEFT, padx=(0, 10))

        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = tk.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            width=10,
            font=self.styles.fonts['body_small']
        )
        quantity_entry.pack(side=tk.LEFT, padx=(0, 10))

        add_btn = tk.Button(
            quantity_frame,
            text="➕ إضافة للعربة",
            command=self.add_to_cart,
            bg=self.styles.colors['success'],
            fg=self.styles.colors['text_white'],
            font=self.styles.fonts['body_small'],
            relief='flat',
            bd=0,
            padx=10,
            pady=5
        )
        add_btn.pack(side=tk.LEFT)

    def clear_product_info(self):
        """مسح معلومات المنتج"""
        for widget in self.product_info_frame.winfo_children():
            widget.destroy()
        self.selected_product = None

    def add_to_cart(self):
        """إضافة المنتج لعربة التسوق"""
        if not self.selected_product:
            return

        try:
            quantity = int(self.quantity_var.get()) if hasattr(self, 'quantity_var') else 1

            if quantity <= 0:
                messagebox.showwarning("تحذير", "الكمية يجب أن تكون أكبر من صفر")
                return

            if quantity > self.selected_product['stock']:
                messagebox.showwarning("تحذير", f"الكمية المطلوبة ({quantity}) أكبر من المخزون المتاح ({self.selected_product['stock']})")
                return

            # التحقق من وجود المنتج في العربة
            existing_item = None
            for item in self.cart_items:
                if item['id'] == self.selected_product['id']:
                    existing_item = item
                    break

            if existing_item:
                # تحديث الكمية
                new_quantity = existing_item['quantity'] + quantity
                if new_quantity > self.selected_product['stock']:
                    messagebox.showwarning("تحذير", f"إجمالي الكمية ({new_quantity}) أكبر من المخزون المتاح ({self.selected_product['stock']})")
                    return
                existing_item['quantity'] = new_quantity
                existing_item['total'] = existing_item['quantity'] * existing_item['price']
            else:
                # إضافة منتج جديد
                cart_item = {
                    'id': self.selected_product['id'],
                    'name': self.selected_product['name'],
                    'price': self.selected_product['price'],
                    'quantity': quantity,
                    'total': self.selected_product['price'] * quantity,
                    'stock': self.selected_product['stock']
                }
                self.cart_items.append(cart_item)

            self.update_cart_display()
            self.clear_product_info()
            self.barcode_entry.delete(0, tk.END)
            self.barcode_entry.focus()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")

    def update_cart_display(self):
        """تحديث عرض عربة التسوق"""
        # مسح العناصر الحالية
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)

        # إضافة العناصر الجديدة
        self.total_amount = 0.0
        for item in self.cart_items:
            remaining_stock = item['stock'] - item['quantity']
            self.cart_tree.insert('', 'end', values=(
                item['name'],
                f"{item['price']:.2f} ₪",
                item['quantity'],
                f"{item['total']:.2f} ₪",
                f"{remaining_stock} قطعة"
            ))
            self.total_amount += item['total']

        # تحديث الإجمالي
        self.total_label.configure(text=f"💰 الإجمالي: {self.total_amount:.2f} ₪")

    def remove_from_cart(self):
        """حذف العنصر المحدد من العربة"""
        selected_item = self.cart_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى تحديد عنصر للحذف")
            return

        # الحصول على فهرس العنصر
        item_index = self.cart_tree.index(selected_item[0])

        # حذف العنصر من القائمة
        if 0 <= item_index < len(self.cart_items):
            del self.cart_items[item_index]
            self.update_cart_display()

    def clear_cart(self):
        """مسح جميع عناصر العربة"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع عناصر العربة؟"):
            self.cart_items.clear()
            self.update_cart_display()

    def save_sale(self):
        """حفظ البيع في قاعدة البيانات"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "لا توجد منتجات في العربة")
            return

        if not self.customer_combo.get():
            messagebox.showwarning("تحذير", "يرجى اختيار عميل")
            return

        try:
            # الحصول على معرف العميل
            customer_text = self.customer_combo.get()
            customer_id = int(customer_text.split(' - ')[0])

            conn = self.db_manager.connect()
            if not conn:
                return

            cursor = conn.cursor()

            # بدء المعاملة
            cursor.execute("BEGIN TRANSACTION")

            # حفظ كل منتج في العربة
            for item in self.cart_items:
                # إدراج البيع
                cursor.execute('''
                    INSERT INTO sales (customer_id, item_id, quantity, unit_price, total_amount, sale_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    customer_id,
                    item['id'],
                    item['quantity'],
                    item['price'],
                    item['total'],
                    datetime.now()
                ))

                # تحديث المخزون
                cursor.execute('''
                    UPDATE inventory
                    SET quantity = quantity - ?
                    WHERE id = ?
                ''', (item['quantity'], item['id']))

                # التحقق من أن المخزون لم يصبح سالباً
                cursor.execute('SELECT quantity FROM inventory WHERE id = ?', (item['id'],))
                new_quantity = cursor.fetchone()[0]

                if new_quantity < 0:
                    raise Exception(f"المخزون غير كافي للمنتج: {item['name']}")

            # تسجيل النشاط
            if self.auth_manager.current_user:
                self.auth_manager.log_activity(
                    self.auth_manager.current_user['id'],
                    "إضافة بيع",
                    f"بيع بقيمة {self.total_amount:.2f} ₪ للعميل {customer_id}"
                )

            # تأكيد المعاملة
            cursor.execute("COMMIT")
            conn.close()

            messagebox.showinfo("نجح", f"تم حفظ البيع بنجاح!\nالإجمالي: {self.total_amount:.2f} ₪")

            # مسح العربة وإغلاق النافذة
            self.cart_items.clear()
            self.update_cart_display()
            self.window.destroy()

        except Exception as e:
            # التراجع عن المعاملة في حالة الخطأ
            try:
                cursor.execute("ROLLBACK")
                conn.close()
            except:
                pass

            messagebox.showerror("خطأ", f"خطأ في حفظ البيع: {e}")

    def show(self):
        """عرض النافذة"""
        self.window.grab_set()  # جعل النافذة modal
        self.window.wait_window()  # انتظار إغلاق النافذة
