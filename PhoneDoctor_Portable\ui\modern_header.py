#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
رأس الصفحة العصري - Modern Header
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from .modern_styles import ModernStyles

class ModernHeader(tk.Frame):
    """رأس الصفحة العصري"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.styles = ModernStyles()
        self.current_page = "لوحة التحكم"
        self.current_page_icon = "🏠"
        
        # تكوين الإطار الرئيسي
        self.configure(
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0,
            height=80
        )
        
        # منع تغيير الحجم
        self.pack_propagate(False)
        
        self.create_header_content()
        self.start_time_update()
        
    def create_header_content(self):
        """إنشاء محتوى الرأس"""
        # الإطار الرئيسي للمحتوى
        main_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=24, pady=16)
        
        # الجانب الأيسر - معلومات الصفحة
        left_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        left_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        # إطار عنوان الصفحة
        page_title_frame = tk.Frame(
            left_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        page_title_frame.pack(fill=tk.X)
        
        # أيقونة الصفحة
        self.page_icon_label = tk.Label(
            page_title_frame,
            text=self.current_page_icon,
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['primary'],
            font=('Segoe UI Emoji', 20),
            anchor='w'
        )
        self.page_icon_label.pack(side=tk.LEFT, padx=(0, 12))
        
        # اسم الصفحة
        self.page_title_label = tk.Label(
            page_title_frame,
            text=self.current_page,
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_medium'],
            anchor='w'
        )
        self.page_title_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # مسار التنقل (Breadcrumb)
        self.breadcrumb_label = tk.Label(
            left_frame,
            text="الرئيسية > لوحة التحكم",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['body_small'],
            anchor='w'
        )
        self.breadcrumb_label.pack(fill=tk.X, pady=(4, 0))
        
        # الجانب الأيمن - معلومات النظام
        right_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الوقت والتاريخ
        datetime_frame = tk.Frame(
            right_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        datetime_frame.pack(anchor='e')
        
        # الوقت
        self.time_label = tk.Label(
            datetime_frame,
            text="",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            font=('Segoe UI', 16, 'bold'),
            anchor='e'
        )
        self.time_label.pack(anchor='e')
        
        # التاريخ
        self.date_label = tk.Label(
            datetime_frame,
            text="",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_secondary'],
            font=self.styles.fonts['body_small'],
            anchor='e'
        )
        self.date_label.pack(anchor='e', pady=(2, 0))
        
        # إطار حالة النظام
        status_frame = tk.Frame(
            right_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        status_frame.pack(anchor='e', pady=(8, 0))
        
        # مؤشر حالة قاعدة البيانات
        db_status_frame = tk.Frame(
            status_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        db_status_frame.pack(anchor='e')
        
        # نقطة الحالة
        status_dot = tk.Label(
            db_status_frame,
            text="●",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['success'],
            font=('Segoe UI', 12),
            anchor='e'
        )
        status_dot.pack(side=tk.LEFT, padx=(0, 4))
        
        # نص الحالة
        status_text = tk.Label(
            db_status_frame,
            text="قاعدة البيانات متصلة",
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['caption'],
            anchor='e'
        )
        status_text.pack(side=tk.LEFT)
        
        # خط فاصل سفلي
        separator = tk.Frame(
            self,
            bg=self.styles.colors['border_light'],
            height=1
        )
        separator.pack(fill=tk.X, side=tk.BOTTOM)
    
    def update_page_info(self, page_name, page_icon, breadcrumb=None):
        """تحديث معلومات الصفحة"""
        self.current_page = page_name
        self.current_page_icon = page_icon
        
        # تحديث العناصر
        self.page_icon_label.configure(text=page_icon)
        self.page_title_label.configure(text=page_name)
        
        # تحديث مسار التنقل
        if breadcrumb:
            self.breadcrumb_label.configure(text=breadcrumb)
        else:
            self.breadcrumb_label.configure(text=f"الرئيسية > {page_name}")
    
    def start_time_update(self):
        """بدء تحديث الوقت"""
        self.update_datetime()
        self.after(1000, self.start_time_update)
    
    def update_datetime(self):
        """تحديث الوقت والتاريخ"""
        now = datetime.now()
        
        # تنسيق الوقت
        time_str = now.strftime("%H:%M:%S")
        self.time_label.configure(text=time_str)
        
        # تنسيق التاريخ بالعربية
        weekdays = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
        
        weekday = weekdays[now.weekday()]
        month = months[now.month - 1]
        
        date_str = f"{weekday}, {now.day} {month} {now.year}"
        self.date_label.configure(text=date_str)

class ModernPageHeader(tk.Frame):
    """رأس الصفحة الفرعية"""
    
    def __init__(self, parent, title, subtitle=None, actions=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.styles = ModernStyles()
        
        # تكوين الإطار
        self.configure(
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        
        self.create_content(title, subtitle, actions)
    
    def create_content(self, title, subtitle, actions):
        """إنشاء محتوى رأس الصفحة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=24, pady=16)
        
        # الجانب الأيسر - العنوان والوصف
        left_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_primary'],
            relief='flat',
            bd=0
        )
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            left_frame,
            text=title,
            bg=self.styles.colors['bg_primary'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_large'],
            anchor='w'
        )
        title_label.pack(fill=tk.X)
        
        # العنوان الفرعي
        if subtitle:
            subtitle_label = tk.Label(
                left_frame,
                text=subtitle,
                bg=self.styles.colors['bg_primary'],
                fg=self.styles.colors['text_secondary'],
                font=self.styles.fonts['body_medium'],
                anchor='w'
            )
            subtitle_label.pack(fill=tk.X, pady=(4, 0))
        
        # الجانب الأيمن - الأعمال
        if actions:
            right_frame = tk.Frame(
                main_frame,
                bg=self.styles.colors['bg_primary'],
                relief='flat',
                bd=0
            )
            right_frame.pack(side=tk.RIGHT, fill=tk.Y)
            
            # إضافة الأزرار
            for action in actions:
                if isinstance(action, dict):
                    btn = tk.Button(
                        right_frame,
                        text=action.get('text', ''),
                        command=action.get('command'),
                        **self.styles.get_button_style(action.get('variant', 'primary'))
                    )
                    btn.pack(side=tk.LEFT, padx=(8, 0))
        
        # خط فاصل
        separator = tk.Frame(
            self,
            bg=self.styles.colors['border_light'],
            height=1
        )
        separator.pack(fill=tk.X, side=tk.BOTTOM)

class ModernCard(tk.Frame):
    """بطاقة عصرية"""
    
    def __init__(self, parent, title=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.styles = ModernStyles()
        
        # تكوين الإطار
        self.configure(
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.styles.colors['border_light'],
            highlightthickness=1
        )
        
        if title:
            self.create_header(title)
        
        # إطار المحتوى
        self.content_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=16)
    
    def create_header(self, title):
        """إنشاء رأس البطاقة"""
        header_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_card'],
            relief='flat',
            bd=0,
            height=50
        )
        header_frame.pack(fill=tk.X, padx=20, pady=(16, 0))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(
            header_frame,
            text=title,
            bg=self.styles.colors['bg_card'],
            fg=self.styles.colors['text_primary'],
            font=self.styles.fonts['heading_small'],
            anchor='w'
        )
        title_label.pack(fill=tk.BOTH, expand=True)
        
        # خط فاصل
        separator = tk.Frame(
            self,
            bg=self.styles.colors['border_light'],
            height=1
        )
        separator.pack(fill=tk.X, padx=20)
    
    def get_content_frame(self):
        """الحصول على إطار المحتوى"""
        return self.content_frame
