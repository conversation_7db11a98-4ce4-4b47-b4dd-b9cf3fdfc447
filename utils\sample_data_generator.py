#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد البيانات التجريبية
Sample Data Generator

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import random
from datetime import datetime, timedelta
import json

class SampleDataGenerator:
    """مولد البيانات التجريبية"""
    
    def __init__(self, db_path="phone_doctor.db"):
        self.db_path = db_path
        
        # بيانات تجريبية
        self.customer_names = [
            "أحمد محمد علي", "فاطمة أحمد", "محمد عبدالله", "عائشة محمود",
            "علي حسن", "خديجة عمر", "عمر سالم", "زينب أحمد",
            "حسن محمد", "مريم علي", "سالم عبدالله", "نور فاتح",
            "يوسف إبراهيم", "هدى محمد", "إبراهيم أحمد", "سارة علي",
            "محمود حسن", "ليلى عمر", "طارق محمد", "رقية أحمد"
        ]
        
        self.phone_models = [
            "iPhone 14 Pro", "iPhone 13", "iPhone 12", "iPhone 11",
            "Samsung Galaxy S23", "Samsung Galaxy S22", "Samsung Galaxy A54",
            "Huawei P50", "Xiaomi 13", "OnePlus 11", "Google Pixel 7",
            "Oppo Find X5", "Vivo X90", "Realme GT3", "Nothing Phone 2"
        ]
        
        self.repair_issues = [
            "شاشة مكسورة", "بطارية تالفة", "مشكلة في الشحن", "مشكلة في الصوت",
            "كاميرا لا تعمل", "مشكلة في اللمس", "جهاز لا يشتغل", "مشكلة في الواي فاي",
            "مشكلة في البلوتوث", "بطء في الأداء", "مشكلة في السماعة", "مشكلة في الميكروفون"
        ]
        
        self.repair_statuses = ["جديد", "قيد الفحص", "قيد الإصلاح", "جاهز للتسليم", "مكتمل"]
        
        self.inventory_items = [
            ("شاشة iPhone 14", "شاشات", 150.0, 200.0),
            ("بطارية Samsung S23", "بطاريات", 80.0, 120.0),
            ("كفر حماية شفاف", "إكسسوارات", 15.0, 25.0),
            ("شاحن سريع Type-C", "شواحن", 25.0, 40.0),
            ("سماعات لاسلكية", "إكسسوارات", 60.0, 100.0),
            ("كابل Lightning", "كوابل", 20.0, 35.0),
            ("حامل هاتف للسيارة", "إكسسوارات", 30.0, 50.0),
            ("واقي شاشة زجاجي", "واقيات", 10.0, 20.0),
            ("بطارية iPhone 13", "بطاريات", 90.0, 130.0),
            ("شاشة Samsung A54", "شاشات", 100.0, 150.0)
        ]
        
        self.suppliers = [
            ("شركة التقنية المتقدمة", "أحمد محمد", "0501234567", "<EMAIL>"),
            ("مؤسسة الهواتف الذكية", "فاطمة علي", "0507654321", "<EMAIL>"),
            ("متجر قطع الغيار", "محمد حسن", "0509876543", "<EMAIL>"),
            ("شركة الإكسسوارات الحديثة", "علي أحمد", "0502468135", "<EMAIL>"),
            ("مركز الصيانة المتخصص", "سارة محمود", "0508642097", "<EMAIL>")
        ]
    
    def generate_all_sample_data(self):
        """توليد جميع البيانات التجريبية"""
        print("🔄 بدء توليد البيانات التجريبية...")
        
        try:
            self.generate_customers()
            self.generate_inventory()
            self.generate_suppliers()
            self.generate_repairs()
            self.generate_sales()
            
            print("✅ تم توليد جميع البيانات التجريبية بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في توليد البيانات التجريبية: {e}")
            return False
    
    def generate_customers(self):
        """توليد بيانات العملاء"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM customers")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        for i, name in enumerate(self.customer_names):
            phone = f"050{random.randint(1000000, 9999999)}"
            email = f"customer{i+1}@example.com"
            address = f"العنوان {i+1}, المدينة"
            
            cursor.execute('''
                INSERT INTO customers (name, phone, email, address)
                VALUES (?, ?, ?, ?)
            ''', (name, phone, email, address))
        
        conn.commit()
        conn.close()
        print("✅ تم توليد بيانات العملاء")
    
    def generate_inventory(self):
        """توليد بيانات المخزون"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM inventory")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        for name, category, purchase_price, selling_price in self.inventory_items:
            quantity = random.randint(5, 50)
            min_quantity = random.randint(3, 10)
            barcode = f"123456{random.randint(100000, 999999)}"
            
            cursor.execute('''
                INSERT INTO inventory (item_name, category, quantity, min_quantity, purchase_price, selling_price, barcode)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (name, category, quantity, min_quantity, purchase_price, selling_price, barcode))
        
        conn.commit()
        conn.close()
        print("✅ تم توليد بيانات المخزون")
    
    def generate_suppliers(self):
        """توليد بيانات الموردين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM suppliers")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        for company, contact_person, phone, email in self.suppliers:
            address = f"عنوان {company}"
            balance = random.uniform(-1000, 5000)
            
            cursor.execute('''
                INSERT INTO suppliers (company, name, phone, email, address, balance)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (company, contact_person, phone, email, address, balance))
        
        conn.commit()
        conn.close()
        print("✅ تم توليد بيانات الموردين")
    
    def generate_repairs(self):
        """توليد بيانات الصيانة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM repairs")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        # الحصول على معرفات العملاء
        cursor.execute("SELECT id FROM customers")
        customer_ids = [row[0] for row in cursor.fetchall()]
        
        for i in range(30):  # 30 عملية صيانة
            customer_id = random.choice(customer_ids)
            device_type = random.choice(self.phone_models)
            issue_description = random.choice(self.repair_issues)
            status = random.choice(self.repair_statuses)
            cost = random.uniform(50, 500)
            
            # تاريخ عشوائي في آخر 3 أشهر
            days_ago = random.randint(0, 90)
            repair_date = datetime.now() - timedelta(days=days_ago)
            
            cursor.execute('''
                INSERT INTO repairs (customer_id, device_type, problem_description, repair_status, estimated_cost, date_received)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (customer_id, device_type, issue_description, status, cost, repair_date))
        
        conn.commit()
        conn.close()
        print("✅ تم توليد بيانات الصيانة")
    
    def generate_sales(self):
        """توليد بيانات المبيعات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM sales")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        # الحصول على معرفات العملاء والمنتجات
        cursor.execute("SELECT id FROM customers")
        customer_ids = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id, item_name, selling_price FROM inventory")
        inventory_items = cursor.fetchall()
        
        for i in range(50):  # 50 عملية بيع
            customer_id = random.choice(customer_ids)
            item_id, item_name, selling_price = random.choice(inventory_items)
            quantity = random.randint(1, 3)
            total_amount = selling_price * quantity
            
            # تاريخ عشوائي في آخر شهرين
            days_ago = random.randint(0, 60)
            sale_date = datetime.now() - timedelta(days=days_ago)
            
            cursor.execute('''
                INSERT INTO sales (customer_id, item_id, quantity, unit_price, total_amount, sale_date)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (customer_id, item_id, quantity, selling_price, total_amount, sale_date))
        
        conn.commit()
        conn.close()
        print("✅ تم توليد بيانات المبيعات")
    
    def clear_all_data(self):
        """مسح جميع البيانات التجريبية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # مسح البيانات بالترتيب الصحيح (بسبب المفاتيح الخارجية)
            tables = ['sales', 'repairs', 'inventory', 'suppliers', 'customers']
            
            for table in tables:
                cursor.execute(f"DELETE FROM {table}")
            
            conn.commit()
            conn.close()
            
            print("✅ تم مسح جميع البيانات التجريبية")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في مسح البيانات: {e}")
            return False
