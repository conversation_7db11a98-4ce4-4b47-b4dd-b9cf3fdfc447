#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def test_system():
    print("🔧 اختبار متطلبات النظام...")
    print("=" * 40)
    
    # اختبار Python
    print(f"✅ Python {sys.version}")
    
    # اختبار المكتبات
    try:
        import tkinter
        print("✅ Tkinter متوفر")
    except ImportError:
        print("❌ Tkinter غير متوفر")
        return False
    
    try:
        import sqlite3
        print("✅ SQLite3 متوفر")
    except ImportError:
        print("❌ SQLite3 غير متوفر")
        return False
    
    # اختبار الملفات
    required_files = ['main_tkinter.py', 'database/db_manager.py', 'utils/config.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} غير موجود")
            return False
    
    print("=" * 40)
    print("🎉 جميع المتطلبات متوفرة!")
    print("يمكنك الآن تشغيل البرنامج بأمان.")
    
    return True

if __name__ == "__main__":
    test_system()
    input("\nاضغط Enter للخروج...")
