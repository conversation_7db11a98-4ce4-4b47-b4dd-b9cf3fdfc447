#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث بيانات الصيانة - Phone Doctor v2.0
Update Repairs Data with New Fields

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import random
from datetime import datetime, timed<PERSON>ta

def update_repairs_data():
    """تحديث بيانات الصيانة لتشمل الحقول الجديدة"""
    
    print("🔧 تحديث بيانات الصيانة...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
        cursor.execute("PRAGMA table_info(repairs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        new_columns = [
            ('device_serial', 'TEXT'),
            ('parts_used', 'TEXT'),
            ('total_cost', 'REAL'),
            ('repair_date', 'TIMESTAMP'),
            ('parts_cost', 'REAL'),
            ('labor_cost', 'REAL')
        ]
        
        for column_name, column_type in new_columns:
            if column_name not in columns:
                print(f"📝 إضافة عمود {column_name}...")
                cursor.execute(f"ALTER TABLE repairs ADD COLUMN {column_name} {column_type}")
        
        # جلب جميع أوامر الصيانة الموجودة
        cursor.execute("SELECT id, device_type, device_brand, estimated_cost, actual_cost, created_date FROM repairs")
        repairs = cursor.fetchall()
        
        print(f"🔄 تحديث {len(repairs)} أمر صيانة...")
        
        # قطع الغيار الشائعة
        common_parts = [
            "شاشة LCD",
            "بطارية ليثيوم",
            "زجاج خلفي",
            "كاميرا خلفية",
            "كاميرا أمامية",
            "سماعة داخلية",
            "سماعة خارجية",
            "مايكروفون",
            "زر الباور",
            "أزرار الصوت",
            "فليكس الشحن",
            "مقبس سماعة",
            "حساس البصمة",
            "حساس القرب",
            "فليكس الواي فاي",
            "مودم البلوتوث",
            "لوحة الشحن",
            "إطار معدني"
        ]
        
        # أنواع الأجهزة وسيريالاتها
        device_serials = {
            'iPhone': lambda: f"F{random.randint(100000000000, 999999999999)}",
            'Samsung': lambda: f"R{random.randint(10000000000, 99999999999)}",
            'Huawei': lambda: f"H{random.randint(1000000000, 9999999999)}",
            'Xiaomi': lambda: f"M{random.randint(100000000, 999999999)}",
            'Oppo': lambda: f"O{random.randint(10000000, 99999999)}",
            'Vivo': lambda: f"V{random.randint(10000000, 99999999)}",
            'OnePlus': lambda: f"P{random.randint(1000000, 9999999)}",
            'Nokia': lambda: f"N{random.randint(100000, 999999)}"
        }
        
        for repair in repairs:
            repair_id, device_type, device_brand, estimated_cost, actual_cost, created_date = repair
            
            # إنشاء سيريال للجهاز
            device_serial = "غير محدد"
            if device_brand:
                for brand in device_serials:
                    if brand.lower() in device_brand.lower():
                        device_serial = device_serials[brand]()
                        break
            
            if device_serial == "غير محدد" and device_type:
                # استخدام نوع الجهاز لتحديد السيريال
                if 'iphone' in device_type.lower():
                    device_serial = device_serials['iPhone']()
                elif 'samsung' in device_type.lower():
                    device_serial = device_serials['Samsung']()
                else:
                    device_serial = f"SN{random.randint(100000000, 999999999)}"
            
            # اختيار قطع غيار عشوائية (1-3 قطع)
            num_parts = random.randint(1, 3)
            selected_parts = random.sample(common_parts, num_parts)
            parts_used = ", ".join(selected_parts)
            
            # حساب التكاليف
            base_cost = actual_cost or estimated_cost or random.uniform(50, 500)
            
            # تكلفة القطع (30-60% من التكلفة الإجمالية)
            parts_cost = base_cost * random.uniform(0.3, 0.6)
            
            # تكلفة العمالة (40-70% من التكلفة الإجمالية)
            labor_cost = base_cost * random.uniform(0.4, 0.7)
            
            # التكلفة الإجمالية
            total_cost = parts_cost + labor_cost
            
            # تاريخ الصيانة (بين تاريخ الإنشاء واليوم)
            if created_date:
                try:
                    created_datetime = datetime.strptime(created_date, "%Y-%m-%d %H:%M:%S")
                except:
                    try:
                        created_datetime = datetime.strptime(created_date[:19], "%Y-%m-%d %H:%M:%S")
                    except:
                        created_datetime = datetime.now() - timedelta(days=random.randint(1, 30))
            else:
                created_datetime = datetime.now() - timedelta(days=random.randint(1, 30))
            
            # تاريخ الصيانة (1-7 أيام بعد تاريخ الإنشاء)
            repair_days = random.randint(1, 7)
            repair_date = created_datetime + timedelta(days=repair_days)
            
            # تحديث السجل
            cursor.execute('''
                UPDATE repairs 
                SET device_serial = ?, parts_used = ?, total_cost = ?, 
                    repair_date = ?, parts_cost = ?, labor_cost = ?
                WHERE id = ?
            ''', (
                device_serial, parts_used, round(total_cost, 2), 
                repair_date.strftime('%Y-%m-%d %H:%M:%S'), 
                round(parts_cost, 2), round(labor_cost, 2), 
                repair_id
            ))
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("✅ تم تحديث بيانات الصيانة بنجاح!")
        
        # عرض إحصائيات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM repairs")
        total_repairs = cursor.fetchone()[0]
        
        cursor.execute("SELECT AVG(total_cost) FROM repairs WHERE total_cost > 0")
        avg_cost = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(total_cost) FROM repairs WHERE total_cost > 0")
        total_revenue = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT COUNT(DISTINCT device_serial) FROM repairs WHERE device_serial != 'غير محدد'")
        unique_devices = cursor.fetchone()[0]
        
        conn.close()
        
        print("\n📈 إحصائيات الصيانة المحدثة:")
        print(f"  🔧 إجمالي أوامر الصيانة: {total_repairs}")
        print(f"  📱 أجهزة فريدة: {unique_devices}")
        print(f"  💰 متوسط التكلفة: {avg_cost:.2f} ₪")
        print(f"  💵 إجمالي الإيرادات: {total_revenue:.2f} ₪")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_repairs_data()
