#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة المخزون مع عمليات CRUD
Inventory Manager with CRUD Operations

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime

class InventoryManager:
    """مدير المخزون مع عمليات الإضافة والتعديل والحذف"""

    def __init__(self, parent_frame, db_manager):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.selected_item = None

        # ألوان النظام
        self.colors = {
            'primary': '#2563eb',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'bg_light': '#f8fafc',
            'text_dark': '#1f2937'
        }

        self.setup_ui()
        self.load_inventory()

    def setup_ui(self):
        """إعداد واجهة إدارة المخزون"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()

        # العنوان الرئيسي
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="📦 إدارة المخزون",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        title_label.pack(side=tk.LEFT)

        # أزرار العمليات
        buttons_frame = tk.Frame(title_frame, bg='white')
        buttons_frame.pack(side=tk.RIGHT)

        # زر إضافة منتج جديد
        add_btn = tk.Button(
            buttons_frame,
            text="➕ إضافة منتج",
            command=self.add_item,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تعديل منتج
        edit_btn = tk.Button(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_item,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر حذف منتج
        delete_btn = tk.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_item,
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث المخزون
        update_stock_btn = tk.Button(
            buttons_frame,
            text="📈 تحديث الكمية",
            command=self.update_stock,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        update_stock_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.load_inventory,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.LEFT)

        # شريط البحث والفلترة
        filter_frame = tk.Frame(self.parent_frame, bg='white')
        filter_frame.pack(fill=tk.X, padx=20, pady=10)

        # البحث
        search_label = tk.Label(
            filter_frame,
            text="🔍 البحث:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        search_label.pack(side=tk.LEFT, padx=(0, 10))

        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)

        search_entry = tk.Entry(
            filter_frame,
            textvariable=self.search_var,
            font=('Arial', 12),
            relief='solid',
            bd=1,
            width=25
        )
        search_entry.pack(side=tk.LEFT, padx=(0, 20))

        # فلتر الفئة
        category_label = tk.Label(
            filter_frame,
            text="📂 الفئة:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        category_label.pack(side=tk.LEFT, padx=(0, 10))

        self.category_var = tk.StringVar()
        self.category_var.trace('w', self.on_search)

        self.category_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.category_var,
            font=('Arial', 11),
            width=15,
            state='readonly'
        )
        self.category_combo.pack(side=tk.LEFT, padx=(0, 20))

        # فلتر المخزون المنخفض
        low_stock_btn = tk.Button(
            filter_frame,
            text="⚠️ مخزون منخفض",
            command=self.show_low_stock,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            bd=0,
            padx=10,
            pady=5,
            cursor='hand2'
        )
        low_stock_btn.pack(side=tk.LEFT)

        # جدول المخزون
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء الجدول
        columns = ("ID", "اسم المنتج", "الفئة", "الكمية", "سعر البيع", "الباركود", "تاريخ الإضافة")
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

        # تعيين العناوين وعرض الأعمدة
        column_widths = [50, 200, 120, 80, 100, 120, 120]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor='center')

        # تلوين الصفوف حسب المخزون
        self.tree.tag_configure('low_stock', background='#fef2f2', foreground='#dc2626')
        self.tree.tag_configure('out_of_stock', background='#fee2e2', foreground='#991b1b')
        self.tree.tag_configure('normal_stock', background='#f0fdf4', foreground='#166534')

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.edit_item)

        # شريط الحالة
        status_frame = tk.Frame(self.parent_frame, bg='white')
        status_frame.pack(fill=tk.X, padx=20, pady=10)

        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 10)
        )
        self.status_label.pack(side=tk.LEFT)

        self.count_label = tk.Label(
            status_frame,
            text="",
            bg='white',
            fg=self.colors['primary'],
            font=('Arial', 10, 'bold')
        )
        self.count_label.pack(side=tk.RIGHT)

    def load_inventory(self):
        """تحميل قائمة المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # جلب المنتجات من قاعدة البيانات
            items = self.db_manager.fetch_all("SELECT * FROM inventory ORDER BY item_name")

            # جلب الفئات للفلتر
            categories = self.db_manager.fetch_all("SELECT DISTINCT category FROM inventory ORDER BY category")
            category_list = ['جميع الفئات'] + [cat['category'] for cat in categories]
            self.category_combo['values'] = category_list
            if not self.category_var.get():
                self.category_var.set('جميع الفئات')

            # إضافة البيانات للجدول
            for item in items:
                # تنسيق التاريخ
                created_at = item['created_at'][:10] if item['created_at'] else 'غير محدد'

                # تحديد لون الصف حسب الكمية
                quantity = item['quantity']
                if quantity == 0:
                    tag = 'out_of_stock'
                elif quantity <= 5:
                    tag = 'low_stock'
                else:
                    tag = 'normal_stock'

                item_id = self.tree.insert('', 'end', values=(
                    item['id'],
                    item['item_name'],
                    item['category'],
                    quantity,
                    f"{item['selling_price']:.2f} ₪",
                    item['barcode'] or 'غير محدد',
                    created_at
                ), tags=(tag,))

            # تحديث عداد المنتجات
            count = len(items)
            total_value = sum(item['quantity'] * item['selling_price'] for item in items)
            self.count_label.configure(text=f"إجمالي المنتجات: {count} | القيمة الإجمالية: {total_value:.2f} ₪")
            self.status_label.configure(text="تم تحميل البيانات بنجاح")

        except Exception as e:
            print(f"خطأ في تحميل المخزون: {e}")
            self.status_label.configure(text="خطأ في تحميل البيانات")

    def on_search(self, *args):
        """البحث والفلترة في المخزون"""
        search_term = self.search_var.get().strip()
        category_filter = self.category_var.get()

        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # بناء الاستعلام
            query = "SELECT * FROM inventory WHERE 1=1"
            params = []

            # إضافة شرط البحث
            if search_term:
                query += " AND (item_name LIKE ? OR barcode LIKE ?)"
                search_pattern = f'%{search_term}%'
                params.extend([search_pattern, search_pattern])

            # إضافة شرط الفئة
            if category_filter and category_filter != 'جميع الفئات':
                query += " AND category = ?"
                params.append(category_filter)

            query += " ORDER BY item_name"

            # تنفيذ الاستعلام
            items = self.db_manager.fetch_all(query, params)

            # إضافة النتائج للجدول
            for item in items:
                created_at = item['created_at'][:10] if item['created_at'] else 'غير محدد'

                quantity = item['quantity']
                if quantity == 0:
                    tag = 'out_of_stock'
                elif quantity <= 5:
                    tag = 'low_stock'
                else:
                    tag = 'normal_stock'

                self.tree.insert('', 'end', values=(
                    item['id'],
                    item['item_name'],
                    item['category'],
                    quantity,
                    f"{item['selling_price']:.2f} ₪",
                    item['barcode'] or 'غير محدد',
                    created_at
                ), tags=(tag,))

            # تحديث العداد
            count = len(items)
            total_value = sum(item['quantity'] * item['selling_price'] for item in items)
            self.count_label.configure(text=f"نتائج البحث: {count} | القيمة: {total_value:.2f} ₪")

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            self.status_label.configure(text="خطأ في البحث")

    def show_low_stock(self):
        """عرض المنتجات ذات المخزون المنخفض"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # جلب المنتجات ذات المخزون المنخفض (أقل من أو يساوي 5)
            items = self.db_manager.fetch_all("SELECT * FROM inventory WHERE quantity <= 5 ORDER BY quantity, item_name")

            # إضافة النتائج للجدول
            for item in items:
                created_at = item['created_at'][:10] if item['created_at'] else 'غير محدد'

                quantity = item['quantity']
                if quantity == 0:
                    tag = 'out_of_stock'
                else:
                    tag = 'low_stock'

                self.tree.insert('', 'end', values=(
                    item['id'],
                    item['item_name'],
                    item['category'],
                    quantity,
                    f"{item['selling_price']:.2f} ₪",
                    item['barcode'] or 'غير محدد',
                    created_at
                ), tags=(tag,))

            # تحديث العداد
            count = len(items)
            self.count_label.configure(text=f"منتجات بمخزون منخفض: {count}")
            self.status_label.configure(text="تم عرض المنتجات ذات المخزون المنخفض")

        except Exception as e:
            print(f"خطأ في عرض المخزون المنخفض: {e}")
            self.status_label.configure(text="خطأ في عرض المخزون المنخفض")

    def on_select(self, event):
        """معالجة اختيار منتج من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_item = {
                    'id': values[0],
                    'item_name': values[1],
                    'category': values[2],
                    'quantity': values[3],
                    'selling_price': float(values[4].replace(' ₪', '')),
                    'barcode': values[5] if values[5] != 'غير محدد' else ''
                }
                self.status_label.configure(text=f"تم اختيار: {values[1]}")

    def add_item(self):
        """إضافة منتج جديد"""
        self.open_item_form()

    def edit_item(self):
        """تعديل منتج محدد"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        self.open_item_form(self.selected_item)

    def delete_item(self):
        """حذف منتج محدد"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج:\n{self.selected_item['item_name']}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                # حذف المنتج من قاعدة البيانات
                self.db_manager.execute_query(
                    "DELETE FROM inventory WHERE id = ?",
                    (self.selected_item['id'],)
                )

                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                self.selected_item = None
                self.load_inventory()

            except Exception as e:
                print(f"خطأ في حذف المنتج: {e}")
                self.status_label.configure(text="فشل في حذف المنتج")

    def update_stock(self):
        """تحديث كمية المخزون"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لتحديث كميته")
            return

        StockUpdateDialog(self, self.selected_item)

    def open_item_form(self, item_data=None):
        """فتح نموذج إضافة/تعديل منتج"""
        ItemForm(self, item_data)

class ItemForm:
    """نموذج إضافة/تعديل منتج"""

    def __init__(self, parent_manager, item_data=None):
        self.parent_manager = parent_manager
        self.item_data = item_data
        self.is_edit_mode = item_data is not None

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تعديل منتج" if self.is_edit_mode else "إضافة منتج جديد")
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_form()

        if self.is_edit_mode:
            self.fill_form_data()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_form(self):
        """إعداد نموذج المنتج"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_text = "✏️ تعديل بيانات المنتج" if self.is_edit_mode else "➕ إضافة منتج جديد"
        title_label = tk.Label(
            title_frame,
            text=title_text,
            bg='white',
            fg='#1f2937',
            font=('Arial', 16, 'bold')
        )
        title_label.pack()

        # نموذج البيانات
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # اسم المنتج
        self.create_field(form_frame, "اسم المنتج:", "item_name_var", 0)

        # الفئة
        self.create_category_field(form_frame, 1)

        # الكمية
        self.create_field(form_frame, "الكمية:", "quantity_var", 2, field_type="number")

        # سعر البيع
        self.create_field(form_frame, "سعر البيع (₪):", "selling_price_var", 3, field_type="number")

        # الباركود
        self.create_field(form_frame, "الباركود:", "barcode_var", 4)

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ" if self.is_edit_mode else "➕ إضافة",
            command=self.save_item,
            bg='#10b981',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def create_field(self, parent, label_text, var_name, row, field_type="text"):
        """إنشاء حقل في النموذج"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(10, 5))

        # المتغير
        var = tk.StringVar()
        setattr(self, var_name, var)

        # حقل الإدخال
        entry = tk.Entry(
            parent,
            textvariable=var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=30
        )
        entry.grid(row=row, column=1, sticky='ew', pady=(10, 5), padx=(10, 0))

        # التحقق من النوع
        if field_type == "number":
            # ربط التحقق من الأرقام
            entry.configure(validate='key', validatecommand=(self.window.register(self.validate_number), '%P'))

        parent.grid_columnconfigure(1, weight=1)

    def create_category_field(self, parent, row):
        """إنشاء حقل الفئة مع قائمة منسدلة"""
        # التسمية
        label = tk.Label(
            parent,
            text="الفئة:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(10, 5))

        # المتغير
        self.category_var = tk.StringVar()

        # قائمة الفئات
        categories = [
            "شاشات", "بطاريات", "إكسسوارات", "كفرات", "شواحن",
            "سماعات", "كابلات", "قطع غيار", "أدوات صيانة", "أخرى"
        ]

        # القائمة المنسدلة
        category_combo = ttk.Combobox(
            parent,
            textvariable=self.category_var,
            values=categories,
            font=('Arial', 11),
            width=28,
            state='normal'
        )
        category_combo.grid(row=row, column=1, sticky='ew', pady=(10, 5), padx=(10, 0))

    def validate_number(self, value):
        """التحقق من صحة الأرقام"""
        if value == "":
            return True
        try:
            float(value)
            return True
        except ValueError:
            return False

    def fill_form_data(self):
        """ملء النموذج ببيانات المنتج للتعديل"""
        if self.item_data:
            self.item_name_var.set(self.item_data['item_name'])
            self.category_var.set(self.item_data['category'])
            self.quantity_var.set(str(self.item_data['quantity']))
            self.selling_price_var.set(str(self.item_data['selling_price']))
            self.barcode_var.set(self.item_data['barcode'])

    def save_item(self):
        """حفظ بيانات المنتج"""
        # جمع البيانات
        item_name = self.item_name_var.get().strip()
        category = self.category_var.get().strip()
        quantity_str = self.quantity_var.get().strip()
        selling_price_str = self.selling_price_var.get().strip()
        barcode = self.barcode_var.get().strip()

        # التحقق من البيانات
        if not item_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
            return

        if not category:
            messagebox.showerror("خطأ", "يرجى اختيار فئة المنتج")
            return

        try:
            quantity = int(quantity_str) if quantity_str else 0
            selling_price = float(selling_price_str) if selling_price_str else 0.0
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للكمية والسعر")
            return

        if quantity < 0:
            messagebox.showerror("خطأ", "الكمية لا يمكن أن تكون سالبة")
            return

        if selling_price < 0:
            messagebox.showerror("خطأ", "السعر لا يمكن أن يكون سالباً")
            return

        try:
            if self.is_edit_mode:
                # تحديث المنتج
                query = """
                    UPDATE inventory
                    SET item_name = ?, category = ?, quantity = ?, selling_price = ?, barcode = ?
                    WHERE id = ?
                """
                params = (item_name, category, quantity, selling_price, barcode or None, self.item_data['id'])
                self.parent_manager.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم تحديث بيانات المنتج بنجاح")
            else:
                # إضافة منتج جديد
                query = """
                    INSERT INTO inventory (item_name, category, quantity, selling_price, barcode, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (item_name, category, quantity, selling_price, barcode or None, datetime.now())
                self.parent_manager.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")

            # تحديث قائمة المخزون
            self.parent_manager.load_inventory()
            self.window.destroy()

        except Exception as e:
            print(f"خطأ في حفظ بيانات المنتج: {e}")
            messagebox.showerror("خطأ", "فشل في حفظ البيانات")

class StockUpdateDialog:
    """نافذة تحديث كمية المخزون"""

    def __init__(self, parent_manager, item_data):
        self.parent_manager = parent_manager
        self.item_data = item_data

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تحديث كمية المخزون")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_dialog()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_dialog(self):
        """إعداد نافذة تحديث المخزون"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="📈 تحديث كمية المخزون",
            bg='white',
            fg='#1f2937',
            font=('Arial', 16, 'bold')
        )
        title_label.pack()

        # معلومات المنتج
        info_frame = tk.Frame(self.window, bg='#f8fafc', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        info_content = tk.Frame(info_frame, bg='#f8fafc')
        info_content.pack(fill=tk.X, padx=15, pady=15)

        # اسم المنتج
        product_label = tk.Label(
            info_content,
            text=f"📦 المنتج: {self.item_data['item_name']}",
            bg='#f8fafc',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        product_label.pack(anchor='w')

        # الكمية الحالية
        current_qty_label = tk.Label(
            info_content,
            text=f"📊 الكمية الحالية: {self.item_data['quantity']}",
            bg='#f8fafc',
            fg='#2563eb',
            font=('Arial', 12, 'bold')
        )
        current_qty_label.pack(anchor='w', pady=(5, 0))

        # نموذج التحديث
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.X, padx=20, pady=20)

        # نوع العملية
        operation_label = tk.Label(
            form_frame,
            text="نوع العملية:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        operation_label.pack(anchor='w')

        self.operation_var = tk.StringVar(value="add")

        operations_frame = tk.Frame(form_frame, bg='white')
        operations_frame.pack(fill=tk.X, pady=(5, 15))

        add_radio = tk.Radiobutton(
            operations_frame,
            text="➕ إضافة للمخزون",
            variable=self.operation_var,
            value="add",
            bg='white',
            fg='#10b981',
            font=('Arial', 11),
            selectcolor='white'
        )
        add_radio.pack(side=tk.LEFT, padx=(0, 20))

        subtract_radio = tk.Radiobutton(
            operations_frame,
            text="➖ خصم من المخزون",
            variable=self.operation_var,
            value="subtract",
            bg='white',
            fg='#ef4444',
            font=('Arial', 11),
            selectcolor='white'
        )
        subtract_radio.pack(side=tk.LEFT)

        # الكمية
        quantity_label = tk.Label(
            form_frame,
            text="الكمية:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        quantity_label.pack(anchor='w')

        self.quantity_var = tk.StringVar()
        quantity_entry = tk.Entry(
            form_frame,
            textvariable=self.quantity_var,
            font=('Arial', 12),
            relief='solid',
            bd=1,
            width=20,
            validate='key',
            validatecommand=(self.window.register(self.validate_number), '%P')
        )
        quantity_entry.pack(anchor='w', pady=(5, 15))
        quantity_entry.focus()

        # ملاحظات
        notes_label = tk.Label(
            form_frame,
            text="ملاحظات (اختياري):",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        notes_label.pack(anchor='w')

        self.notes_text = tk.Text(
            form_frame,
            height=3,
            width=40,
            font=('Arial', 10),
            relief='solid',
            bd=1
        )
        self.notes_text.pack(anchor='w', pady=(5, 0))

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر التحديث
        update_btn = tk.Button(
            buttons_frame,
            text="📈 تحديث المخزون",
            command=self.update_stock,
            bg='#2563eb',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        update_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#6b7280',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def validate_number(self, value):
        """التحقق من صحة الأرقام"""
        if value == "":
            return True
        try:
            int(value)
            return True
        except ValueError:
            return False

    def update_stock(self):
        """تحديث كمية المخزون"""
        quantity_str = self.quantity_var.get().strip()
        operation = self.operation_var.get()
        notes = self.notes_text.get('1.0', tk.END).strip()

        # التحقق من البيانات
        if not quantity_str:
            messagebox.showerror("خطأ", "يرجى إدخال الكمية")
            return

        try:
            quantity = int(quantity_str)
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للكمية")
            return

        if quantity <= 0:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
            return

        # حساب الكمية الجديدة
        current_quantity = self.item_data['quantity']

        if operation == "add":
            new_quantity = current_quantity + quantity
            operation_text = "إضافة"
        else:  # subtract
            new_quantity = current_quantity - quantity
            operation_text = "خصم"

            if new_quantity < 0:
                result = messagebox.askyesno(
                    "تحذير",
                    f"ستصبح الكمية سالبة ({new_quantity})\nهل تريد المتابعة؟"
                )
                if not result:
                    return

        # تأكيد العملية
        confirm_msg = f"""
تأكيد تحديث المخزون:

المنتج: {self.item_data['item_name']}
العملية: {operation_text} {quantity}
الكمية الحالية: {current_quantity}
الكمية الجديدة: {new_quantity}

هل تريد المتابعة؟
        """

        result = messagebox.askyesno("تأكيد التحديث", confirm_msg)
        if not result:
            return

        try:
            # تحديث الكمية في قاعدة البيانات
            self.parent_manager.db_manager.execute_query(
                "UPDATE inventory SET quantity = ? WHERE id = ?",
                (new_quantity, self.item_data['id'])
            )

            # إضافة سجل في تاريخ المخزون (إذا كان الجدول موجود)
            try:
                stock_history_query = """
                    INSERT INTO stock_history (item_id, operation_type, quantity_change,
                                             old_quantity, new_quantity, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                self.parent_manager.db_manager.execute_query(
                    stock_history_query,
                    (self.item_data['id'], operation, quantity if operation == "add" else -quantity,
                     current_quantity, new_quantity, notes or None, datetime.now())
                )
            except:
                # إذا لم يكن جدول التاريخ موجود، تجاهل الخطأ
                pass

            messagebox.showinfo("نجح", f"تم تحديث المخزون بنجاح\nالكمية الجديدة: {new_quantity}")

            # تحديث قائمة المخزون
            self.parent_manager.load_inventory()
            self.window.destroy()

        except Exception as e:
            print(f"خطأ في تحديث المخزون: {e}")
            messagebox.showerror("خطأ", "فشل في تحديث المخزون")