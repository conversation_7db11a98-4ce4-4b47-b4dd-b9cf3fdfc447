#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ حوار المخزون - Inventory Dialogs
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import random
import string

class NewInventoryItemDialog:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة قطعة جديدة للمخزون")
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل الموردين
        self.load_suppliers()
        
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إضافة قطعة جديدة للمخزون", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إطار بيانات القطعة
        item_frame = ttk.LabelFrame(main_frame, text="بيانات القطعة", padding=15)
        item_frame.pack(fill=tk.X, pady=(0, 15))
        
        # اسم القطعة
        ttk.Label(item_frame, text="اسم القطعة:").grid(row=0, column=0, sticky="w", pady=5)
        self.item_name_var = tk.StringVar()
        ttk.Entry(item_frame, textvariable=self.item_name_var, width=40).grid(row=0, column=1, sticky="ew", pady=5)
        
        # الفئة
        ttk.Label(item_frame, text="الفئة:").grid(row=1, column=0, sticky="w", pady=5)
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(item_frame, textvariable=self.category_var, width=37)
        category_combo['values'] = ('شاشات', 'بطاريات', 'كاميرات', 'سماعات', 'مكبرات صوت', 
                                   'أزرار', 'كابلات', 'شواحن', 'حافظات', 'أخرى')
        category_combo.grid(row=1, column=1, sticky="ew", pady=5)
        
        # الباركود
        barcode_frame = ttk.Frame(item_frame)
        barcode_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=5)
        
        ttk.Label(barcode_frame, text="الباركود:").pack(side=tk.LEFT)
        self.barcode_var = tk.StringVar()
        barcode_entry = ttk.Entry(barcode_frame, textvariable=self.barcode_var, width=30)
        barcode_entry.pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(barcode_frame, text="توليد", 
                  command=self.generate_barcode).pack(side=tk.LEFT)
        
        # المورد
        ttk.Label(item_frame, text="المورد:").grid(row=3, column=0, sticky="w", pady=5)
        self.supplier_combo = ttk.Combobox(item_frame, width=37, state="readonly")
        self.supplier_combo.grid(row=3, column=1, sticky="ew", pady=5)
        
        # تكوين الأعمدة
        item_frame.columnconfigure(1, weight=1)
        
        # إطار الأسعار والكميات
        price_frame = ttk.LabelFrame(main_frame, text="الأسعار والكميات", padding=15)
        price_frame.pack(fill=tk.X, pady=(0, 15))
        
        # سعر الشراء
        ttk.Label(price_frame, text="سعر الشراء (₪):").grid(row=0, column=0, sticky="w", pady=5)
        self.purchase_price_var = tk.StringVar(value="0.00")
        ttk.Entry(price_frame, textvariable=self.purchase_price_var, width=20).grid(row=0, column=1, sticky="ew", pady=5)
        
        # سعر البيع
        ttk.Label(price_frame, text="سعر البيع (₪):").grid(row=1, column=0, sticky="w", pady=5)
        self.selling_price_var = tk.StringVar(value="0.00")
        selling_price_entry = ttk.Entry(price_frame, textvariable=self.selling_price_var, width=20)
        selling_price_entry.grid(row=1, column=1, sticky="ew", pady=5)
        
        # زر حساب سعر البيع تلقائياً
        ttk.Button(price_frame, text="حساب تلقائي (+30%)", 
                  command=self.calculate_selling_price).grid(row=1, column=2, padx=(10, 0), pady=5)
        
        # الكمية
        ttk.Label(price_frame, text="الكمية:").grid(row=2, column=0, sticky="w", pady=5)
        self.quantity_var = tk.StringVar(value="1")
        ttk.Entry(price_frame, textvariable=self.quantity_var, width=20).grid(row=2, column=1, sticky="ew", pady=5)
        
        # الحد الأدنى للكمية
        ttk.Label(price_frame, text="الحد الأدنى:").grid(row=3, column=0, sticky="w", pady=5)
        self.min_quantity_var = tk.StringVar(value="5")
        ttk.Entry(price_frame, textvariable=self.min_quantity_var, width=20).grid(row=3, column=1, sticky="ew", pady=5)
        
        # الموقع
        ttk.Label(price_frame, text="الموقع:").grid(row=4, column=0, sticky="w", pady=5)
        self.location_var = tk.StringVar()
        location_combo = ttk.Combobox(price_frame, textvariable=self.location_var, width=17)
        location_combo['values'] = ('الرف الأول', 'الرف الثاني', 'الرف الثالث', 'المخزن', 'العرض')
        location_combo.grid(row=4, column=1, sticky="ew", pady=5)
        
        # تكوين الأعمدة
        price_frame.columnconfigure(1, weight=1)
        
        # ملاحظات
        notes_frame = ttk.LabelFrame(main_frame, text="ملاحظات", padding=15)
        notes_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.notes_text = tk.Text(notes_frame, width=50, height=4)
        self.notes_text.pack(fill=tk.X)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="حفظ", 
                  command=self.save).pack(side=tk.RIGHT)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_query("SELECT id, name FROM suppliers ORDER BY name")
            supplier_list = [f"{supplier['name']}" for supplier in suppliers]
            self.supplier_combo['values'] = supplier_list
            self.suppliers_data = suppliers
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")
            self.supplier_combo['values'] = []
            self.suppliers_data = []
    
    def generate_barcode(self):
        """توليد باركود عشوائي"""
        barcode = ''.join(random.choices(string.digits, k=12))
        self.barcode_var.set(barcode)
    
    def calculate_selling_price(self):
        """حساب سعر البيع تلقائياً (سعر الشراء + 30%)"""
        try:
            purchase_price = float(self.purchase_price_var.get())
            selling_price = purchase_price * 1.3  # زيادة 30%
            self.selling_price_var.set(f"{selling_price:.2f}")
        except ValueError:
            messagebox.showwarning("تحذير", "يرجى إدخال سعر شراء صحيح أولاً!")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.item_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم القطعة!")
            return False
        
        if not self.category_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار فئة القطعة!")
            return False
        
        # التحقق من الأسعار
        try:
            purchase_price = float(self.purchase_price_var.get())
            selling_price = float(self.selling_price_var.get())
            if purchase_price < 0 or selling_price < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أسعار صحيحة!")
            return False
        
        # التحقق من الكميات
        try:
            quantity = int(self.quantity_var.get())
            min_quantity = int(self.min_quantity_var.get())
            if quantity < 0 or min_quantity < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كميات صحيحة!")
            return False
        
        return True
    
    def save(self):
        """حفظ البيانات"""
        if not self.validate_data():
            return
        
        try:
            # الحصول على معرف المورد
            supplier_id = None
            if self.supplier_combo.get():
                selected_index = self.supplier_combo.current()
                if selected_index >= 0:
                    supplier_id = self.suppliers_data[selected_index]['id']
            
            # إضافة القطعة للمخزون
            inventory_query = """
            INSERT INTO inventory (item_name, barcode, category, supplier_id, 
                                 purchase_price, selling_price, quantity, min_quantity, 
                                 location, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            if self.db_manager.execute_query(inventory_query, (
                self.item_name_var.get().strip(),
                self.barcode_var.get().strip() or None,
                self.category_var.get().strip(),
                supplier_id,
                float(self.purchase_price_var.get()),
                float(self.selling_price_var.get()),
                int(self.quantity_var.get()),
                int(self.min_quantity_var.get()),
                self.location_var.get().strip() or None,
                self.notes_text.get("1.0", tk.END).strip() or None
            )):
                messagebox.showinfo("نجح", "تم إضافة القطعة للمخزون بنجاح!")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة القطعة للمخزون!")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class InventoryManagementWindow:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المخزون")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.refresh_inventory()
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان وأدوات التحكم
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="إدارة المخزون", 
                               font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # أزرار التحكم
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side=tk.RIGHT)
        
        ttk.Button(controls_frame, text="إضافة قطعة", 
                  command=self.add_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تعديل", 
                  command=self.edit_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="حذف", 
                  command=self.delete_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="تحديث", 
                  command=self.refresh_inventory).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث والفلترة
        search_frame = ttk.LabelFrame(main_frame, text="البحث والفلترة", padding=10)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # فلترة حسب الفئة
        ttk.Label(search_frame, text="الفئة:").grid(row=0, column=2, sticky="w", padx=(20, 5))
        self.category_filter_var = tk.StringVar()
        category_filter = ttk.Combobox(search_frame, textvariable=self.category_filter_var, width=20)
        category_filter['values'] = ('الكل', 'شاشات', 'بطاريات', 'كاميرات', 'سماعات', 
                                    'مكبرات صوت', 'أزرار', 'كابلات', 'شواحن', 'حافظات', 'أخرى')
        category_filter.set('الكل')
        category_filter.grid(row=0, column=3, padx=5)
        category_filter.bind('<<ComboboxSelected>>', self.on_filter)
        
        # فلترة المخزون المنخفض
        self.low_stock_var = tk.BooleanVar()
        ttk.Checkbutton(search_frame, text="المخزون المنخفض فقط", 
                       variable=self.low_stock_var, 
                       command=self.on_filter).grid(row=0, column=4, padx=(20, 0))
        
        # جدول المخزون
        columns = ("ID", "اسم القطعة", "الفئة", "الباركود", "الكمية", "الحد الأدنى", 
                  "سعر الشراء", "سعر البيع", "الموقع", "المورد")
        
        self.inventory_tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=20)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            if col == "اسم القطعة":
                self.inventory_tree.column(col, width=200)
            elif col in ["سعر الشراء", "سعر البيع"]:
                self.inventory_tree.column(col, width=100)
            elif col == "الكمية":
                self.inventory_tree.column(col, width=80)
            else:
                self.inventory_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        scrollbar_h = ttk.Scrollbar(main_frame, orient=tk.HORIZONTAL, command=self.inventory_tree.xview)
        self.inventory_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # تخطيط الجدول وأشرطة التمرير
        self.inventory_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_v.grid(row=0, column=1, sticky="ns")
        scrollbar_h.grid(row=1, column=0, sticky="ew")
        
        # إعداد الشبكة
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # إطار للجدول
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        self.inventory_tree.grid(row=0, column=0, sticky="nsew", in_=table_frame)
        scrollbar_v.grid(row=0, column=1, sticky="ns", in_=table_frame)
        scrollbar_h.grid(row=1, column=0, sticky="ew", in_=table_frame)
        
        # ربط النقر المزدوج
        self.inventory_tree.bind('<Double-1>', lambda e: self.edit_item())
    
    def refresh_inventory(self):
        """تحديث قائمة المخزون"""
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # جلب البيانات الجديدة
        query = """
        SELECT i.*, s.name as supplier_name
        FROM inventory i
        LEFT JOIN suppliers s ON i.supplier_id = s.id
        ORDER BY i.item_name
        """
        
        try:
            items = self.db_manager.fetch_query(query)
            for item in items:
                # تحديد لون الصف حسب المخزون
                tags = []
                if item['quantity'] <= item['min_quantity']:
                    tags = ['low_stock']
                
                self.inventory_tree.insert("", "end", values=(
                    item['id'],
                    item['item_name'],
                    item['category'] or '',
                    item['barcode'] or '',
                    item['quantity'],
                    item['min_quantity'],
                    f"{item['purchase_price']:.2f}",
                    f"{item['selling_price']:.2f}",
                    item['location'] or '',
                    item['supplier_name'] or ''
                ), tags=tags)
            
            # تعيين ألوان للمخزون المنخفض
            self.inventory_tree.tag_configure('low_stock', background='#ffcccc')
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المخزون: {e}")
    
    def on_search(self, event=None):
        """البحث في المخزون"""
        self.apply_filters()
    
    def on_filter(self, event=None):
        """تطبيق الفلاتر"""
        self.apply_filters()
    
    def apply_filters(self):
        """تطبيق البحث والفلاتر"""
        search_term = self.search_var.get().lower()
        category_filter = self.category_filter_var.get()
        low_stock_only = self.low_stock_var.get()
        
        # إخفاء جميع العناصر أولاً
        for item in self.inventory_tree.get_children():
            self.inventory_tree.detach(item)
        
        # إعادة إظهار العناصر المطابقة للفلاتر
        for item in self.inventory_tree.get_children():
            values = self.inventory_tree.item(item)['values']
            
            # فلتر البحث
            if search_term and search_term not in values[1].lower():  # البحث في اسم القطعة
                continue
            
            # فلتر الفئة
            if category_filter != 'الكل' and values[2] != category_filter:
                continue
            
            # فلتر المخزون المنخفض
            if low_stock_only and int(values[4]) > int(values[5]):  # الكمية > الحد الأدنى
                continue
            
            # إعادة إدراج العنصر
            self.inventory_tree.reattach(item, '', 'end')
    
    def add_item(self):
        """إضافة قطعة جديدة"""
        dialog = NewInventoryItemDialog(self.window, self.db_manager)
        if dialog.show():
            self.refresh_inventory()
    
    def edit_item(self):
        """تعديل قطعة"""
        selected_item = self.inventory_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار قطعة من القائمة!")
            return
        
        messagebox.showinfo("قيد التطوير", "نافذة تعديل القطعة قيد التطوير")
    
    def delete_item(self):
        """حذف قطعة"""
        selected_item = self.inventory_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار قطعة من القائمة!")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه القطعة؟"):
            try:
                item_id = self.inventory_tree.item(selected_item[0])['values'][0]
                if self.db_manager.execute_query("DELETE FROM inventory WHERE id = ?", (item_id,)):
                    messagebox.showinfo("نجح", "تم حذف القطعة بنجاح!")
                    self.refresh_inventory()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف القطعة!")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def show(self):
        """عرض النافذة"""
        self.window.mainloop()
