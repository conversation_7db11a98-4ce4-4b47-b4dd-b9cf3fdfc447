#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة العملاء مع عمليات CRUD
Customers Manager with CRUD Operations

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime

class CustomersManager:
    """مدير العملاء مع عمليات الإضافة والتعديل والحذف"""
    
    def __init__(self, parent_frame, db_manager):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.selected_customer = None
        
        # ألوان النظام
        self.colors = {
            'primary': '#2563eb',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'bg_light': '#f8fafc',
            'text_dark': '#1f2937'
        }
        
        self.setup_ui()
        self.load_customers()
    
    def setup_ui(self):
        """إعداد واجهة إدارة العملاء"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(
            title_frame,
            text="👥 إدارة العملاء",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        title_label.pack(side=tk.LEFT)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(title_frame, bg='white')
        buttons_frame.pack(side=tk.RIGHT)
        
        # زر إضافة عميل جديد
        add_btn = tk.Button(
            buttons_frame,
            text="➕ إضافة عميل",
            command=self.add_customer,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تعديل عميل
        edit_btn = tk.Button(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_customer,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر حذف عميل
        delete_btn = tk.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_customer,
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تحديث
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.load_customers,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.LEFT)
        
        # شريط البحث
        search_frame = tk.Frame(self.parent_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=20, pady=10)
        
        search_label = tk.Label(
            search_frame,
            text="🔍 البحث:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Arial', 12),
            relief='solid',
            bd=1,
            width=30
        )
        search_entry.pack(side=tk.LEFT)
        
        # جدول العملاء
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء الجدول
        columns = ("ID", "الاسم", "الهاتف", "البريد الإلكتروني", "العنوان", "تاريخ الإضافة")
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين العناوين وعرض الأعمدة
        column_widths = [50, 150, 120, 180, 150, 120]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor='center')
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.edit_customer)
        
        # شريط الحالة
        status_frame = tk.Frame(self.parent_frame, bg='white')
        status_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 10)
        )
        self.status_label.pack(side=tk.LEFT)
        
        self.count_label = tk.Label(
            status_frame,
            text="",
            bg='white',
            fg=self.colors['primary'],
            font=('Arial', 10, 'bold')
        )
        self.count_label.pack(side=tk.RIGHT)
    
    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب العملاء من قاعدة البيانات
            customers = self.db_manager.fetch_all("SELECT * FROM customers ORDER BY name")
            
            # إضافة البيانات للجدول
            for customer in customers:
                # تنسيق التاريخ
                created_at = customer['created_at'][:10] if customer['created_at'] else 'غير محدد'
                
                self.tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'],
                    customer['email'] or 'غير محدد',
                    customer['address'] or 'غير محدد',
                    created_at
                ))
            
            # تحديث عداد العملاء
            count = len(customers)
            self.count_label.configure(text=f"إجمالي العملاء: {count}")
            self.status_label.configure(text="تم تحميل البيانات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العملاء: {str(e)}")
            self.status_label.configure(text="خطأ في تحميل البيانات")
    
    def on_search(self, *args):
        """البحث في العملاء"""
        search_term = self.search_var.get().strip()
        
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            if search_term:
                # البحث في قاعدة البيانات
                query = """
                    SELECT * FROM customers 
                    WHERE name LIKE ? OR phone LIKE ? OR email LIKE ? OR address LIKE ?
                    ORDER BY name
                """
                search_pattern = f'%{search_term}%'
                customers = self.db_manager.fetch_all(query, (search_pattern, search_pattern, search_pattern, search_pattern))
            else:
                # عرض جميع العملاء
                customers = self.db_manager.fetch_all("SELECT * FROM customers ORDER BY name")
            
            # إضافة النتائج للجدول
            for customer in customers:
                created_at = customer['created_at'][:10] if customer['created_at'] else 'غير محدد'
                
                self.tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'],
                    customer['email'] or 'غير محدد',
                    customer['address'] or 'غير محدد',
                    created_at
                ))
            
            # تحديث العداد
            count = len(customers)
            self.count_label.configure(text=f"نتائج البحث: {count}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def on_select(self, event):
        """معالجة اختيار عميل من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_customer = {
                    'id': values[0],
                    'name': values[1],
                    'phone': values[2],
                    'email': values[3] if values[3] != 'غير محدد' else '',
                    'address': values[4] if values[4] != 'غير محدد' else ''
                }
                self.status_label.configure(text=f"تم اختيار: {values[1]}")
    
    def add_customer(self):
        """إضافة عميل جديد"""
        self.open_customer_form()
    
    def edit_customer(self):
        """تعديل عميل محدد"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return
        
        self.open_customer_form(self.selected_customer)
    
    def delete_customer(self):
        """حذف عميل محدد"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل:\n{self.selected_customer['name']}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )
        
        if result:
            try:
                # حذف العميل من قاعدة البيانات
                self.db_manager.execute_query(
                    "DELETE FROM customers WHERE id = ?",
                    (self.selected_customer['id'],)
                )
                
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.selected_customer = None
                self.load_customers()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف العميل: {str(e)}")
    
    def open_customer_form(self, customer_data=None):
        """فتح نموذج إضافة/تعديل عميل"""
        CustomerForm(self, customer_data)

class CustomerForm:
    """نموذج إضافة/تعديل عميل"""
    
    def __init__(self, parent_manager, customer_data=None):
        self.parent_manager = parent_manager
        self.customer_data = customer_data
        self.is_edit_mode = customer_data is not None
        
        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تعديل عميل" if self.is_edit_mode else "إضافة عميل جديد")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.configure(bg='white')
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()
        
        self.setup_form()
        
        if self.is_edit_mode:
            self.fill_form_data()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_form(self):
        """إعداد نموذج العميل"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_text = "✏️ تعديل بيانات العميل" if self.is_edit_mode else "➕ إضافة عميل جديد"
        title_label = tk.Label(
            title_frame,
            text=title_text,
            bg='white',
            fg='#1f2937',
            font=('Arial', 16, 'bold')
        )
        title_label.pack()
        
        # نموذج البيانات
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # اسم العميل
        self.create_field(form_frame, "الاسم الكامل:", "name_var", 0)
        
        # رقم الهاتف
        self.create_field(form_frame, "رقم الهاتف:", "phone_var", 1)
        
        # البريد الإلكتروني
        self.create_field(form_frame, "البريد الإلكتروني:", "email_var", 2)
        
        # العنوان
        self.create_field(form_frame, "العنوان:", "address_var", 3, is_text=True)
        
        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ" if self.is_edit_mode else "➕ إضافة",
            command=self.save_customer,
            bg='#10b981',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)
    
    def create_field(self, parent, label_text, var_name, row, is_text=False):
        """إنشاء حقل في النموذج"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(10, 5))
        
        # المتغير
        var = tk.StringVar()
        setattr(self, var_name, var)
        
        if is_text:
            # حقل نص متعدد الأسطر
            text_widget = tk.Text(
                parent,
                height=3,
                width=40,
                font=('Arial', 11),
                relief='solid',
                bd=1
            )
            text_widget.grid(row=row, column=1, sticky='ew', pady=(10, 5), padx=(10, 0))
            setattr(self, var_name.replace('_var', '_widget'), text_widget)
        else:
            # حقل إدخال عادي
            entry = tk.Entry(
                parent,
                textvariable=var,
                font=('Arial', 11),
                relief='solid',
                bd=1,
                width=30
            )
            entry.grid(row=row, column=1, sticky='ew', pady=(10, 5), padx=(10, 0))
        
        parent.grid_columnconfigure(1, weight=1)
    
    def fill_form_data(self):
        """ملء النموذج ببيانات العميل للتعديل"""
        if self.customer_data:
            self.name_var.set(self.customer_data['name'])
            self.phone_var.set(self.customer_data['phone'])
            self.email_var.set(self.customer_data['email'])
            self.address_widget.insert('1.0', self.customer_data['address'])
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # جمع البيانات
        name = self.name_var.get().strip()
        phone = self.phone_var.get().strip()
        email = self.email_var.get().strip()
        address = self.address_widget.get('1.0', tk.END).strip()
        
        # التحقق من البيانات
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return
        
        if not phone:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الهاتف")
            return
        
        try:
            if self.is_edit_mode:
                # تحديث العميل
                query = """
                    UPDATE customers 
                    SET name = ?, phone = ?, email = ?, address = ?
                    WHERE id = ?
                """
                params = (name, phone, email or None, address or None, self.customer_data['id'])
                self.parent_manager.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
            else:
                # إضافة عميل جديد
                query = """
                    INSERT INTO customers (name, phone, email, address, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """
                params = (name, phone, email or None, address or None, datetime.now())
                self.parent_manager.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            
            # تحديث قائمة العملاء
            self.parent_manager.load_customers()
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
