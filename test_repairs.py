#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الصيانة
Test Repairs Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد المكونات المطلوبة
from phone_doctor_working import DatabaseManager
from ui.repairs_manager import RepairsManager

def test_repairs_manager():
    """اختبار مدير الصيانة"""
    print("🧪 بدء اختبار نظام إدارة الصيانة...")
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار إدارة الصيانة")
    root.geometry("1200x800")
    root.configure(bg='white')
    
    try:
        # إنشاء مدير قاعدة البيانات
        print("📊 إنشاء مدير قاعدة البيانات...")
        db_manager = DatabaseManager()
        
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return
        
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # إنشاء إطار المحتوى
        content_frame = tk.Frame(root, bg='white')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء مدير الصيانة
        print("🔧 إنشاء مدير الصيانة...")
        current_user = {'username': 'admin', 'full_name': 'المدير'}
        repairs_manager = RepairsManager(content_frame, db_manager, current_user)
        
        print("✅ تم إنشاء مدير الصيانة بنجاح")
        
        # تشغيل النافذة
        print("🚀 تشغيل واجهة الاختبار...")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_repairs_manager()
