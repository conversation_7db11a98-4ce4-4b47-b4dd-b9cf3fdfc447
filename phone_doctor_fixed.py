#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor - نظام إدارة محلات صيانة الهواتف (النسخة المحسنة)
تطبيق شامل لإدارة محلات صيانة الهواتف مع معالجة شاملة للأخطاء

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
import sqlite3
from datetime import datetime, timedelta
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ErrorHandler:
    """معالج الأخطاء المركزي"""

    @staticmethod
    def handle_error(error, context="عملية غير محددة"):
        """معالجة الأخطاء مع عرض رسائل واضحة"""
        error_msg = f"خطأ في {context}:\n{str(error)}"
        print(f"❌ {error_msg}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        messagebox.showerror("خطأ في النظام", error_msg)
        return False

    @staticmethod
    def safe_execute(func, context="عملية", default_return=None):
        """تنفيذ آمن للدوال مع معالجة الأخطاء"""
        try:
            return func()
        except Exception as e:
            ErrorHandler.handle_error(e, context)
            return default_return

class DatabaseManager:
    """مدير قاعدة البيانات المحسن"""

    def __init__(self, db_path="phone_doctor.db"):
        self.db_path = db_path
        self.connection = None

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            ErrorHandler.handle_error(e, "الاتصال بقاعدة البيانات")
            return False

    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            if not self.connection:
                if not self.connect():
                    return None

            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            self.connection.commit()
            return cursor
        except Exception as e:
            ErrorHandler.handle_error(e, f"تنفيذ الاستعلام: {query[:50]}...")
            return None

    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchall()
        return []

    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchone()
        return None

    def initialize_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        if not self.connect():
            return False

        tables = {
            'customers': '''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT NOT NULL,
                    email TEXT,
                    address TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'inventory': '''
                CREATE TABLE IF NOT EXISTS inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    min_quantity INTEGER DEFAULT 5,
                    purchase_price REAL DEFAULT 0,
                    selling_price REAL DEFAULT 0,
                    barcode TEXT,
                    supplier_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'suppliers': '''
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    company TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    balance REAL DEFAULT 0,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'repairs': '''
                CREATE TABLE IF NOT EXISTS repairs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    device_type TEXT NOT NULL,
                    device_model TEXT,
                    device_imei TEXT,
                    problem_description TEXT NOT NULL,
                    repair_status TEXT DEFAULT 'قيد الانتظار',
                    technician_notes TEXT,
                    estimated_cost REAL DEFAULT 0,
                    actual_cost REAL DEFAULT 0,
                    date_received DATE DEFAULT CURRENT_DATE,
                    date_completed DATE,
                    date_delivered DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''',
            'sales': '''
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    item_id INTEGER,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    total_amount REAL NOT NULL,
                    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    payment_method TEXT DEFAULT 'نقدي',
                    notes TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (item_id) REFERENCES inventory (id)
                )
            ''',
            'users': '''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    permissions TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''
        }

        try:
            for table_name, table_sql in tables.items():
                self.execute_query(table_sql)

            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            ErrorHandler.handle_error(e, "إنشاء قاعدة البيانات")
            return False

class AuthManager:
    """مدير المصادقة"""

    def __init__(self):
        self.db = DatabaseManager()
        self.current_user = None
        self.setup_default_users()

    def setup_default_users(self):
        """إنشاء المستخدمين الافتراضيين"""
        try:
            # التحقق من وجود المستخدمين
            existing_users = self.db.fetch_all("SELECT username FROM users")
            if existing_users:
                return

            # إنشاء المدير
            self.db.execute_query('''
                INSERT INTO users (username, password_hash, full_name, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', 'admin123', 'مدير النظام', 'admin', 'all'))

            # إنشاء موظف المبيعات
            self.db.execute_query('''
                INSERT INTO users (username, password_hash, full_name, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            ''', ('sales', 'sales123', 'موظف المبيعات', 'sales', 'sales,repairs'))

            print("✅ تم إنشاء المستخدمين الافتراضيين")
        except Exception as e:
            ErrorHandler.handle_error(e, "إنشاء المستخدمين الافتراضيين")

    def authenticate(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            user = self.db.fetch_one(
                "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
                (username, password)
            )

            if user:
                self.current_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role'],
                    'permissions': user['permissions'].split(',') if user['permissions'] else []
                }
                return True
            return False
        except Exception as e:
            ErrorHandler.handle_error(e, "التحقق من المصادقة")
            return False

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        if not self.current_user:
            return False
        return (self.current_user['role'] == 'admin' or
                permission in self.current_user['permissions'] or
                'all' in self.current_user['permissions'])

class SimpleStyles:
    """أنماط بسيطة وآمنة"""

    def __init__(self):
        self.colors = {
            'primary': '#2563eb',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'bg_light': '#f8fafc',
            'bg_dark': '#1f2937',
            'text_dark': '#111827',
            'text_light': '#6b7280',
            'border': '#d1d5db'
        }

        self.fonts = {
            'title': ('Arial', 16, 'bold'),
            'heading': ('Arial', 14, 'bold'),
            'body': ('Arial', 12),
            'small': ('Arial', 10)
        }

    def get_button_style(self, variant='primary'):
        """الحصول على نمط الزر"""
        styles = {
            'primary': {
                'bg': self.colors['primary'],
                'fg': 'white',
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2'
            },
            'success': {
                'bg': self.colors['success'],
                'fg': 'white',
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2'
            },
            'danger': {
                'bg': self.colors['danger'],
                'fg': 'white',
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2'
            }
        }
        return styles.get(variant, styles['primary'])

class FixedLoginWindow:
    """نافذة تسجيل دخول بتصميم الذكاء الاصطناعي"""

    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback

        # ألوان الذكاء الاصطناعي
        self.ai_colors = {
            'bg_primary': '#0a0a0a',      # أسود عميق
            'bg_card': '#1a1a1a',         # رمادي داكن
            'bg_input': '#2a2a2a',        # رمادي متوسط
            'accent': '#00d4ff',          # أزرق سيان
            'accent_hover': '#00b8e6',    # أزرق سيان داكن
            'success': '#00ff88',         # أخضر نيون
            'danger': '#ff4757',          # أحمر نيون
            'text_primary': '#ffffff',    # أبيض
            'text_secondary': '#a0a0a0',  # رمادي فاتح
            'border': '#333333',          # رمادي حدود
            'glow': '#00d4ff'             # توهج أزرق
        }

        # إنشاء النافذة
        self.root = tk.Tk()
        self.root.title("🤖 AI Login - Phone Doctor")
        self.root.geometry("500x700")
        self.root.resizable(False, False)
        self.root.configure(bg=self.ai_colors['bg_primary'])

        # توسيط النافذة
        self.center_window()

        # منع إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.setup_ui()

        # التركيز على حقل اسم المستخدم
        self.root.after(100, lambda: self.username_entry.focus())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول بتصميم AI"""

        # الإطار الرئيسي
        main_frame = tk.Frame(
            self.root,
            bg=self.ai_colors['bg_primary']
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # رأس التطبيق مع تأثير AI
        header_frame = tk.Frame(
            main_frame,
            bg=self.ai_colors['bg_primary']
        )
        header_frame.pack(fill=tk.X, pady=(0, 30))

        # أيقونة AI متحركة
        ai_icon = tk.Label(
            header_frame,
            text="🤖",
            bg=self.ai_colors['bg_primary'],
            fg=self.ai_colors['accent'],
            font=('Segoe UI Emoji', 48)
        )
        ai_icon.pack(pady=(0, 10))

        # اسم التطبيق مع تأثير مستقبلي
        title_label = tk.Label(
            header_frame,
            text="PHONE DOCTOR AI",
            bg=self.ai_colors['bg_primary'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 24, 'bold')
        )
        title_label.pack(pady=(0, 5))

        # خط فاصل مضيء
        separator = tk.Frame(
            header_frame,
            bg=self.ai_colors['accent'],
            height=2
        )
        separator.pack(fill=tk.X, pady=(10, 0))

        # وصف النظام
        desc_label = tk.Label(
            header_frame,
            text="⚡ نظام إدارة ذكي لمحلات صيانة الهواتف ⚡",
            bg=self.ai_colors['bg_primary'],
            fg=self.ai_colors['text_secondary'],
            font=('Segoe UI', 12)
        )
        desc_label.pack(pady=(15, 0))

        # بطاقة تسجيل الدخول مع تأثير الزجاج
        login_card = tk.Frame(
            main_frame,
            bg=self.ai_colors['bg_card'],
            relief='flat',
            bd=2,
            highlightbackground=self.ai_colors['border'],
            highlightthickness=1
        )
        login_card.pack(fill=tk.X, pady=(20, 0))

        # محتوى البطاقة
        content_frame = tk.Frame(
            login_card,
            bg=self.ai_colors['bg_card']
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # عنوان تسجيل الدخول
        login_title = tk.Label(
            content_frame,
            text="🔐 SYSTEM ACCESS",
            bg=self.ai_colors['bg_card'],
            fg=self.ai_colors['accent'],
            font=('Consolas', 16, 'bold')
        )
        login_title.pack(pady=(0, 25))

        # حقل اسم المستخدم
        username_frame = tk.Frame(
            content_frame,
            bg=self.ai_colors['bg_card']
        )
        username_frame.pack(fill=tk.X, pady=(0, 20))

        username_label = tk.Label(
            username_frame,
            text="👤 USER ID:",
            bg=self.ai_colors['bg_card'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 12, 'bold')
        )
        username_label.pack(anchor='w', pady=(0, 8))

        self.username_entry = tk.Entry(
            username_frame,
            bg=self.ai_colors['bg_input'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 14),
            relief='flat',
            bd=0,
            insertbackground=self.ai_colors['accent'],
            selectbackground=self.ai_colors['accent'],
            selectforeground=self.ai_colors['bg_primary']
        )
        self.username_entry.pack(fill=tk.X, ipady=12)

        # إطار حول حقل اسم المستخدم
        username_border = tk.Frame(
            username_frame,
            bg=self.ai_colors['border'],
            height=1
        )
        username_border.pack(fill=tk.X, pady=(2, 0))

        # حقل كلمة المرور
        password_frame = tk.Frame(
            content_frame,
            bg=self.ai_colors['bg_card']
        )
        password_frame.pack(fill=tk.X, pady=(0, 20))

        password_label = tk.Label(
            password_frame,
            text="🔑 PASSWORD:",
            bg=self.ai_colors['bg_card'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 12, 'bold')
        )
        password_label.pack(anchor='w', pady=(0, 8))

        self.password_entry = tk.Entry(
            password_frame,
            bg=self.ai_colors['bg_input'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 14),
            relief='flat',
            bd=0,
            show="●",
            insertbackground=self.ai_colors['accent'],
            selectbackground=self.ai_colors['accent'],
            selectforeground=self.ai_colors['bg_primary']
        )
        self.password_entry.pack(fill=tk.X, ipady=12)

        # إطار حول حقل كلمة المرور
        password_border = tk.Frame(
            password_frame,
            bg=self.ai_colors['border'],
            height=1
        )
        password_border.pack(fill=tk.X, pady=(2, 0))

        # خيار إظهار كلمة المرور
        show_frame = tk.Frame(
            content_frame,
            bg=self.ai_colors['bg_card']
        )
        show_frame.pack(fill=tk.X, pady=(0, 25))

        self.show_password_var = tk.BooleanVar()
        show_check = tk.Checkbutton(
            show_frame,
            text="👁️ SHOW PASSWORD",
            variable=self.show_password_var,
            command=self.toggle_password,
            bg=self.ai_colors['bg_card'],
            fg=self.ai_colors['text_secondary'],
            font=('Consolas', 10),
            selectcolor=self.ai_colors['bg_input'],
            activebackground=self.ai_colors['bg_card'],
            activeforeground=self.ai_colors['accent'],
            relief='flat',
            bd=0
        )
        show_check.pack(anchor='w')

        # أزرار العمل
        buttons_frame = tk.Frame(
            content_frame,
            bg=self.ai_colors['bg_card']
        )
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # زر تسجيل الدخول مع تأثير مضيء
        self.login_button = tk.Button(
            buttons_frame,
            text="🚀 ACCESS GRANTED",
            command=self.login,
            bg=self.ai_colors['accent'],
            fg=self.ai_colors['bg_primary'],
            font=('Consolas', 14, 'bold'),
            relief='flat',
            bd=0,
            pady=15,
            cursor='hand2',
            activebackground=self.ai_colors['accent_hover'],
            activeforeground=self.ai_colors['bg_primary']
        )
        self.login_button.pack(fill=tk.X, pady=(0, 15))

        # زر الخروج
        exit_button = tk.Button(
            buttons_frame,
            text="❌ SYSTEM EXIT",
            command=self.exit_app,
            bg=self.ai_colors['danger'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 12, 'bold'),
            relief='flat',
            bd=0,
            pady=12,
            cursor='hand2',
            activebackground='#ff3742',
            activeforeground=self.ai_colors['text_primary']
        )
        exit_button.pack(fill=tk.X)

        # معلومات النظام
        info_frame = tk.Frame(
            main_frame,
            bg=self.ai_colors['bg_primary']
        )
        info_frame.pack(fill=tk.X, pady=(25, 0))

        # معلومات المطور
        dev_label = tk.Label(
            info_frame,
            text="💻 DEVELOPER: محمد الشوامرة - 📞 0566000140",
            bg=self.ai_colors['bg_primary'],
            fg=self.ai_colors['text_secondary'],
            font=('Consolas', 10)
        )
        dev_label.pack(pady=(0, 10))

        # معلومات تسجيل الدخول
        creds_frame = tk.Frame(
            info_frame,
            bg=self.ai_colors['bg_input'],
            relief='flat',
            bd=1,
            highlightbackground=self.ai_colors['border'],
            highlightthickness=1
        )
        creds_frame.pack(fill=tk.X)

        creds_content = tk.Frame(
            creds_frame,
            bg=self.ai_colors['bg_input']
        )
        creds_content.pack(fill=tk.X, padx=15, pady=10)

        creds_title = tk.Label(
            creds_content,
            text="🔐 DEFAULT CREDENTIALS:",
            bg=self.ai_colors['bg_input'],
            fg=self.ai_colors['success'],
            font=('Consolas', 11, 'bold')
        )
        creds_title.pack()

        admin_label = tk.Label(
            creds_content,
            text="👨‍💼 ADMIN: admin / admin123",
            bg=self.ai_colors['bg_input'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 10)
        )
        admin_label.pack(pady=(5, 2))

        sales_label = tk.Label(
            creds_content,
            text="👨‍💻 SALES: sales / sales123",
            bg=self.ai_colors['bg_input'],
            fg=self.ai_colors['text_primary'],
            font=('Consolas', 10)
        )
        sales_label.pack(pady=(2, 0))

        # ربط مفاتيح الاختصار
        self.root.bind('<Return>', lambda e: self.login())
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda e: self.login())

        # تأثيرات التفاعل
        self.add_hover_effects()

    def add_hover_effects(self):
        """إضافة تأثيرات التفاعل"""
        def on_enter_login(e):
            self.login_button.configure(bg=self.ai_colors['accent_hover'])

        def on_leave_login(e):
            self.login_button.configure(bg=self.ai_colors['accent'])

        self.login_button.bind("<Enter>", on_enter_login)
        self.login_button.bind("<Leave>", on_leave_login)

    def toggle_password(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.show_password_var.get():
            self.password_entry.configure(show="")
        else:
            self.password_entry.configure(show="●")

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username:
            messagebox.showerror("⚠️ ACCESS DENIED", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return

        if not password:
            messagebox.showerror("⚠️ ACCESS DENIED", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return

        # تأثير تحميل
        self.login_button.configure(text="🔄 PROCESSING...", state='disabled')
        self.root.update()

        try:
            if self.auth_manager.authenticate(username, password):
                user_name = self.auth_manager.current_user.get('full_name', 'المستخدم')
                self.login_button.configure(text="✅ ACCESS GRANTED", bg=self.ai_colors['success'])
                self.root.update()
                self.root.after(1000, lambda: [
                    messagebox.showinfo("🎉 SUCCESS", f"مرحباً {user_name}!\nتم منح الوصول للنظام"),
                    self.root.destroy(),
                    self.on_success_callback()
                ])
            else:
                self.login_button.configure(text="❌ ACCESS DENIED", bg=self.ai_colors['danger'])
                self.root.update()
                self.root.after(1500, lambda: [
                    self.login_button.configure(text="🚀 ACCESS GRANTED", bg=self.ai_colors['accent'], state='normal'),
                    messagebox.showerror("⚠️ ACCESS DENIED", "اسم المستخدم أو كلمة المرور غير صحيحة"),
                    self.password_entry.delete(0, tk.END),
                    self.username_entry.focus()
                ])
        except Exception as e:
            self.login_button.configure(text="❌ SYSTEM ERROR", bg=self.ai_colors['danger'])
            self.root.update()
            self.root.after(1500, lambda: [
                self.login_button.configure(text="🚀 ACCESS GRANTED", bg=self.ai_colors['accent'], state='normal'),
                messagebox.showerror("💥 SYSTEM ERROR", f"خطأ في النظام: {str(e)}")
            ])

    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("🚪 SYSTEM EXIT", "هل تريد الخروج من النظام؟"):
            self.root.quit()
            sys.exit()

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        self.exit_app()

    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.root.mainloop()

class DataManager:
    """مدير البيانات مع معالجة الأخطاء"""

    def __init__(self, db_manager):
        self.db = db_manager

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            stats = {}

            # عدد العملاء
            customers = self.db.fetch_one("SELECT COUNT(*) as count FROM customers")
            stats['customers'] = customers['count'] if customers else 0

            # عدد المنتجات
            inventory = self.db.fetch_one("SELECT COUNT(*) as count FROM inventory")
            stats['inventory'] = inventory['count'] if inventory else 0

            # عدد الصيانات المعلقة
            pending_repairs = self.db.fetch_one(
                "SELECT COUNT(*) as count FROM repairs WHERE repair_status NOT IN ('مكتمل', 'ملغي')"
            )
            stats['pending_repairs'] = pending_repairs['count'] if pending_repairs else 0

            # إجمالي المبيعات اليوم
            today_sales = self.db.fetch_one(
                "SELECT SUM(total_amount) as total FROM sales WHERE DATE(sale_date) = DATE('now')"
            )
            stats['today_sales'] = today_sales['total'] if today_sales and today_sales['total'] else 0

            # المنتجات منخفضة المخزون
            low_stock = self.db.fetch_one(
                "SELECT COUNT(*) as count FROM inventory WHERE quantity <= min_quantity"
            )
            stats['low_stock'] = low_stock['count'] if low_stock else 0

            return stats
        except Exception as e:
            ErrorHandler.handle_error(e, "جلب إحصائيات لوحة التحكم")
            return {
                'customers': 0,
                'inventory': 0,
                'pending_repairs': 0,
                'today_sales': 0,
                'low_stock': 0
            }

    def get_recent_sales(self, limit=10):
        """الحصول على المبيعات الأخيرة"""
        try:
            query = '''
                SELECT s.*, c.name as customer_name, i.item_name
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                LEFT JOIN inventory i ON s.item_id = i.id
                ORDER BY s.sale_date DESC
                LIMIT ?
            '''
            return self.db.fetch_all(query, (limit,))
        except Exception as e:
            ErrorHandler.handle_error(e, "جلب المبيعات الأخيرة")
            return []

    def get_recent_repairs(self, limit=10):
        """الحصول على الصيانات الأخيرة"""
        try:
            query = '''
                SELECT r.*, c.name as customer_name
                FROM repairs r
                LEFT JOIN customers c ON r.customer_id = c.id
                ORDER BY r.date_received DESC
                LIMIT ?
            '''
            return self.db.fetch_all(query, (limit,))
        except Exception as e:
            ErrorHandler.handle_error(e, "جلب الصيانات الأخيرة")
            return []

    def get_low_stock_items(self):
        """الحصول على المنتجات منخفضة المخزون"""
        try:
            query = '''
                SELECT * FROM inventory
                WHERE quantity <= min_quantity
                ORDER BY quantity ASC
            '''
            return self.db.fetch_all(query)
        except Exception as e:
            ErrorHandler.handle_error(e, "جلب المنتجات منخفضة المخزون")
            return []

    def search_customers(self, search_term=""):
        """البحث في العملاء"""
        try:
            if search_term:
                query = '''
                    SELECT * FROM customers
                    WHERE name LIKE ? OR phone LIKE ?
                    ORDER BY name
                '''
                return self.db.fetch_all(query, (f'%{search_term}%', f'%{search_term}%'))
            else:
                return self.db.fetch_all("SELECT * FROM customers ORDER BY name")
        except Exception as e:
            ErrorHandler.handle_error(e, "البحث في العملاء")
            return []

    def search_inventory(self, search_term=""):
        """البحث في المخزون"""
        try:
            if search_term:
                query = '''
                    SELECT * FROM inventory
                    WHERE item_name LIKE ? OR barcode LIKE ? OR category LIKE ?
                    ORDER BY item_name
                '''
                return self.db.fetch_all(query, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
            else:
                return self.db.fetch_all("SELECT * FROM inventory ORDER BY item_name")
        except Exception as e:
            ErrorHandler.handle_error(e, "البحث في المخزون")
            return []

    def add_sample_data(self):
        """إضافة بيانات تجريبية"""
        try:
            # التحقق من وجود بيانات
            existing_customers = self.db.fetch_one("SELECT COUNT(*) as count FROM customers")
            if existing_customers and existing_customers['count'] > 0:
                return True

            # إضافة عملاء تجريبيين
            customers = [
                ('أحمد محمد علي', '0501234567', '<EMAIL>', 'الرياض'),
                ('فاطمة أحمد', '0507654321', '<EMAIL>', 'جدة'),
                ('محمد عبدالله', '0509876543', '<EMAIL>', 'الدمام'),
                ('عائشة محمود', '0502468135', '<EMAIL>', 'مكة'),
                ('علي حسن', '0508642097', '<EMAIL>', 'المدينة')
            ]

            for name, phone, email, address in customers:
                self.db.execute_query(
                    'INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)',
                    (name, phone, email, address)
                )

            # إضافة منتجات تجريبية
            products = [
                ('شاشة iPhone 14', 'شاشات', 10, 3, 150.0, 200.0, '123456789001'),
                ('بطارية Samsung S23', 'بطاريات', 15, 5, 80.0, 120.0, '123456789002'),
                ('كفر حماية شفاف', 'إكسسوارات', 50, 10, 15.0, 25.0, '123456789003'),
                ('شاحن سريع Type-C', 'شواحن', 25, 5, 25.0, 40.0, '123456789004'),
                ('سماعات لاسلكية', 'إكسسوارات', 20, 3, 60.0, 100.0, '123456789005')
            ]

            for name, category, quantity, min_qty, purchase_price, selling_price, barcode in products:
                self.db.execute_query('''
                    INSERT INTO inventory
                    (item_name, category, quantity, min_quantity, purchase_price, selling_price, barcode)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (name, category, quantity, min_qty, purchase_price, selling_price, barcode))

            print("✅ تم إضافة البيانات التجريبية")
            return True
        except Exception as e:
            ErrorHandler.handle_error(e, "إضافة البيانات التجريبية")
            return False

class PhoneDoctorApp:
    """التطبيق الرئيسي المحسن"""

    def __init__(self):
        # إعداد المكونات الأساسية
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager()
        self.data_manager = DataManager(self.db_manager)
        self.styles = SimpleStyles()

        # تهيئة قاعدة البيانات
        if not self.db_manager.initialize_database():
            messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
            sys.exit(1)

        # إضافة بيانات تجريبية
        self.data_manager.add_sample_data()

        # متغيرات التطبيق
        self.root = None
        self.current_page = "dashboard"

        # عرض نافذة تسجيل الدخول
        self.show_login()

    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = FixedLoginWindow(self.auth_manager, self.on_login_success)
        login_window.show()

    def on_login_success(self):
        """معالجة نجاح تسجيل الدخول"""
        self.setup_main_window()
        self.load_dashboard()
        self.root.mainloop()

    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor - نظام إدارة محلات صيانة الهواتف")
        self.root.geometry("1200x800")
        self.root.state('zoomed')
        self.root.configure(bg=self.styles.colors['bg_light'])

        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = tk.Frame(
            self.root,
            bg=self.styles.colors['bg_light']
        )
        main_frame.pack(fill=tk.BOTH, expand=True)

        # الشريط العلوي
        self.setup_header(main_frame)

        # المحتوى الرئيسي
        content_frame = tk.Frame(
            main_frame,
            bg=self.styles.colors['bg_light']
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # الشريط الجانبي
        self.setup_sidebar(content_frame)

        # منطقة المحتوى
        self.content_area = tk.Frame(
            content_frame,
            bg='white',
            relief='solid',
            bd=1
        )
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

    def setup_header(self, parent):
        """إعداد الشريط العلوي"""
        header_frame = tk.Frame(
            parent,
            bg=self.styles.colors['primary'],
            height=60
        )
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # محتوى الشريط العلوي
        header_content = tk.Frame(
            header_frame,
            bg=self.styles.colors['primary']
        )
        header_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # شعار التطبيق
        logo_frame = tk.Frame(
            header_content,
            bg=self.styles.colors['primary']
        )
        logo_frame.pack(side=tk.LEFT)

        app_icon = tk.Label(
            logo_frame,
            text="📱",
            bg=self.styles.colors['primary'],
            fg='white',
            font=('Arial', 24)
        )
        app_icon.pack(side=tk.LEFT, padx=(0, 10))

        app_title = tk.Label(
            logo_frame,
            text="Phone Doctor",
            bg=self.styles.colors['primary'],
            fg='white',
            font=self.styles.fonts['title']
        )
        app_title.pack(side=tk.LEFT)

        # معلومات المستخدم
        user_frame = tk.Frame(
            header_content,
            bg=self.styles.colors['primary']
        )
        user_frame.pack(side=tk.RIGHT)

        if self.auth_manager.current_user:
            user_name = self.auth_manager.current_user['full_name']
            user_label = tk.Label(
                user_frame,
                text=f"👤 {user_name}",
                bg=self.styles.colors['primary'],
                fg='white',
                font=self.styles.fonts['body']
            )
            user_label.pack(side=tk.LEFT, padx=(0, 10))

        # زر تسجيل الخروج
        logout_btn = tk.Button(
            user_frame,
            text="🚪 خروج",
            command=self.logout,
            bg=self.styles.colors['danger'],
            fg='white',
            font=self.styles.fonts['small'],
            relief='flat',
            bd=0,
            padx=15,
            pady=5,
            cursor='hand2'
        )
        logout_btn.pack(side=tk.RIGHT)

    def setup_sidebar(self, parent):
        """إعداد الشريط الجانبي"""
        sidebar_frame = tk.Frame(
            parent,
            bg=self.styles.colors['bg_dark'],
            width=250
        )
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(
            sidebar_frame,
            text="📋 القوائم الرئيسية",
            bg=self.styles.colors['bg_dark'],
            fg='white',
            font=self.styles.fonts['heading']
        )
        sidebar_title.pack(fill=tk.X, padx=15, pady=(15, 10))

        # أزرار القوائم
        menu_items = [
            ("🏠", "لوحة التحكم", "dashboard"),
            ("🔧", "إدارة الصيانة", "repairs"),
            ("📦", "إدارة المخزون", "inventory"),
            ("🏪", "إدارة الموردين", "suppliers"),
            ("💰", "إدارة المبيعات", "sales"),
            ("💳", "الشؤون المالية", "financial"),
            ("📊", "التقارير", "reports"),
            ("⚙️", "الإعدادات", "settings")
        ]

        self.menu_buttons = {}

        for icon, text, page_id in menu_items:
            # التحقق من الصلاحيات
            if not self.auth_manager.has_permission(page_id) and page_id not in ['dashboard']:
                continue

            btn_frame = tk.Frame(
                sidebar_frame,
                bg=self.styles.colors['bg_dark']
            )
            btn_frame.pack(fill=tk.X, padx=10, pady=2)

            btn = tk.Button(
                btn_frame,
                text=f"{icon} {text}",
                command=lambda p=page_id: self.change_page(p),
                bg=self.styles.colors['bg_dark'],
                fg='white',
                font=self.styles.fonts['body'],
                relief='flat',
                bd=0,
                padx=15,
                pady=12,
                cursor='hand2',
                anchor='w'
            )
            btn.pack(fill=tk.X)

            self.menu_buttons[page_id] = btn

            # تمييز الصفحة الحالية
            if page_id == self.current_page:
                btn.configure(bg=self.styles.colors['primary'])

    def change_page(self, page_id):
        """تغيير الصفحة"""
        # إعادة تعيين ألوان الأزرار
        for btn_id, btn in self.menu_buttons.items():
            if btn_id == page_id:
                btn.configure(bg=self.styles.colors['primary'])
            else:
                btn.configure(bg=self.styles.colors['bg_dark'])

        self.current_page = page_id

        # تحميل محتوى الصفحة
        if page_id == "dashboard":
            self.load_dashboard()
        elif page_id == "sales":
            self.load_sales_page()
        elif page_id == "repairs":
            self.load_repairs_page()
        elif page_id == "inventory":
            self.load_inventory_page()
        elif page_id == "customers":
            self.load_customers_page()
        else:
            self.load_placeholder_page(page_id)

    def clear_content_area(self):
        """مسح منطقة المحتوى"""
        for widget in self.content_area.winfo_children():
            widget.destroy()

    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        self.clear_content_area()

        # عنوان الصفحة
        title_frame = tk.Frame(
            self.content_area,
            bg='white'
        )
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="🏠 لوحة التحكم",
            bg='white',
            fg=self.styles.colors['text_dark'],
            font=self.styles.fonts['title']
        )
        title_label.pack(anchor='w')

        # الإحصائيات
        stats_frame = tk.Frame(
            self.content_area,
            bg='white'
        )
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        # جلب الإحصائيات
        stats = self.data_manager.get_dashboard_stats()

        # بطاقات الإحصائيات
        stats_data = [
            ("👥", "العملاء", stats['customers'], self.styles.colors['primary']),
            ("📦", "المنتجات", stats['inventory'], self.styles.colors['success']),
            ("🔧", "الصيانات المعلقة", stats['pending_repairs'], self.styles.colors['warning']),
            ("💰", "مبيعات اليوم", f"{stats['today_sales']:.2f} ₪", self.styles.colors['success']),
            ("⚠️", "مخزون منخفض", stats['low_stock'], self.styles.colors['danger'])
        ]

        # إنشاء الشبكة
        for i, (icon, title, value, color) in enumerate(stats_data):
            row = i // 3
            col = i % 3

            card_frame = tk.Frame(
                stats_frame,
                bg=color,
                relief='solid',
                bd=1
            )
            card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

            # محتوى البطاقة
            card_content = tk.Frame(
                card_frame,
                bg=color
            )
            card_content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            # الأيقونة
            icon_label = tk.Label(
                card_content,
                text=icon,
                bg=color,
                fg='white',
                font=('Arial', 24)
            )
            icon_label.pack()

            # القيمة
            value_label = tk.Label(
                card_content,
                text=str(value),
                bg=color,
                fg='white',
                font=self.styles.fonts['title']
            )
            value_label.pack()

            # العنوان
            title_label = tk.Label(
                card_content,
                text=title,
                bg=color,
                fg='white',
                font=self.styles.fonts['body']
            )
            title_label.pack()

        # تكوين الأعمدة
        for i in range(3):
            stats_frame.columnconfigure(i, weight=1)

        # الجداول
        tables_frame = tk.Frame(
            self.content_area,
            bg='white'
        )
        tables_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # جدول المبيعات الأخيرة
        sales_frame = tk.LabelFrame(
            tables_frame,
            text="💰 المبيعات الأخيرة",
            bg='white',
            fg=self.styles.colors['text_dark'],
            font=self.styles.fonts['heading']
        )
        sales_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # جدول الصيانات الأخيرة
        repairs_frame = tk.LabelFrame(
            tables_frame,
            text="🔧 الصيانات الأخيرة",
            bg='white',
            fg=self.styles.colors['text_dark'],
            font=self.styles.fonts['heading']
        )
        repairs_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # إضافة البيانات للجداول
        self.load_recent_sales_table(sales_frame)
        self.load_recent_repairs_table(repairs_frame)

    def load_recent_sales_table(self, parent):
        """تحميل جدول المبيعات الأخيرة"""
        try:
            # إنشاء الجدول
            columns = ("العميل", "المنتج", "الكمية", "المبلغ")
            tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

            # تعيين العناوين
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=100)

            # جلب البيانات
            sales = self.data_manager.get_recent_sales(10)

            # إضافة البيانات
            for sale in sales:
                customer_name = sale['customer_name'] if sale['customer_name'] else 'غير محدد'
                item_name = sale['item_name'] if sale['item_name'] else 'غير محدد'
                tree.insert('', 'end', values=(
                    customer_name,
                    item_name,
                    sale['quantity'],
                    f"{sale['total_amount']:.2f} ₪"
                ))

            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        except Exception as e:
            ErrorHandler.handle_error(e, "تحميل جدول المبيعات")

    def load_recent_repairs_table(self, parent):
        """تحميل جدول الصيانات الأخيرة"""
        try:
            # إنشاء الجدول
            columns = ("العميل", "الجهاز", "المشكلة", "الحالة")
            tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

            # تعيين العناوين
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=100)

            # جلب البيانات
            repairs = self.data_manager.get_recent_repairs(10)

            # إضافة البيانات
            for repair in repairs:
                customer_name = repair['customer_name'] if repair['customer_name'] else 'غير محدد'
                tree.insert('', 'end', values=(
                    customer_name,
                    repair['device_type'],
                    repair['problem_description'][:30] + "..." if len(repair['problem_description']) > 30 else repair['problem_description'],
                    repair['repair_status']
                ))

            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        except Exception as e:
            ErrorHandler.handle_error(e, "تحميل جدول الصيانات")

    def load_sales_page(self):
        """تحميل صفحة المبيعات"""
        self.clear_content_area()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="💰 إدارة المبيعات",
            bg='white',
            fg=self.styles.colors['text_dark'],
            font=self.styles.fonts['title']
        )
        title_label.pack(anchor='w')

        # أزرار العمل
        buttons_frame = tk.Frame(self.content_area, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        new_sale_btn = tk.Button(
            buttons_frame,
            text="🛒 بيع جديد",
            command=self.open_new_sale,
            font=self.styles.fonts['body'],
            **self.styles.get_button_style('success')
        )
        new_sale_btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول المبيعات
        self.create_sales_table()

    def load_repairs_page(self):
        """تحميل صفحة الصيانات"""
        self.clear_content_area()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="🔧 إدارة الصيانة",
            bg='white',
            fg=self.styles.colors['text_dark'],
            font=self.styles.fonts['title']
        )
        title_label.pack(anchor='w')

        # أزرار العمل
        buttons_frame = tk.Frame(self.content_area, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        new_repair_btn = tk.Button(
            buttons_frame,
            text="🔧 صيانة جديدة",
            command=self.open_new_repair,
            font=self.styles.fonts['body'],
            **self.styles.get_button_style('primary')
        )
        new_repair_btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول الصيانات
        self.create_repairs_table()

    def load_inventory_page(self):
        """تحميل صفحة المخزون"""
        self.clear_content_area()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_area, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        title_label = tk.Label(
            title_frame,
            text="📦 إدارة المخزون",
            bg='white',
            fg=self.styles.colors['text_dark'],
            font=self.styles.fonts['title']
        )
        title_label.pack(anchor='w')

        # أزرار العمل
        buttons_frame = tk.Frame(self.content_area, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        add_item_btn = tk.Button(
            buttons_frame,
            text="➕ إضافة منتج",
            command=self.open_add_item,
            font=self.styles.fonts['body'],
            **self.styles.get_button_style('success')
        )
        add_item_btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول المخزون
        self.create_inventory_table()

    def load_placeholder_page(self, page_id):
        """تحميل صفحة مؤقتة"""
        self.clear_content_area()

        placeholder_frame = tk.Frame(
            self.content_area,
            bg='white'
        )
        placeholder_frame.pack(fill=tk.BOTH, expand=True)

        placeholder_label = tk.Label(
            placeholder_frame,
            text=f"🚧 صفحة {page_id} قيد التطوير",
            bg='white',
            fg=self.styles.colors['text_light'],
            font=self.styles.fonts['title']
        )
        placeholder_label.pack(expand=True)

    def create_sales_table(self):
        """إنشاء جدول المبيعات"""
        table_frame = tk.Frame(self.content_area, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        columns = ("التاريخ", "العميل", "المنتج", "الكمية", "المبلغ", "طريقة الدفع")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # جلب البيانات
        sales = self.data_manager.get_recent_sales(50)
        for sale in sales:
            customer_name = sale['customer_name'] if sale['customer_name'] else 'غير محدد'
            item_name = sale['item_name'] if sale['item_name'] else 'غير محدد'
            tree.insert('', 'end', values=(
                sale['sale_date'][:10],
                customer_name,
                item_name,
                sale['quantity'],
                f"{sale['total_amount']:.2f} ₪",
                sale['payment_method']
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_repairs_table(self):
        """إنشاء جدول الصيانات"""
        table_frame = tk.Frame(self.content_area, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        columns = ("التاريخ", "العميل", "الجهاز", "المشكلة", "الحالة", "التكلفة")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # جلب البيانات
        repairs = self.data_manager.get_recent_repairs(50)
        for repair in repairs:
            customer_name = repair['customer_name'] if repair['customer_name'] else 'غير محدد'
            tree.insert('', 'end', values=(
                repair['date_received'],
                customer_name,
                repair['device_type'],
                repair['problem_description'][:30] + "..." if len(repair['problem_description']) > 30 else repair['problem_description'],
                repair['repair_status'],
                f"{repair['estimated_cost']:.2f} ₪"
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_inventory_table(self):
        """إنشاء جدول المخزون"""
        table_frame = tk.Frame(self.content_area, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        columns = ("المنتج", "الفئة", "الكمية", "الحد الأدنى", "سعر الشراء", "سعر البيع", "الباركود")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)

        # جلب البيانات
        inventory = self.data_manager.search_inventory()
        for item in inventory:
            # تمييز المنتجات منخفضة المخزون
            tags = ()
            if item['quantity'] <= item['min_quantity']:
                tags = ('low_stock',)

            tree.insert('', 'end', values=(
                item['item_name'],
                item['category'],
                item['quantity'],
                item['min_quantity'],
                f"{item['purchase_price']:.2f} ₪",
                f"{item['selling_price']:.2f} ₪",
                item['barcode'] if item['barcode'] else ''
            ), tags=tags)

        # تنسيق المنتجات منخفضة المخزون
        tree.tag_configure('low_stock', background='#fee2e2', foreground='#dc2626')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def open_new_sale(self):
        """فتح نافذة بيع جديد"""
        messagebox.showinfo("قريباً", "نافذة البيع الجديد قيد التطوير")

    def open_new_repair(self):
        """فتح نافذة صيانة جديدة"""
        messagebox.showinfo("قريباً", "نافذة الصيانة الجديدة قيد التطوير")

    def open_add_item(self):
        """فتح نافذة إضافة منتج"""
        messagebox.showinfo("قريباً", "نافذة إضافة المنتج قيد التطوير")

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد تسجيل الخروج؟"):
            self.auth_manager.logout()
            self.root.destroy()
            self.show_login()

def main():
    """نقطة الدخول الرئيسية"""
    try:
        print("🚀 بدء تشغيل Phone Doctor...")
        app = PhoneDoctorApp()
    except Exception as e:
        ErrorHandler.handle_error(e, "تشغيل التطبيق")
        sys.exit(1)

if __name__ == "__main__":
    main()