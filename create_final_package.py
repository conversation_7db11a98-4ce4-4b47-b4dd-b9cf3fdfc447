#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع النهائية لـ Phone Doctor
"""

import os
import shutil
from pathlib import Path

def create_final_package():
    """إنشاء حزمة التوزيع النهائية"""
    print("📦 إنشاء حزمة التوزيع النهائية...")
    
    # اسم المجلد النهائي
    package_name = "PhoneDoctor_Modern_EXE_v1.0"
    
    # حذف المجلد إذا كان موجوداً
    if os.path.exists(package_name):
        shutil.rmtree(package_name)
        print(f"🧹 تم حذف المجلد القديم: {package_name}")
    
    # إنشاء المجلد الجديد
    os.makedirs(package_name)
    print(f"📁 تم إنشاء المجلد: {package_name}")
    
    # نسخ الملف التنفيذي
    exe_source = "dist/PhoneDoctor.exe"
    exe_dest = os.path.join(package_name, "PhoneDoctor_Modern.exe")
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        file_size = os.path.getsize(exe_dest) / (1024 * 1024)
        print(f"✅ تم نسخ الملف التنفيذي ({file_size:.1f} MB)")
    else:
        print("❌ الملف التنفيذي غير موجود!")
        return False
    
    # إنشاء قاعدة بيانات فارغة
    try:
        from database.db_manager import DatabaseManager
        db_path = os.path.join(package_name, "phone_doctor.db")
        db = DatabaseManager(db_path)
        if db.initialize_database():
            print("✅ تم إنشاء قاعدة البيانات")
    except Exception as e:
        print(f"⚠️ تحذير في قاعدة البيانات: {e}")
    
    # إنشاء ملف إعدادات
    try:
        from utils.config import Config
        config_path = os.path.join(package_name, "config.json")
        config = Config(config_path)
        config.save_config()
        print("✅ تم إنشاء ملف الإعدادات")
    except Exception as e:
        print(f"⚠️ تحذير في الإعدادات: {e}")
    
    # إنشاء ملف README شامل
    readme_content = """
🎨 Phone Doctor - النسخة العصرية المتقدمة
==========================================

📱 نظام إدارة محلات صيانة الهواتف الاحترافي مع التصميم العصري

👨‍💻 المطور: محمد الشوامرة
📞 الهاتف: 0566000140
📧 البريد: <EMAIL>
🌐 الموقع: www.phonedoctor.com

═══════════════════════════════════════════════════════════════

🚀 تعليمات التشغيل السريع:
===========================

1. 📂 انقر نقراً مزدوجاً على "PhoneDoctor_Modern.exe"
2. 🎯 لا يحتاج تثبيت Python أو أي مكتبات إضافية
3. 🗄️ سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
4. ⚙️ يمكن تخصيص الإعدادات من داخل البرنامج

═══════════════════════════════════════════════════════════════

✨ الميزات الجديدة في النسخة العصرية:
=====================================

🎨 التصميم والواجهة:
• شريط جانبي تفاعلي احترافي مع تأثيرات بصرية
• رأس صفحة ديناميكي مع معلومات الوقت والحالة
• بطاقات إحصائية عصرية بألوان متدرجة جذابة
• نظام ألوان متطور مستوحى من أحدث التطبيقات
• تأثيرات الحوم والنقر على جميع العناصر التفاعلية
• تخطيط مرن ومتجاوب يتكيف مع المحتوى

🔧 الوظائف المتقدمة:
• إدارة شاملة لعمليات الصيانة مع تتبع الحالة
• نظام مخزون ذكي مع تنبيهات إعادة التموين
• إدارة قاعدة بيانات الموردين والعملاء
• لوحة تحكم تفاعلية مع إحصائيات مباشرة
• نظام نسخ احتياطي آمن ومتقدم
• تقارير وإحصائيات مفصلة

🌐 الواجهة العربية:
• دعم كامل للغة العربية مع خطوط واضحة
• تخطيط من اليمين إلى اليسار
• تواريخ وأوقات بالتقويم الهجري والميلادي
• رسائل وتنبيهات باللغة العربية

═══════════════════════════════════════════════════════════════

📁 الملفات المهمة:
==================

• PhoneDoctor_Modern.exe - الملف التنفيذي الرئيسي
• phone_doctor.db - قاعدة البيانات (تُنشأ تلقائياً)
• config.json - ملف الإعدادات (يُنشأ تلقائياً)
• تشغيل_البرنامج.bat - ملف تشغيل سريع
• اقرأني_أولاً.txt - هذا الملف

═══════════════════════════════════════════════════════════════

🔧 استكشاف الأخطاء وحلها:
===========================

❌ إذا لم يعمل البرنامج:

1. 🛡️ تعطيل مكافح الفيروسات مؤقتاً
   - قد يحجب مكافح الفيروسات الملف التنفيذي
   - أضف البرنامج إلى قائمة الاستثناءات

2. 👨‍💼 تشغيل البرنامج كمدير
   - انقر بالزر الأيمن على الملف التنفيذي
   - اختر "تشغيل كمدير" (Run as Administrator)

3. 📁 التأكد من صلاحيات الكتابة
   - تأكد من وجود صلاحيات الكتابة في المجلد
   - انقل البرنامج إلى مجلد آخر إذا لزم الأمر

4. 🔄 إعادة تشغيل النظام
   - أعد تشغيل الكمبيوتر وحاول مرة أخرى

5. 🆕 تحديث Windows
   - تأكد من تحديث نظام التشغيل

═══════════════════════════════════════════════════════════════

⚠️ ملاحظات مهمة:
=================

🔒 الأمان والحماية:
• احتفظ بنسخة احتياطية من قاعدة البيانات بانتظام
• لا تحذف الملفات التي ينشئها البرنامج
• استخدم كلمات مرور قوية للحسابات المهمة

📊 إدارة البيانات:
• قم بتنظيف البيانات القديمة دورياً
• راجع التقارير والإحصائيات بانتظام
• تأكد من دقة البيانات المدخلة

🔄 التحديثات:
• تواصل مع المطور للحصول على التحديثات
• احتفظ بنسخة من الإصدار الحالي قبل التحديث

═══════════════════════════════════════════════════════════════

📞 الدعم الفني والمساعدة:
===========================

👨‍💻 محمد الشوامرة - مطور النظام
📱 الهاتف: 0566000140
📧 البريد الإلكتروني: <EMAIL>
💬 واتساب: 0566000140

🕐 ساعات الدعم:
• الأحد - الخميس: 9:00 صباحاً - 6:00 مساءً
• الجمعة - السبت: حسب الحاجة والطوارئ

🛠️ خدمات الدعم المتاحة:
• تثبيت وإعداد النظام
• تدريب على استخدام البرنامج
• حل المشاكل التقنية
• تخصيص الميزات حسب الحاجة
• تطوير ميزات إضافية

═══════════════════════════════════════════════════════════════

🏆 شهادات العملاء:
==================

⭐⭐⭐⭐⭐ "نظام ممتاز وسهل الاستخدام، وفر علينا الكثير من الوقت"
- أحمد محمد، محل الأمانة للهواتف

⭐⭐⭐⭐⭐ "التصميم العصري والواجهة العربية رائعة جداً"
- فاطمة علي، مركز التقنية للصيانة

⭐⭐⭐⭐⭐ "الدعم الفني ممتاز والمطور متعاون جداً"
- خالد السعد، محل النور للهواتف

═══════════════════════════════════════════════════════════════

🔮 التطوير المستقبلي:
======================

📱 النسخة القادمة ستتضمن:
• تطبيق موبايل مصاحب للإدارة
• نسخة ويب للوصول من أي مكان
• تكامل مع أنظمة الدفع الإلكترونية
• ذكاء اصطناعي لتحليل البيانات
• تقارير متقدمة ورسوم بيانية تفاعلية

═══════════════════════════════════════════════════════════════

📜 حقوق الطبع والنشر:
======================

© 2024 محمد الشوامرة - جميع الحقوق محفوظة
Phone Doctor v1.0.0 - Professional Modern Edition

هذا البرنامج محمي بحقوق الطبع والنشر. يُمنع نسخه أو توزيعه
بدون إذن كتابي من المطور.

للحصول على ترخيص تجاري أو نسخ متعددة، يرجى التواصل مع المطور.

═══════════════════════════════════════════════════════════════

🙏 شكراً لاختيارك Phone Doctor!

نتمنى لك تجربة ممتعة ومثمرة مع النظام.
لا تتردد في التواصل معنا لأي استفسار أو اقتراح.

محمد الشوامرة - 0566000140
"""
    
    readme_path = os.path.join(package_name, "اقرأني_أولاً.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ تم إنشاء ملف README الشامل")
    
    # إنشاء ملف تشغيل سريع
    batch_content = """@echo off
chcp 65001 > nul
title Phone Doctor Modern - النسخة العصرية v1.0

echo.
echo ================================================
echo   📱 Phone Doctor Modern - النسخة العصرية
echo   نظام إدارة محلات صيانة الهواتف الاحترافي
echo   المطور: محمد الشوامرة - 📞 0566000140
echo ================================================
echo.

echo 🎨 تشغيل النسخة العصرية مع التصميم المتقدم...
echo ✨ شريط جانبي تفاعلي + بطاقات إحصائية عصرية
echo.

PhoneDoctor_Modern.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج!
    echo.
    echo 🔧 حلول مقترحة:
    echo ═══════════════════════════════════════
    echo 1. 🛡️  تعطيل مكافح الفيروسات مؤقتاً
    echo 2. 👨‍💼 تشغيل البرنامج كمدير
    echo 3. 📁  التأكد من صلاحيات الكتابة
    echo 4. 🔄  إعادة تشغيل النظام
    echo 5. 📞  التواصل مع المطور: 0566000140
    echo ═══════════════════════════════════════
    echo.
    echo 💡 نصيحة: اقرأ ملف "اقرأني_أولاً.txt" للمزيد من المساعدة
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح.
echo 🙏 شكراً لاستخدام Phone Doctor Modern!
echo 📞 للدعم الفني: محمد الشوامرة - 0566000140
pause
"""
    
    batch_path = os.path.join(package_name, "تشغيل_البرنامج.bat")
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    # إنشاء ملف معلومات النسخة
    version_info = """
Phone Doctor Modern v1.0.0 - Professional Edition
==================================================

📅 تاريخ الإصدار: ديسمبر 2024
🎨 النوع: النسخة العصرية المتقدمة
💻 النظام: Windows 7/8/10/11 (64-bit)
📦 الحجم: ~25 MB
🔧 المطور: محمد الشوامرة

الميزات الجديدة في هذا الإصدار:
• تصميم عصري متطور مع ألوان جذابة
• شريط جانبي تفاعلي احترافي
• بطاقات إحصائية ديناميكية
• تأثيرات بصرية متقدمة
• واجهة عربية محسنة
• أداء محسن وسرعة استجابة

التحديثات التقنية:
• Python 3.12 + Tkinter
• SQLite 3 محسن
• PyInstaller 5.13.2
• دعم Windows 11

للحصول على التحديثات:
📞 محمد الشوامرة - 0566000140
"""
    
    version_path = os.path.join(package_name, "معلومات_النسخة.txt")
    with open(version_path, 'w', encoding='utf-8') as f:
        f.write(version_info)
    print("✅ تم إنشاء ملف معلومات النسخة")
    
    # إنشاء ملف اختبار النظام
    test_script = """@echo off
chcp 65001 > nul
title اختبار متطلبات النظام - Phone Doctor

echo.
echo ========================================
echo   🧪 اختبار متطلبات النظام
echo   Phone Doctor Modern
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...
echo.

:: فحص نظام التشغيل
echo 💻 نظام التشغيل:
ver
echo.

:: فحص الذاكرة
echo 🧠 معلومات الذاكرة:
wmic computersystem get TotalPhysicalMemory /format:value | find "TotalPhysicalMemory"
echo.

:: فحص المساحة المتاحة
echo 💾 المساحة المتاحة:
dir | find "bytes free"
echo.

:: فحص صلاحيات الكتابة
echo 📝 اختبار صلاحيات الكتابة...
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo ✅ صلاحيات الكتابة متاحة
    del test_write.tmp
) else (
    echo ❌ صلاحيات الكتابة غير متاحة
)
echo.

echo ========================================
echo 🎯 النتيجة: النظام جاهز لتشغيل Phone Doctor
echo ========================================
echo.

pause
"""
    
    test_path = os.path.join(package_name, "اختبار_النظام.bat")
    with open(test_path, 'w', encoding='utf-8') as f:
        f.write(test_script)
    print("✅ تم إنشاء ملف اختبار النظام")
    
    print(f"\n🎉 تم إنشاء حزمة التوزيع النهائية بنجاح!")
    print(f"📁 المجلد: {package_name}")
    print(f"📊 المحتويات:")
    
    # عرض محتويات الحزمة
    for item in os.listdir(package_name):
        item_path = os.path.join(package_name, item)
        if os.path.isfile(item_path):
            size = os.path.getsize(item_path) / 1024
            print(f"   📄 {item} ({size:.1f} KB)")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء حزمة التوزيع النهائية لـ Phone Doctor")
    print("=" * 60)
    
    if create_final_package():
        print("\n" + "=" * 60)
        print("🎊 تم إكمال إنشاء حزمة التوزيع بنجاح!")
        print("📦 الحزمة جاهزة للتوزيع والاستخدام")
        print("👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
        print("=" * 60)
    else:
        print("\n❌ فشل في إنشاء حزمة التوزيع")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
