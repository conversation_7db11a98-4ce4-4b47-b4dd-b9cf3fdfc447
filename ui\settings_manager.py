#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الإعدادات والمستخدمين
Settings and Users Manager

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import hashlib

class SettingsManager:
    """مدير الإعدادات والمستخدمين"""
    
    def __init__(self, parent_frame, db_manager, auth_manager):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.selected_user = None
        
        # ألوان النظام العصرية
        self.colors = {
            'primary': '#7c3aed',
            'secondary': '#06b6d4',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'info': '#3b82f6',
            'bg_light': '#f8fafc',
            'bg_gradient': '#ede9fe',
            'text_dark': '#1f2937',
            'text_light': '#6b7280',
            'accent': '#ec4899'
        }
        
        # التحقق من صلاحيات المدير
        if not self.check_admin_permissions():
            self.show_access_denied()
            return
        
        self.setup_ui()
        self.ensure_settings_table()
        self.load_settings()
    
    def check_admin_permissions(self):
        """التحقق من صلاحيات المدير"""
        current_user = self.auth_manager.current_user
        if not current_user:
            return False
        
        # فقط المدير يمكنه الوصول للإعدادات
        return current_user.get('role') == 'admin' or current_user.get('username') == 'admin'
    
    def show_access_denied(self):
        """عرض رسالة منع الوصول"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # إطار منع الوصول
        access_frame = tk.Frame(self.parent_frame, bg='white')
        access_frame.pack(fill=tk.BOTH, expand=True)
        
        # رسالة منع الوصول
        denied_frame = tk.Frame(access_frame, bg=self.colors['danger'])
        denied_frame.pack(expand=True, fill=tk.BOTH, padx=50, pady=50)
        
        # الأيقونة
        icon_label = tk.Label(
            denied_frame,
            text="🚫",
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 80)
        )
        icon_label.pack(expand=True)
        
        # العنوان
        title_label = tk.Label(
            denied_frame,
            text="ممنوع الوصول",
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 24, 'bold')
        )
        title_label.pack()
        
        # الرسالة
        message_label = tk.Label(
            denied_frame,
            text="عذراً، هذه الصفحة مخصصة للمديرين فقط\nيرجى تسجيل الدخول بحساب المدير للوصول للإعدادات",
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        message_label.pack(pady=20)
        
        return
    
    def setup_ui(self):
        """إعداد واجهة الإعدادات العصرية"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي مع تدرج لوني
        title_frame = tk.Frame(self.parent_frame, bg='white', height=100)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        title_frame.pack_propagate(False)
        
        # إطار التدرج للعنوان
        gradient_frame = tk.Frame(title_frame, bg=self.colors['primary'], height=80)
        gradient_frame.pack(fill=tk.X, pady=10)
        gradient_frame.pack_propagate(False)
        
        title_content = tk.Frame(gradient_frame, bg=self.colors['primary'])
        title_content.pack(expand=True, fill=tk.BOTH)
        
        title_label = tk.Label(
            title_content,
            text="⚙️ إعدادات النظام",
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 22, 'bold')
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            title_content,
            text="إدارة إعدادات المحل والمستخدمين والصلاحيات",
            bg=self.colors['primary'],
            fg='#e2e8f0',
            font=('Arial', 12)
        )
        subtitle_label.pack()
        
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(self.parent_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # تبويب إعدادات المحل
        self.create_shop_settings_tab()
        
        # تبويب إدارة المستخدمين
        self.create_users_management_tab()
        
        # تبويب إعدادات النظام
        self.create_system_settings_tab()
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab()

        # تبويب إدارة التراخيص (للمدير فقط)
        self.create_license_tab()
    
    def create_shop_settings_tab(self):
        """إنشاء تبويب إعدادات المحل"""
        shop_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(shop_frame, text="🏪 إعدادات المحل")
        
        # إعدادات المحل
        shop_info_frame = tk.LabelFrame(
            shop_frame,
            text="🏪 معلومات المحل",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        shop_info_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # اسم المحل
        self.create_setting_field(shop_info_frame, "اسم المحل:", "shop_name", 0, "Phone Doctor")
        
        # عنوان المحل
        self.create_setting_field(shop_info_frame, "عنوان المحل:", "shop_address", 1, "الرياض، المملكة العربية السعودية")
        
        # رقم الهاتف
        self.create_setting_field(shop_info_frame, "رقم الهاتف:", "shop_phone", 2, "0566000140")
        
        # البريد الإلكتروني
        self.create_setting_field(shop_info_frame, "البريد الإلكتروني:", "shop_email", 3, "<EMAIL>")
        
        # اسم المالك
        self.create_setting_field(shop_info_frame, "اسم المالك:", "owner_name", 4, "محمد الشوامرة")
        
        # رقم السجل التجاري
        self.create_setting_field(shop_info_frame, "رقم السجل التجاري:", "commercial_register", 5, "1234567890")
        
        # أزرار حفظ إعدادات المحل
        shop_buttons_frame = tk.Frame(shop_frame, bg='white')
        shop_buttons_frame.pack(fill=tk.X, padx=30, pady=20)
        
        save_shop_btn = tk.Button(
            shop_buttons_frame,
            text="💾 حفظ إعدادات المحل",
            command=self.save_shop_settings,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=25,
            pady=12,
            cursor='hand2'
        )
        save_shop_btn.pack(side=tk.LEFT)
        
        reset_shop_btn = tk.Button(
            shop_buttons_frame,
            text="🔄 إعادة تعيين",
            command=self.reset_shop_settings,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=25,
            pady=12,
            cursor='hand2'
        )
        reset_shop_btn.pack(side=tk.LEFT, padx=(15, 0))
    
    def create_users_management_tab(self):
        """إنشاء تبويب إدارة المستخدمين"""
        users_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(users_frame, text="👥 إدارة المستخدمين")
        
        # شريط الأدوات
        toolbar_frame = tk.Frame(users_frame, bg='white')
        toolbar_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # أزرار إدارة المستخدمين
        add_user_btn = tk.Button(
            toolbar_frame,
            text="➕ إضافة مستخدم",
            command=self.add_user,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        add_user_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        edit_user_btn = tk.Button(
            toolbar_frame,
            text="✏️ تعديل مستخدم",
            command=self.edit_user,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        edit_user_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        delete_user_btn = tk.Button(
            toolbar_frame,
            text="🗑️ حذف مستخدم",
            command=self.delete_user,
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        delete_user_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        change_password_btn = tk.Button(
            toolbar_frame,
            text="🔑 تغيير كلمة المرور",
            command=self.change_password,
            bg=self.colors['info'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        change_password_btn.pack(side=tk.LEFT)
        
        # جدول المستخدمين
        table_frame = tk.Frame(users_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        table_title = tk.Label(
            table_frame,
            text="👥 قائمة المستخدمين",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold')
        )
        table_title.pack(anchor='w', pady=(0, 10))
        
        # إنشاء الجدول
        columns = ("ID", "اسم المستخدم", "الاسم الكامل", "الدور", "الحالة", "آخر دخول")
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # تعيين العناوين وعرض الأعمدة
        column_widths = [50, 120, 180, 100, 80, 150]
        for i, col in enumerate(columns):
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=column_widths[i], anchor='center')
        
        # تلوين الصفوف حسب الدور
        self.users_tree.tag_configure('admin', background='#dbeafe', foreground='#1e40af')
        self.users_tree.tag_configure('user', background='#f0fdf4', foreground='#166534')
        self.users_tree.tag_configure('inactive', background='#fee2e2', foreground='#991b1b')
        
        # شريط التمرير
        users_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)
        
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط أحداث الجدول
        self.users_tree.bind('<ButtonRelease-1>', self.on_user_select)
        self.users_tree.bind('<Double-1>', self.edit_user)
        
        # تحميل المستخدمين
        self.load_users()
    
    def create_system_settings_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        system_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(system_frame, text="🔧 إعدادات النظام")
        
        # إعدادات النظام
        system_settings_frame = tk.LabelFrame(
            system_frame,
            text="🔧 إعدادات النظام",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        system_settings_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # إعدادات مختلفة
        settings_data = [
            ("العملة الافتراضية:", "default_currency", "ريال سعودي (₪)"),
            ("لغة النظام:", "system_language", "العربية"),
            ("المنطقة الزمنية:", "timezone", "Asia/Riyadh"),
            ("تنسيق التاريخ:", "date_format", "DD/MM/YYYY"),
            ("عدد العناصر في الصفحة:", "items_per_page", "50"),
            ("مدة انتهاء الجلسة (دقيقة):", "session_timeout", "60")
        ]
        
        for i, (label, key, default) in enumerate(settings_data):
            self.create_setting_field(system_settings_frame, label, key, i, default)
        
        # أزرار حفظ إعدادات النظام
        system_buttons_frame = tk.Frame(system_frame, bg='white')
        system_buttons_frame.pack(fill=tk.X, padx=30, pady=20)
        
        save_system_btn = tk.Button(
            system_buttons_frame,
            text="💾 حفظ إعدادات النظام",
            command=self.save_system_settings,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=25,
            pady=12,
            cursor='hand2'
        )
        save_system_btn.pack(side=tk.LEFT)
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(backup_frame, text="💾 النسخ الاحتياطي")
        
        # عنوان النسخ الاحتياطي
        backup_title = tk.Label(
            backup_frame,
            text="💾 إدارة النسخ الاحتياطي",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        backup_title.pack(pady=30)
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = tk.Frame(backup_frame, bg='white')
        backup_buttons_frame.pack(pady=30)
        
        create_backup_btn = tk.Button(
            backup_buttons_frame,
            text="📤 إنشاء نسخة احتياطية",
            command=self.create_backup,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=15,
            cursor='hand2'
        )
        create_backup_btn.pack(pady=10)
        
        restore_backup_btn = tk.Button(
            backup_buttons_frame,
            text="📥 استعادة نسخة احتياطية",
            command=self.restore_backup,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=15,
            cursor='hand2'
        )
        restore_backup_btn.pack(pady=10)
        
        # معلومات النسخ الاحتياطي
        backup_info = tk.Label(
            backup_frame,
            text="💡 يُنصح بإنشاء نسخة احتياطية بشكل دوري لحماية بياناتك\nالنسخ الاحتياطية تشمل جميع البيانات: العملاء، المبيعات، المخزون، والصيانة",
            bg='white',
            fg=self.colors['text_light'],
            font=('Arial', 12),
            justify=tk.CENTER
        )
        backup_info.pack(pady=30)

    def create_license_tab(self):
        """إنشاء تبويب إدارة التراخيص"""
        license_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(license_frame, text="🔐 إدارة التراخيص")

        # عنوان التراخيص
        license_title = tk.Label(
            license_frame,
            text="🔐 مولد مفاتيح التراخيص",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        license_title.pack(pady=30)

        # نموذج توليد الترخيص
        license_form_frame = tk.LabelFrame(
            license_frame,
            text="🏪 معلومات العميل",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        license_form_frame.pack(fill=tk.X, padx=30, pady=20)

        # اسم المحل
        self.create_license_field(license_form_frame, "اسم المحل:", "license_shop_name", 0)

        # اسم المالك
        self.create_license_field(license_form_frame, "اسم المالك:", "license_owner_name", 1)

        # نوع الترخيص
        license_type_label = tk.Label(
            license_form_frame,
            text="نوع الترخيص:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        license_type_label.grid(row=2, column=0, sticky='w', pady=(10, 5), padx=(0, 20))

        self.license_type_var = tk.StringVar(value="full")

        license_type_frame = tk.Frame(license_form_frame, bg='white')
        license_type_frame.grid(row=2, column=1, sticky='w', pady=(10, 5))

        full_radio = tk.Radiobutton(
            license_type_frame,
            text="ترخيص كامل",
            variable=self.license_type_var,
            value="full",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 11),
            selectcolor='white'
        )
        full_radio.pack(side=tk.LEFT, padx=(0, 20))

        trial_radio = tk.Radiobutton(
            license_type_frame,
            text="فترة تجريبية",
            variable=self.license_type_var,
            value="trial",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 11),
            selectcolor='white'
        )
        trial_radio.pack(side=tk.LEFT)

        # أزرار التراخيص
        license_buttons_frame = tk.Frame(license_frame, bg='white')
        license_buttons_frame.pack(pady=30)

        generate_btn = tk.Button(
            license_buttons_frame,
            text="🔑 توليد مفتاح ترخيص",
            command=self.generate_license_key,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=15,
            cursor='hand2'
        )
        generate_btn.pack(pady=10)

        # عرض مفتاح الترخيص
        self.license_key_frame = tk.LabelFrame(
            license_frame,
            text="🔐 مفتاح الترخيص المولد",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=20
        )
        self.license_key_frame.pack(fill=tk.X, padx=30, pady=20)

        self.license_key_display = tk.Text(
            self.license_key_frame,
            height=3,
            width=60,
            font=('Arial', 12, 'bold'),
            relief='solid',
            bd=1,
            wrap=tk.WORD,
            state='disabled'
        )
        self.license_key_display.pack(fill=tk.X, pady=10)

        copy_btn = tk.Button(
            self.license_key_frame,
            text="📋 نسخ المفتاح",
            command=self.copy_license_key,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        copy_btn.pack()

        # معلومات مهمة
        info_frame = tk.Frame(license_frame, bg='#fef3c7', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, padx=30, pady=20)

        info_label = tk.Label(
            info_frame,
            text="⚠️ تحذير مهم:\n• مفاتيح التراخيص مرتبطة بمعرف الجهاز\n• كل مفتاح يعمل على جهاز واحد فقط\n• احتفظ بنسخة من المفاتيح المولدة\n• لا تشارك المفاتيح مع أشخاص غير مخولين",
            bg='#fef3c7',
            fg='#92400e',
            font=('Arial', 11, 'bold'),
            justify=tk.LEFT
        )
        info_label.pack(pady=15, padx=15)

    def create_license_field(self, parent, label_text, var_name, row):
        """إنشاء حقل في نموذج التراخيص"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(10, 5), padx=(0, 20))

        # المتغير
        var = tk.StringVar()
        setattr(self, var_name, var)

        # حقل الإدخال
        entry = tk.Entry(
            parent,
            textvariable=var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=40
        )
        entry.grid(row=row, column=1, sticky='ew', pady=(10, 5))

        parent.grid_columnconfigure(1, weight=1)

    def generate_license_key(self):
        """توليد مفتاح ترخيص جديد"""
        try:
            # التحقق من البيانات
            shop_name = self.license_shop_name.get().strip()
            owner_name = self.license_owner_name.get().strip()
            license_type = self.license_type_var.get()

            if not shop_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المحل")
                return

            if not owner_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المالك")
                return

            # استيراد مدير التراخيص
            try:
                from license_manager import LicenseManager
                license_manager = LicenseManager(self.db_manager)

                # توليد مفتاح الترخيص
                license_key, license_data = license_manager.generate_license_key(
                    shop_name, owner_name, license_type
                )

                if license_key:
                    # عرض المفتاح
                    self.license_key_display.configure(state='normal')
                    self.license_key_display.delete('1.0', tk.END)
                    self.license_key_display.insert('1.0', f"مفتاح الترخيص: {license_key}\n\nمعلومات الترخيص:\n• اسم المحل: {shop_name}\n• اسم المالك: {owner_name}\n• نوع الترخيص: {'كامل' if license_type == 'full' else 'تجريبي'}\n• تاريخ التوليد: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    self.license_key_display.configure(state='disabled')

                    messagebox.showinfo("نجح", f"تم توليد مفتاح الترخيص بنجاح!\n\nالمفتاح: {license_key}")
                else:
                    messagebox.showerror("خطأ", "فشل في توليد مفتاح الترخيص")

            except ImportError:
                messagebox.showerror("خطأ", "مدير التراخيص غير متوفر")

        except Exception as e:
            print(f"خطأ في توليد مفتاح الترخيص: {e}")
            messagebox.showerror("خطأ", "حدث خطأ في توليد مفتاح الترخيص")

    def copy_license_key(self):
        """نسخ مفتاح الترخيص"""
        try:
            content = self.license_key_display.get('1.0', tk.END).strip()
            if content and "مفتاح الترخيص:" in content:
                # استخراج المفتاح فقط
                lines = content.split('\n')
                key_line = lines[0]
                license_key = key_line.replace("مفتاح الترخيص: ", "")

                # نسخ إلى الحافظة
                self.license_key_display.clipboard_clear()
                self.license_key_display.clipboard_append(license_key)

                messagebox.showinfo("تم", "تم نسخ مفتاح الترخيص إلى الحافظة")
            else:
                messagebox.showwarning("تحذير", "لا يوجد مفتاح ترخيص للنسخ")

        except Exception as e:
            print(f"خطأ في نسخ مفتاح الترخيص: {e}")
            messagebox.showerror("خطأ", "فشل في نسخ مفتاح الترخيص")

    def create_setting_field(self, parent, label_text, key, row, default_value=""):
        """إنشاء حقل إعداد"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(10, 5), padx=(0, 20))

        # المتغير
        var = tk.StringVar()
        var.set(default_value)
        setattr(self, f"{key}_var", var)

        # حقل الإدخال
        entry = tk.Entry(
            parent,
            textvariable=var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=40
        )
        entry.grid(row=row, column=1, sticky='ew', pady=(10, 5))

        parent.grid_columnconfigure(1, weight=1)

    def ensure_settings_table(self):
        """التأكد من وجود جدول الإعدادات"""
        try:
            settings_table = '''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''

            self.db_manager.execute_query(settings_table)

        except Exception as e:
            print(f"خطأ في إنشاء جدول الإعدادات: {e}")

    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        try:
            settings = self.db_manager.fetch_all("SELECT setting_key, setting_value FROM settings")

            for setting in settings:
                key = setting['setting_key']
                value = setting['setting_value']

                # تحديث المتغيرات
                if hasattr(self, f"{key}_var"):
                    getattr(self, f"{key}_var").set(value)

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_shop_settings(self):
        """حفظ إعدادات المحل وتطبيقها على البرنامج"""
        try:
            shop_settings = [
                ('shop_name', self.shop_name_var.get()),
                ('shop_address', self.shop_address_var.get()),
                ('shop_phone', self.shop_phone_var.get()),
                ('shop_email', self.shop_email_var.get()),
                ('owner_name', self.owner_name_var.get()),
                ('commercial_register', self.commercial_register_var.get())
            ]

            for key, value in shop_settings:
                self.save_setting(key, value)

            # تطبيق التغييرات على البرنامج
            self.apply_settings_to_app()

            messagebox.showinfo("نجح", "تم حفظ إعدادات المحل بنجاح وتطبيقها على البرنامج")

        except Exception as e:
            print(f"خطأ في حفظ إعدادات المحل: {e}")
            messagebox.showerror("خطأ", "فشل في حفظ إعدادات المحل")

    def apply_settings_to_app(self):
        """تطبيق الإعدادات على البرنامج"""
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            for widget in tk._default_root.winfo_children():
                if hasattr(widget, 'title') and 'Phone Doctor' in widget.title():
                    main_window = widget
                    break

            if main_window:
                # تحديث عنوان النافذة
                shop_name = self.shop_name_var.get() or "Phone Doctor v2.0"
                main_window.title(f"{shop_name} - نظام إدارة محلات صيانة الهواتف")

                # تحديث الشريط العلوي إذا كان موجوداً
                self.update_header_info(main_window)

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات: {e}")

    def update_header_info(self, main_window):
        """تحديث معلومات الشريط العلوي"""
        try:
            # البحث عن الشريط العلوي وتحديثه
            # هذا يتطلب إعادة هيكلة الشريط العلوي
            pass
        except Exception as e:
            print(f"خطأ في تحديث الشريط العلوي: {e}")

    def save_system_settings(self):
        """حفظ إعدادات النظام"""
        try:
            system_settings = [
                ('default_currency', self.default_currency_var.get()),
                ('system_language', self.system_language_var.get()),
                ('timezone', self.timezone_var.get()),
                ('date_format', self.date_format_var.get()),
                ('items_per_page', self.items_per_page_var.get()),
                ('session_timeout', self.session_timeout_var.get())
            ]

            for key, value in system_settings:
                self.save_setting(key, value)

            messagebox.showinfo("نجح", "تم حفظ إعدادات النظام بنجاح")

        except Exception as e:
            print(f"خطأ في حفظ إعدادات النظام: {e}")
            messagebox.showerror("خطأ", "فشل في حفظ إعدادات النظام")

    def save_setting(self, key, value):
        """حفظ إعداد واحد"""
        try:
            # محاولة التحديث أولاً
            result = self.db_manager.execute_query(
                "UPDATE settings SET setting_value = ?, updated_at = ? WHERE setting_key = ?",
                (value, datetime.now(), key)
            )

            # إذا لم يتم التحديث، قم بالإدراج
            if result and result.rowcount == 0:
                self.db_manager.execute_query(
                    "INSERT INTO settings (setting_key, setting_value, updated_at) VALUES (?, ?, ?)",
                    (key, value, datetime.now())
                )

        except Exception as e:
            print(f"خطأ في حفظ الإعداد {key}: {e}")

    def reset_shop_settings(self):
        """إعادة تعيين إعدادات المحل"""
        result = messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع إعدادات المحل للقيم الافتراضية؟")

        if result:
            self.shop_name_var.set("Phone Doctor")
            self.shop_address_var.set("الرياض، المملكة العربية السعودية")
            self.shop_phone_var.set("0566000140")
            self.shop_email_var.set("<EMAIL>")
            self.owner_name_var.set("محمد الشوامرة")
            self.commercial_register_var.set("1234567890")

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # جلب المستخدمين من قاعدة البيانات
            users = self.db_manager.fetch_all("SELECT * FROM users ORDER BY username")

            # إضافة البيانات للجدول
            for user in users:
                # تحديد لون الصف
                if user['role'] == 'admin':
                    tag = 'admin'
                elif user.get('active', 1) == 0:
                    tag = 'inactive'
                else:
                    tag = 'user'

                # تنسيق آخر دخول
                last_login = user.get('last_login', '')
                if last_login:
                    last_login = last_login[:19] if len(last_login) > 19 else last_login
                else:
                    last_login = 'لم يسجل دخول'

                self.users_tree.insert('', 'end', values=(
                    user['id'],
                    user['username'],
                    user['full_name'],
                    'مدير' if user['role'] == 'admin' else 'مستخدم',
                    'نشط' if user.get('active', 1) == 1 else 'غير نشط',
                    last_login
                ), tags=(tag,))

        except Exception as e:
            print(f"خطأ في تحميل المستخدمين: {e}")

    def on_user_select(self, event):
        """معالجة اختيار مستخدم من الجدول"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_user = {
                    'id': values[0],
                    'username': values[1],
                    'full_name': values[2],
                    'role': 'admin' if values[3] == 'مدير' else 'user',
                    'active': 1 if values[4] == 'نشط' else 0
                }

    def add_user(self):
        """إضافة مستخدم جديد"""
        UserForm(self)

    def edit_user(self):
        """تعديل مستخدم محدد"""
        if not self.selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        # جلب البيانات الكاملة
        user_data = self.db_manager.fetch_one(
            "SELECT * FROM users WHERE id = ?",
            (self.selected_user['id'],)
        )

        if user_data:
            UserForm(self, user_data)

    def delete_user(self):
        """حذف مستخدم محدد"""
        if not self.selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return

        # منع حذف المدير الرئيسي
        if self.selected_user['username'] == 'admin':
            messagebox.showerror("خطأ", "لا يمكن حذف المدير الرئيسي")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم:\n{self.selected_user['full_name']}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                # حذف المستخدم من قاعدة البيانات
                self.db_manager.execute_query(
                    "DELETE FROM users WHERE id = ?",
                    (self.selected_user['id'],)
                )

                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.selected_user = None
                self.load_users()

            except Exception as e:
                print(f"خطأ في حذف المستخدم: {e}")
                messagebox.showerror("خطأ", "فشل في حذف المستخدم")

    def change_password(self):
        """تغيير كلمة مرور المستخدم"""
        if not self.selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتغيير كلمة مروره")
            return

        PasswordChangeDialog(self, self.selected_user)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # هنا يمكن إضافة منطق النسخ الاحتياطي الفعلي
            messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية بنجاح\nالموقع: backup_" + datetime.now().strftime("%Y%m%d_%H%M%S") + ".db")
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        result = messagebox.askyesno(
            "تحذير",
            "استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية\nهل أنت متأكد من المتابعة؟"
        )

        if result:
            messagebox.showinfo("معلومات", "سيتم إضافة ميزة استعادة النسخ الاحتياطية قريباً")

class UserForm:
    """نموذج إضافة/تعديل مستخدم"""

    def __init__(self, parent_manager, user_data=None):
        self.parent_manager = parent_manager
        self.user_data = user_data
        self.is_edit_mode = user_data is not None

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد")
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_form()

        if self.is_edit_mode:
            self.fill_form_data()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_form(self):
        """إعداد نموذج المستخدم"""
        # العنوان العصري
        title_frame = tk.Frame(self.window, bg='#7c3aed', height=80)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        title_content = tk.Frame(title_frame, bg='#7c3aed')
        title_content.pack(expand=True, fill=tk.BOTH)

        title_text = "✏️ تعديل مستخدم" if self.is_edit_mode else "➕ إضافة مستخدم جديد"
        title_label = tk.Label(
            title_content,
            text=title_text,
            bg='#7c3aed',
            fg='white',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(expand=True)

        # نموذج البيانات
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # معلومات المستخدم
        user_section = tk.LabelFrame(
            form_frame,
            text="👤 معلومات المستخدم",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        user_section.pack(fill=tk.X, pady=(0, 15))

        # اسم المستخدم
        self.create_field(user_section, "اسم المستخدم:", "username_var", 0)

        # الاسم الكامل
        self.create_field(user_section, "الاسم الكامل:", "full_name_var", 1)

        # كلمة المرور (فقط للإضافة)
        if not self.is_edit_mode:
            self.create_field(user_section, "كلمة المرور:", "password_var", 2, field_type="password")
            self.create_field(user_section, "تأكيد كلمة المرور:", "confirm_password_var", 3, field_type="password")

        # الصلاحيات والحالة
        permissions_section = tk.LabelFrame(
            form_frame,
            text="🔐 الصلاحيات والحالة",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        permissions_section.pack(fill=tk.X, pady=(0, 15))

        # الدور
        self.create_role_field(permissions_section, 0)

        # الحالة
        self.create_status_field(permissions_section, 1)

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=30, pady=20)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ" if self.is_edit_mode else "➕ إضافة",
            command=self.save_user,
            bg='#10b981',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#ef4444',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def create_field(self, parent, label_text, var_name, row, field_type="text"):
        """إنشاء حقل في النموذج"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(5, 5), padx=(0, 10))

        # المتغير
        var = tk.StringVar()
        setattr(self, var_name, var)

        # حقل الإدخال
        entry = tk.Entry(
            parent,
            textvariable=var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=30,
            show='*' if field_type == "password" else None
        )
        entry.grid(row=row, column=1, sticky='ew', pady=(5, 5))

        parent.grid_columnconfigure(1, weight=1)

    def create_role_field(self, parent, row):
        """إنشاء حقل الدور"""
        label = tk.Label(
            parent,
            text="الدور:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(5, 5), padx=(0, 10))

        self.role_var = tk.StringVar(value="user")

        role_frame = tk.Frame(parent, bg='white')
        role_frame.grid(row=row, column=1, sticky='w', pady=(5, 5))

        admin_radio = tk.Radiobutton(
            role_frame,
            text="مدير",
            variable=self.role_var,
            value="admin",
            bg='white',
            fg='#1f2937',
            font=('Arial', 10),
            selectcolor='white'
        )
        admin_radio.pack(side=tk.LEFT, padx=(0, 20))

        user_radio = tk.Radiobutton(
            role_frame,
            text="مستخدم",
            variable=self.role_var,
            value="user",
            bg='white',
            fg='#1f2937',
            font=('Arial', 10),
            selectcolor='white'
        )
        user_radio.pack(side=tk.LEFT)

    def create_status_field(self, parent, row):
        """إنشاء حقل الحالة"""
        label = tk.Label(
            parent,
            text="الحالة:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 11, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(5, 5), padx=(0, 10))

        self.active_var = tk.IntVar(value=1)

        status_frame = tk.Frame(parent, bg='white')
        status_frame.grid(row=row, column=1, sticky='w', pady=(5, 5))

        active_radio = tk.Radiobutton(
            status_frame,
            text="نشط",
            variable=self.active_var,
            value=1,
            bg='white',
            fg='#10b981',
            font=('Arial', 10),
            selectcolor='white'
        )
        active_radio.pack(side=tk.LEFT, padx=(0, 20))

        inactive_radio = tk.Radiobutton(
            status_frame,
            text="غير نشط",
            variable=self.active_var,
            value=0,
            bg='white',
            fg='#ef4444',
            font=('Arial', 10),
            selectcolor='white'
        )
        inactive_radio.pack(side=tk.LEFT)

    def fill_form_data(self):
        """ملء النموذج ببيانات المستخدم للتعديل"""
        if self.user_data:
            self.username_var.set(self.user_data['username'])
            self.full_name_var.set(self.user_data['full_name'])
            self.role_var.set(self.user_data['role'])
            self.active_var.set(self.user_data.get('active', 1))

    def save_user(self):
        """حفظ بيانات المستخدم"""
        # جمع البيانات
        username = self.username_var.get().strip()
        full_name = self.full_name_var.get().strip()
        role = self.role_var.get()
        active = self.active_var.get()

        # التحقق من البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return

        if not full_name:
            messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
            return

        # التحقق من كلمة المرور للمستخدمين الجدد
        if not self.is_edit_mode:
            password = self.password_var.get().strip()
            confirm_password = self.confirm_password_var.get().strip()

            if not password:
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                return

            if len(password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return

            if password != confirm_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقين")
                return

        try:
            if self.is_edit_mode:
                # تحديث المستخدم
                query = """
                    UPDATE users
                    SET username = ?, full_name = ?, role = ?, active = ?
                    WHERE id = ?
                """
                params = (username, full_name, role, active, self.user_data['id'])
                self.parent_manager.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم تحديث بيانات المستخدم بنجاح")
            else:
                # التحقق من عدم تكرار اسم المستخدم
                existing_user = self.parent_manager.db_manager.fetch_one(
                    "SELECT id FROM users WHERE username = ?",
                    (username,)
                )

                if existing_user:
                    messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                    return

                # تشفير كلمة المرور
                password_hash = hashlib.sha256(password.encode()).hexdigest()

                # إضافة مستخدم جديد
                query = """
                    INSERT INTO users (username, password_hash, full_name, role, active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (username, password_hash, full_name, role, active, datetime.now())
                self.parent_manager.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")

            # تحديث قائمة المستخدمين
            self.parent_manager.load_users()
            self.window.destroy()

        except Exception as e:
            print(f"خطأ في حفظ بيانات المستخدم: {e}")
            messagebox.showerror("خطأ", "فشل في حفظ البيانات")

class PasswordChangeDialog:
    """نافذة تغيير كلمة المرور"""

    def __init__(self, parent_manager, user_data):
        self.parent_manager = parent_manager
        self.user_data = user_data

        # إنشاء النافذة
        self.window = tk.Toplevel()
        self.window.title("تغيير كلمة المرور")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        self.window.configure(bg='white')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.grab_set()
        self.window.focus_set()

        self.setup_dialog()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_dialog(self):
        """إعداد نافذة تغيير كلمة المرور"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#7c3aed', height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text="🔑 تغيير كلمة المرور",
            bg='#7c3aed',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        title_label.pack(expand=True)

        # معلومات المستخدم
        info_frame = tk.Frame(self.window, bg='#f8fafc', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, padx=20, pady=20)

        info_content = tk.Frame(info_frame, bg='#f8fafc')
        info_content.pack(fill=tk.X, padx=15, pady=15)

        user_info = tk.Label(
            info_content,
            text=f"👤 المستخدم: {self.user_data['full_name']}\n🔖 اسم المستخدم: {self.user_data['username']}",
            bg='#f8fafc',
            fg='#1f2937',
            font=('Arial', 11),
            justify=tk.LEFT
        )
        user_info.pack(anchor='w')

        # نموذج تغيير كلمة المرور
        form_frame = tk.Frame(self.window, bg='white')
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        # كلمة المرور الجديدة
        new_password_label = tk.Label(
            form_frame,
            text="كلمة المرور الجديدة:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        new_password_label.pack(anchor='w')

        self.new_password_var = tk.StringVar()
        new_password_entry = tk.Entry(
            form_frame,
            textvariable=self.new_password_var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=30,
            show='*'
        )
        new_password_entry.pack(anchor='w', pady=(5, 15))
        new_password_entry.focus()

        # تأكيد كلمة المرور
        confirm_password_label = tk.Label(
            form_frame,
            text="تأكيد كلمة المرور:",
            bg='white',
            fg='#1f2937',
            font=('Arial', 12, 'bold')
        )
        confirm_password_label.pack(anchor='w')

        self.confirm_password_var = tk.StringVar()
        confirm_password_entry = tk.Entry(
            form_frame,
            textvariable=self.confirm_password_var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=30,
            show='*'
        )
        confirm_password_entry.pack(anchor='w', pady=(5, 0))

        # أزرار العمل
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر التغيير
        change_btn = tk.Button(
            buttons_frame,
            text="🔑 تغيير كلمة المرور",
            command=self.change_password,
            bg='#10b981',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        change_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bg='#6b7280',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

    def change_password(self):
        """تغيير كلمة المرور"""
        new_password = self.new_password_var.get().strip()
        confirm_password = self.confirm_password_var.get().strip()

        # التحقق من البيانات
        if not new_password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور الجديدة")
            return

        if len(new_password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        if new_password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقين")
            return

        try:
            # تشفير كلمة المرور الجديدة
            password_hash = hashlib.sha256(new_password.encode()).hexdigest()

            # تحديث كلمة المرور في قاعدة البيانات
            self.parent_manager.db_manager.execute_query(
                "UPDATE users SET password_hash = ? WHERE id = ?",
                (password_hash, self.user_data['id'])
            )

            messagebox.showinfo("نجح", "تم تغيير كلمة المرور بنجاح")
            self.window.destroy()

        except Exception as e:
            print(f"خطأ في تغيير كلمة المرور: {e}")
            messagebox.showerror("خطأ", "فشل في تغيير كلمة المرور")
