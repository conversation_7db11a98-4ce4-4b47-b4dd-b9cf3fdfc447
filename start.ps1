# Phone Doctor - PowerShell Launcher
# المطور: محمد الشوامرة - 0566000140

Clear-Host

Write-Host ""
Write-Host "████████████████████████████████████████████████████████████████" -ForegroundColor Cyan
Write-Host "██                                                            ██" -ForegroundColor Cyan
Write-Host "██                    📱 PHONE DOCTOR AI 📱                   ██" -ForegroundColor White
Write-Host "██                                                            ██" -ForegroundColor Cyan
Write-Host "██              نظام إدارة محلات صيانة الهواتف              ██" -ForegroundColor Yellow
Write-Host "██                                                            ██" -ForegroundColor Cyan
Write-Host "██                المطور: محمد الشوامرة                      ██" -ForegroundColor Green
Write-Host "██                    📞 0566000140                           ██" -ForegroundColor Green
Write-Host "██                                                            ██" -ForegroundColor Cyan
Write-Host "████████████████████████████████████████████████████████████████" -ForegroundColor Cyan
Write-Host ""

Write-Host "🚀 جاري تشغيل النظام..." -ForegroundColor Green
Write-Host ""
Write-Host "🔐 بيانات تسجيل الدخول:" -ForegroundColor Yellow
Write-Host "   👨‍💼 المدير: admin / admin123" -ForegroundColor White
Write-Host "   👨‍💻 المبيعات: sales / sales123" -ForegroundColor White
Write-Host ""
Write-Host "⚡ انتظر حتى تظهر نافذة تسجيل الدخول..." -ForegroundColor Cyan
Write-Host ""

try {
    # تشغيل البرنامج
    python phone_doctor_working.py
    
    Write-Host ""
    Write-Host "✅ تم إغلاق البرنامج بنجاح" -ForegroundColor Green
    Write-Host "👋 شكراً لاستخدام Phone Doctor!" -ForegroundColor Yellow
    Write-Host ""
}
catch {
    Write-Host ""
    Write-Host "❌ حدث خطأ في تشغيل البرنامج" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 تأكد من:" -ForegroundColor Yellow
    Write-Host "   - تثبيت Python على النظام" -ForegroundColor White
    Write-Host "   - وجود جميع ملفات البرنامج" -ForegroundColor White
    Write-Host ""
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

Read-Host "اضغط Enter للخروج"
