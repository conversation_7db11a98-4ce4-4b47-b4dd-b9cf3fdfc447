#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def main():
    print("🚀 تجميع سريع لـ Phone Doctor")
    
    # تثبيت PyInstaller إذا لم يكن موجوداً
    try:
        import PyInstaller
        print("✅ PyInstaller موجود")
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # أمر تجميع بسيط
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=PhoneDoctor',
        'phone_doctor_simple.py'
    ]
    
    print("🔨 تجميع...")
    result = subprocess.run(cmd)
    
    if result.returncode == 0:
        print("✅ تم التجميع بنجاح!")
        print("📁 الملف في: dist/PhoneDoctor.exe")
    else:
        print("❌ فشل التجميع")

if __name__ == "__main__":
    main()
    input("اضغط Enter...")
