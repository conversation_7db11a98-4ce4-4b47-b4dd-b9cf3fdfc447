#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تجميع Phone Doctor النهائي إلى EXE
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_and_install_pyinstaller():
    """التحقق من وتثبيت PyInstaller"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} متوفر")
        return True
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("✅ تم تثبيت PyInstaller")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت PyInstaller")
            return False

def prepare_files():
    """إعداد الملفات للتجميع"""
    print("📋 إعداد الملفات...")
    
    # إنشاء قاعدة بيانات إذا لم تكن موجودة
    if not os.path.exists('phone_doctor.db'):
        try:
            from database.db_manager import DatabaseManager
            db = DatabaseManager('phone_doctor.db')
            db.initialize_database()
            print("✅ تم إنشاء قاعدة البيانات")
        except Exception as e:
            print(f"⚠️ تحذير: {e}")
    
    # إنشاء ملف إعدادات
    if not os.path.exists('config.json'):
        try:
            from utils.config import Config
            config = Config('config.json')
            config.save_config()
            print("✅ تم إنشاء ملف الإعدادات")
        except Exception as e:
            print(f"⚠️ تحذير: {e}")

def build_exe():
    """تجميع الملف التنفيذي"""
    print("🔨 تجميع الملف التنفيذي...")
    
    # تنظيف المجلدات المؤقتة
    for folder in ['build', 'dist']:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🧹 تم حذف مجلد: {folder}")
    
    # أمر PyInstaller المحسن
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # ملف واحد
        '--windowed',                   # بدون نافذة كونسول
        '--name=PhoneDoctor_Modern',    # اسم الملف
        '--clean',                      # تنظيف الملفات المؤقتة
        '--noconfirm',                  # عدم طلب تأكيد
        
        # إضافة المجلدات
        '--add-data=ui;ui',
        '--add-data=database;database',
        '--add-data=utils;utils',
        
        # إضافة الملفات
        '--add-data=phone_doctor.db;.',
        '--add-data=config.json;.',
        
        # الاستيرادات المخفية
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=sqlite3',
        '--hidden-import=datetime',
        '--hidden-import=json',
        '--hidden-import=pathlib',
        
        # استبعاد المكتبات غير المطلوبة
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=pandas',
        '--exclude-module=PIL',
        
        'phone_doctor_simple.py'        # الملف الرئيسي
    ]
    
    try:
        print("⏳ جاري التجميع... (قد يستغرق عدة دقائق)")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم تجميع الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في التجميع:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التجميع: {e}")
        return False

def test_exe():
    """اختبار الملف التنفيذي"""
    exe_path = "dist/PhoneDoctor_Modern.exe"
    
    if not os.path.exists(exe_path):
        print(f"❌ الملف التنفيذي غير موجود: {exe_path}")
        return False
    
    # معلومات الملف
    file_size = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"📏 حجم الملف: {file_size:.1f} MB")
    print(f"📁 مسار الملف: {os.path.abspath(exe_path)}")
    
    # اختبار تشغيل سريع
    print("🧪 اختبار تشغيل سريع...")
    try:
        import time
        process = subprocess.Popen([exe_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        time.sleep(2)  # انتظار ثانيتين
        
        if process.poll() is None:  # العملية ما زالت تعمل
            process.terminate()
            time.sleep(1)
            if process.poll() is None:
                process.kill()
            print("✅ الملف التنفيذي يعمل بشكل صحيح")
            return True
        else:
            print("❌ الملف التنفيذي توقف بشكل غير متوقع")
            return False
            
    except Exception as e:
        print(f"⚠️ تحذير في اختبار التشغيل: {e}")
        return True  # نعتبره نجح حتى لو فشل الاختبار

def create_distribution():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع...")
    
    dist_folder = "PhoneDoctor_EXE_Final"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    
    # نسخ الملف التنفيذي
    exe_source = "dist/PhoneDoctor_Modern.exe"
    exe_dest = os.path.join(dist_folder, "PhoneDoctor_Modern.exe")
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print("✅ تم نسخ الملف التنفيذي")
    else:
        print("❌ الملف التنفيذي غير موجود")
        return False
    
    # إنشاء ملف README
    readme_content = """
🎨 Phone Doctor - النسخة العصرية (EXE)
=======================================

📱 نظام إدارة محلات صيانة الهواتف الاحترافي

👨‍💻 المطور: محمد الشوامرة
📞 الهاتف: 0566000140
📧 البريد: <EMAIL>

🚀 تعليمات التشغيل:
==================

1. انقر نقراً مزدوجاً على PhoneDoctor_Modern.exe
2. لا يحتاج تثبيت Python أو مكتبات إضافية
3. سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول

✨ الميزات:
==========

• تصميم عصري متطور مع ألوان جذابة
• شريط جانبي تفاعلي احترافي
• بطاقات إحصائية ديناميكية
• واجهة عربية كاملة ومتقنة
• قاعدة بيانات SQLite قوية
• نظام نسخ احتياطي آمن
• تحديث تلقائي للبيانات

🔧 استكشاف الأخطاء:
==================

إذا لم يعمل البرنامج:
1. تأكد من وجود صلاحيات الكتابة في المجلد
2. تعطيل مكافح الفيروسات مؤقتاً
3. تشغيل البرنامج كمدير (Run as Administrator)
4. التواصل مع المطور للدعم الفني

⚠️ ملاحظات مهمة:
================

• احتفظ بنسخة احتياطية من قاعدة البيانات بانتظام
• لا تحذف الملفات التي ينشئها البرنامج
• في حالة مشاكل، تواصل مع المطور

📞 الدعم الفني:
==============

محمد الشوامرة: 0566000140
متاح: الأحد - الخميس (9 ص - 6 م)

---
Phone Doctor Modern v1.0.0 - Professional EXE Version
© 2024 Mohammad Shawamreh. All rights reserved.
"""
    
    readme_path = os.path.join(dist_folder, "اقرأني_أولاً.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ تم إنشاء ملف README")
    
    # إنشاء ملف تشغيل سريع
    batch_content = """@echo off
chcp 65001 > nul
title Phone Doctor Modern - النسخة العصرية

echo.
echo ==========================================
echo   📱 Phone Doctor Modern - النسخة العصرية
echo   نظام إدارة محلات صيانة الهواتف
echo   المطور: محمد الشوامرة - 0566000140
echo ==========================================
echo.

echo 🚀 تشغيل النسخة العصرية...
echo.

PhoneDoctor_Modern.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج!
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تشغيل البرنامج كمدير
    echo 2. تعطيل مكافح الفيروسات مؤقتاً
    echo 3. التأكد من صلاحيات الكتابة
    echo 4. التواصل مع المطور: 0566000140
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح.
echo 🙏 شكراً لاستخدام Phone Doctor!
pause
"""
    
    batch_path = os.path.join(dist_folder, "تشغيل_البرنامج.bat")
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    print(f"🎉 تم إنشاء حزمة التوزيع في: {dist_folder}")
    return True

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_items = ['build', '__pycache__']
    for item in temp_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"✅ تم حذف: {item}")

def main():
    """الدالة الرئيسية"""
    print("🚀 تجميع Phone Doctor النهائي إلى EXE")
    print("=" * 60)
    
    try:
        # التحقق من PyInstaller
        if not check_and_install_pyinstaller():
            return False
        
        # إعداد الملفات
        prepare_files()
        
        # تجميع EXE
        if not build_exe():
            return False
        
        # اختبار EXE
        if not test_exe():
            print("⚠️ تحذير: قد تكون هناك مشاكل في الملف التنفيذي")
        
        # إنشاء حزمة التوزيع
        if not create_distribution():
            return False
        
        # تنظيف الملفات المؤقتة
        cleanup()
        
        print("\n" + "=" * 60)
        print("🎉 تم تجميع Phone Doctor بنجاح!")
        print("📁 ستجد الملف التنفيذي في: PhoneDoctor_EXE_Final")
        print("🚀 يمكنك الآن تشغيل PhoneDoctor_Modern.exe")
        print("📋 اقرأ ملف 'اقرأني_أولاً.txt' للتعليمات")
        print("👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في عملية التجميع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ نجح التجميع!' if success else '❌ فشل التجميع!'}")
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
