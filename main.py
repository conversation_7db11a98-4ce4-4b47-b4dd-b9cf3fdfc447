#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة محلات صيانة الهواتف
Phone Doctor - Professional Phone Repair Shop Management System

المطور: محمد الشوامرة
الهاتف: 0566000140
جميع الحقوق محفوظة © 2024
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

# Import custom modules
from database.db_manager import DatabaseManager
from ui.main_window import MainWindow
from ui.splash_screen import SplashScreen
from utils.config import Config

class PhoneDoctorApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("Phone Doctor")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("<PERSON>")
        
        # Set application font
        font = QFont("Segoe UI", 10)
        self.app.setFont(font)
        
        # Initialize database
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()
        
        # Initialize configuration
        self.config = Config()
        
        # Show splash screen
        self.splash = SplashScreen()
        self.splash.show()
        
        # Initialize main window after splash
        QTimer.singleShot(3000, self.show_main_window)
        
    def show_main_window(self):
        self.splash.close()
        self.main_window = MainWindow(self.db_manager, self.config)
        self.main_window.show()
        
    def run(self):
        return self.app.exec_()

if __name__ == "__main__":
    app = PhoneDoctorApp()
    sys.exit(app.run())
