#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة العملاء المتطورة - Phone Doctor v2.0
Advanced Customers Management System with Financial Tracking

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.theme_manager import ThemeManager

class CustomersManager:
    """مدير العملاء المتطور مع تتبع الديون والأرباح"""
    
    def __init__(self, parent_frame, db_manager, current_user=None):
        print("👥 تهيئة مدير العملاء المتطور...")
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.current_user = current_user or {'username': 'admin', 'full_name': 'المدير'}
        self.selected_customer = None
        
        # تهيئة مدير الثيمات
        self.theme = ThemeManager()
        
        # متغيرات الحالة
        self.current_filter = 'all'
        self.current_sort = 'newest'
        self.is_loading = False
        
        self.setup_ui()
        self.ensure_customers_table()
        self.load_customers()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتطورة"""
        # تنظيف الإطار
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # تطبيق النمط العصري
        self.parent_frame.configure(bg=self.theme.colors['bg_primary'])
        
        # الهيدر المتطور مع تدرج لوني
        header_frame = self.theme.create_gradient_frame(
            self.parent_frame, 
            self.theme.colors['gradient_info'], 
            height=100
        )
        header_frame.pack(fill=tk.X)
        
        # محتوى الهيدر
        header_content = tk.Frame(header_frame, bg='transparent')
        header_content.place(relx=0.5, rely=0.5, anchor='center')
        
        # أيقونة وعنوان
        title_frame = tk.Frame(header_content, bg='transparent')
        title_frame.pack()
        
        icon_label = tk.Label(
            title_frame,
            text=self.theme.icons['customers'],
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=('Arial', 32)
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        title_label = tk.Label(
            title_frame,
            text="إدارة العملاء والحسابات المالية",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['title_large']
        )
        title_label.pack(side=tk.LEFT)
        
        # معلومات المستخدم
        user_info = tk.Label(
            header_content,
            text=f"{self.theme.icons['user']} {self.current_user.get('full_name', 'المستخدم')}",
            bg='transparent',
            fg=self.theme.colors['text_white'],
            font=self.theme.fonts['body_medium']
        )
        user_info.pack(pady=(10, 0))
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent_frame, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
        
        # بطاقة الإحصائيات المالية
        stats_card, stats_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['money']} الإحصائيات المالية"
        )
        stats_card.pack(fill=tk.X, pady=(0, 20))
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(stats_content, bg=self.theme.colors['bg_card'])
        stats_frame.pack(fill=tk.X)
        
        # إحصائيات مختلفة
        self.stats_labels = {}
        stats_data = [
            ("total_customers", "إجمالي العملاء", "info", "customers"),
            ("total_debt", "إجمالي الديون", "danger", "money"),
            ("total_paid", "إجمالي المدفوع", "success", "money"),
            ("net_profit", "صافي الربح", "primary", "star"),
        ]
        
        for i, (key, label, style, icon) in enumerate(stats_data):
            stat_frame = tk.Frame(stats_frame, bg=self.theme.colors['bg_card'])
            stat_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10 if i < len(stats_data)-1 else 0))
            
            # أيقونة الإحصائية
            icon_label = tk.Label(
                stat_frame,
                text=self.theme.icons[icon],
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors[style],
                font=('Arial', 20)
            )
            icon_label.pack()
            
            # قيمة الإحصائية
            value_label = tk.Label(
                stat_frame,
                text="0",
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_primary'],
                font=self.theme.fonts['title_medium']
            )
            value_label.pack()
            
            # تسمية الإحصائية
            name_label = tk.Label(
                stat_frame,
                text=label,
                bg=self.theme.colors['bg_card'],
                fg=self.theme.colors['text_secondary'],
                font=self.theme.fonts['body_small']
            )
            name_label.pack()
            
            self.stats_labels[key] = value_label
        
        # بطاقة شريط الأدوات
        toolbar_card, toolbar_content = self.theme.create_modern_card(
            main_frame, 
            title=f"{self.theme.icons['settings']} أدوات إدارة العملاء"
        )
        toolbar_card.pack(fill=tk.X, pady=(0, 20))
        
        # الصف الأول من الأزرار
        buttons_row1 = tk.Frame(toolbar_content, bg=self.theme.colors['bg_card'])
        buttons_row1.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار العمليات الأساسية
        add_btn = self.theme.create_modern_button(
            buttons_row1,
            "إضافة عميل",
            command=self.add_customer,
            style='success',
            size='medium',
            icon='add'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        edit_btn = self.theme.create_modern_button(
            buttons_row1,
            "تعديل",
            command=self.edit_customer,
            style='info',
            size='medium',
            icon='edit'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        delete_btn = self.theme.create_modern_button(
            buttons_row1,
            "حذف",
            command=self.delete_customer,
            style='danger',
            size='medium',
            icon='delete'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # الصف الثاني من الأزرار
        buttons_row2 = tk.Frame(toolbar_content, bg=self.theme.colors['bg_card'])
        buttons_row2.pack(fill=tk.X)
        
        refresh_btn = self.theme.create_modern_button(
            buttons_row2,
            "تحديث",
            command=self.load_customers,
            style='warning',
            size='medium',
            icon='refresh'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        payments_btn = self.theme.create_modern_button(
            buttons_row2,
            "إدارة المدفوعات",
            command=self.manage_payments,
            style='primary',
            size='medium',
            icon='money'
        )
        payments_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        debt_btn = self.theme.create_modern_button(
            buttons_row2,
            "تسديد دين",
            command=self.pay_debt,
            style='success',
            size='medium',
            icon='money'
        )
        debt_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        report_btn = self.theme.create_modern_button(
            buttons_row2,
            "تقرير مالي",
            command=self.generate_financial_report,
            style='secondary',
            size='medium',
            icon='export'
        )
        report_btn.pack(side=tk.LEFT)
        
        # بطاقة البحث والفلترة
        search_card, search_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['search']} البحث والفلترة المتقدمة"
        )
        search_card.pack(fill=tk.X, pady=(0, 20))
        
        # البحث الرئيسي
        search_row = tk.Frame(search_content, bg=self.theme.colors['bg_card'])
        search_row.pack(fill=tk.X, pady=(0, 15))
        
        search_label = tk.Label(
            search_row,
            text=f"{self.theme.icons['search']} البحث:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['heading_small']
        )
        search_label.pack(side=tk.LEFT, padx=(0, 15))
        
        search_input_frame, self.search_entry = self.theme.create_modern_input(
            search_row,
            placeholder="ابحث في الاسم، الهاتف، البريد الإلكتروني...",
            size='large'
        )
        search_input_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        
        search_btn = self.theme.create_modern_button(
            search_row,
            "بحث",
            command=self.perform_search,
            style='primary',
            size='medium',
            icon='search'
        )
        search_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = self.theme.create_modern_button(
            search_row,
            "مسح",
            command=self.clear_search,
            style='secondary',
            size='medium',
            icon='cancel'
        )
        clear_btn.pack(side=tk.LEFT)
        
        # الفلاتر
        filters_row = tk.Frame(search_content, bg=self.theme.colors['bg_card'])
        filters_row.pack(fill=tk.X)
        
        # فلتر حالة الدين
        debt_label = tk.Label(
            filters_row,
            text=f"{self.theme.icons['money']} حالة الدين:",
            bg=self.theme.colors['bg_card'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        debt_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.debt_filter_var = tk.StringVar(value="الكل")
        debt_combo = ttk.Combobox(
            filters_row,
            textvariable=self.debt_filter_var,
            values=["الكل", "لا يوجد دين", "دين مستحق", "دين متأخر"],
            font=self.theme.fonts['body_medium'],
            width=12,
            state='readonly'
        )
        debt_combo.pack(side=tk.LEFT, padx=(0, 20))
        debt_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # ربط البحث الفوري
        self.search_var = tk.StringVar()
        self.search_entry.configure(textvariable=self.search_var)
        self.search_var.trace('w', self.on_search_change)

        # بطاقة جدول العملاء
        table_card, table_content = self.theme.create_modern_card(
            main_frame,
            title=f"{self.theme.icons['customers']} قائمة العملاء والحسابات المالية"
        )
        table_card.pack(fill=tk.BOTH, expand=True)

        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(table_content, bg=self.theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الجدول المتطور
        columns = ("ID", "الاسم", "الهاتف", "البريد الإلكتروني", "إجمالي المشتريات", "المدفوع", "الدين", "صافي الربح", "تاريخ التسجيل")
        self.tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15,
            style='Modern.Treeview'
        )

        # تعيين العناوين والعرض
        column_widths = [60, 150, 120, 180, 120, 120, 120, 120, 120]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=f"{self.get_column_icon(col)} {col}")
            self.tree.column(col, width=column_widths[i], anchor='center')

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # تلوين الصفوف حسب حالة الدين
        self.tree.tag_configure('no_debt', background='#d1fae5', foreground='#065f46')
        self.tree.tag_configure('has_debt', background='#fef3c7', foreground='#92400e')
        self.tree.tag_configure('overdue_debt', background='#fee2e2', foreground='#991b1b')
        self.tree.tag_configure('vip_customer', background='#ede9fe', foreground='#5b21b6')

        # ربط أحداث الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_select)
        self.tree.bind('<Double-1>', self.view_customer_details)
        self.tree.bind('<Button-3>', self.show_context_menu)

        # شريط الحالة المتطور
        status_frame = tk.Frame(
            self.parent_frame,
            bg=self.theme.colors['bg_tertiary'],
            height=50
        )
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)

        # محتوى شريط الحالة
        status_content = tk.Frame(status_frame, bg=self.theme.colors['bg_tertiary'])
        status_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # حالة النظام
        self.status_label = tk.Label(
            status_content,
            text=f"{self.theme.icons['success']} النظام جاهز",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_primary'],
            font=self.theme.fonts['body_medium']
        )
        self.status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        info_frame = tk.Frame(status_content, bg=self.theme.colors['bg_tertiary'])
        info_frame.pack(side=tk.RIGHT)

        # وقت آخر تحديث
        self.last_update_label = tk.Label(
            info_frame,
            text=f"{self.theme.icons['clock']} آخر تحديث: --",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_secondary'],
            font=self.theme.fonts['body_small']
        )
        self.last_update_label.pack(side=tk.RIGHT, padx=(20, 0))

        # عداد النتائج
        self.count_label = tk.Label(
            info_frame,
            text="",
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['primary'],
            font=self.theme.fonts['body_medium']
        )
        self.count_label.pack(side=tk.RIGHT, padx=(20, 0))

    def get_column_icon(self, column):
        """الحصول على أيقونة العمود"""
        icons = {
            "ID": self.theme.icons['info'],
            "الاسم": self.theme.icons['customer'],
            "الهاتف": self.theme.icons['phone_number'],
            "البريد الإلكتروني": self.theme.icons['email'],
            "إجمالي المشتريات": self.theme.icons['money'],
            "المدفوع": self.theme.icons['success'],
            "الدين": self.theme.icons['warning'],
            "صافي الربح": self.theme.icons['star'],
            "تاريخ التسجيل": self.theme.icons['calendar']
        }
        return icons.get(column, "")

    def ensure_customers_table(self):
        """التأكد من وجود جدول العملاء"""
        try:
            print("🔨 التحقق من جدول العملاء...")

            # إنشاء جدول العملاء
            customers_table = '''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT NOT NULL UNIQUE,
                    email TEXT,
                    address TEXT,
                    customer_type TEXT DEFAULT 'عميل عادي',
                    total_purchases REAL DEFAULT 0,
                    total_paid REAL DEFAULT 0,
                    total_debt REAL DEFAULT 0,
                    net_profit REAL DEFAULT 0,
                    last_purchase_date TIMESTAMP,
                    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    is_vip BOOLEAN DEFAULT 0,
                    credit_limit REAL DEFAULT 0,
                    created_by TEXT,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''

            # إنشاء جدول المعاملات المالية
            transactions_table = '''
                CREATE TABLE IF NOT EXISTS customer_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER NOT NULL,
                    transaction_type TEXT NOT NULL, -- 'purchase', 'payment', 'refund'
                    amount REAL NOT NULL,
                    description TEXT,
                    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reference_id INTEGER, -- ربط بأمر صيانة أو مبيعات
                    reference_type TEXT, -- 'repair', 'sale'
                    created_by TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            '''

            self.db_manager.execute_query(customers_table)
            self.db_manager.execute_query(transactions_table)
            print("✅ تم التحقق من جداول العملاء والمعاملات")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جداول العملاء: {e}")

    def load_customers(self):
        """تحميل قائمة العملاء مع الحسابات المالية"""
        try:
            print("🔄 تحميل بيانات العملاء...")
            self.is_loading = True
            self.update_status(f"{self.theme.icons['loading']} جاري تحميل البيانات...", "info")

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # الاتصال المباشر بقاعدة البيانات
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = """
                SELECT
                    c.*,
                    COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END), 0) as calculated_purchases,
                    COALESCE(SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as calculated_paid,
                    COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END) -
                             SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as calculated_debt
                FROM customers c
                LEFT JOIN customer_transactions ct ON c.id = ct.customer_id
                GROUP BY c.id
                ORDER BY c.registration_date DESC
            """

            cursor.execute(query)
            customers = cursor.fetchall()
            conn.close()

            print(f"📊 تم جلب {len(customers)} عميل")

            # حساب الإحصائيات
            stats = {
                'total_customers': len(customers),
                'total_debt': 0,
                'total_paid': 0,
                'net_profit': 0
            }

            # إضافة البيانات للجدول وحساب الإحصائيات
            for customer in customers:
                registration_date = customer['registration_date'][:10] if customer['registration_date'] else 'غير محدد'

                # حساب القيم المالية
                total_purchases = customer['calculated_purchases'] or 0
                total_paid = customer['calculated_paid'] or 0
                total_debt = customer['calculated_debt'] or 0
                net_profit = total_paid * 0.3  # افتراض هامش ربح 30%

                # تحديد التاغ للتلوين
                tag = self.get_customer_tag(total_debt, customer.get('is_vip', 0))

                # إضافة للجدول
                self.tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'],
                    customer['email'] or 'غير محدد',
                    f"{total_purchases:.2f} ₪",
                    f"{total_paid:.2f} ₪",
                    f"{total_debt:.2f} ₪" if total_debt > 0 else "لا يوجد",
                    f"{net_profit:.2f} ₪",
                    registration_date
                ), tags=(tag,))

                # تحديث الإحصائيات
                stats['total_debt'] += total_debt
                stats['total_paid'] += total_paid
                stats['net_profit'] += net_profit

            # تحديث الإحصائيات في الواجهة
            self.update_statistics(stats)

            # تحديث العداد وشريط الحالة
            self.count_label.configure(text=f"{self.theme.icons['customers']} إجمالي: {len(customers)} عميل")
            self.update_status(f"{self.theme.icons['success']} تم تحميل {len(customers)} عميل بنجاح", "success")

            # تحديث وقت آخر تحديث
            current_time = datetime.now().strftime("%H:%M:%S")
            self.last_update_label.configure(text=f"{self.theme.icons['clock']} آخر تحديث: {current_time}")

            self.is_loading = False

        except Exception as e:
            print(f"❌ خطأ في تحميل العملاء: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في تحميل البيانات: {str(e)}", "error")
            self.is_loading = False

    def get_customer_tag(self, debt, is_vip):
        """تحديد تاغ العميل للتلوين"""
        if is_vip:
            return 'vip_customer'
        elif debt > 1000:  # دين كبير
            return 'overdue_debt'
        elif debt > 0:
            return 'has_debt'
        else:
            return 'no_debt'

    def update_statistics(self, stats):
        """تحديث الإحصائيات في الواجهة"""
        self.stats_labels['total_customers'].configure(text=str(stats['total_customers']))
        self.stats_labels['total_debt'].configure(text=f"{stats['total_debt']:.2f} ₪")
        self.stats_labels['total_paid'].configure(text=f"{stats['total_paid']:.2f} ₪")
        self.stats_labels['net_profit'].configure(text=f"{stats['net_profit']:.2f} ₪")

    def update_status(self, message, status_type="info"):
        """تحديث شريط الحالة"""
        icons = {
            "success": self.theme.icons['success'],
            "error": self.theme.icons['error'],
            "warning": self.theme.icons['warning'],
            "info": self.theme.icons['info'],
            "loading": self.theme.icons['loading']
        }

        colors = {
            "success": self.theme.colors['success'],
            "error": self.theme.colors['danger'],
            "warning": self.theme.colors['warning'],
            "info": self.theme.colors['info'],
            "loading": self.theme.colors['secondary']
        }

        icon = icons.get(status_type, self.theme.icons['info'])
        color = colors.get(status_type, self.theme.colors['text_primary'])

        self.status_label.configure(
            text=f"{icon} {message}",
            fg=color
        )

    def on_search_change(self, *args):
        """معالجة تغيير البحث الفوري"""
        if not self.is_loading:
            self.parent_frame.after(300, self.perform_search)

    def on_filter_change(self, event=None):
        """معالجة تغيير الفلاتر"""
        if not self.is_loading:
            self.perform_search()

    def perform_search(self):
        """تنفيذ البحث والفلترة"""
        if self.is_loading:
            return

        try:
            search_term = self.search_var.get().strip().lower()
            debt_filter = self.debt_filter_var.get()

            self.update_status(f"{self.theme.icons['search']} جاري البحث...", "loading")

            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # بناء الاستعلام
            query = """
                SELECT
                    c.*,
                    COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END), 0) as calculated_purchases,
                    COALESCE(SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as calculated_paid,
                    COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END) -
                             SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as calculated_debt
                FROM customers c
                LEFT JOIN customer_transactions ct ON c.id = ct.customer_id
                WHERE 1=1
            """
            params = []

            # إضافة شرط البحث
            if search_term:
                query += """ AND (
                    LOWER(c.name) LIKE ? OR
                    LOWER(c.phone) LIKE ? OR
                    LOWER(c.email) LIKE ? OR
                    LOWER(c.address) LIKE ?
                )"""
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern] * 4)

            query += " GROUP BY c.id"

            # إضافة فلتر الدين
            if debt_filter and debt_filter != "الكل":
                if debt_filter == "لا يوجد دين":
                    query += " HAVING calculated_debt <= 0"
                elif debt_filter == "دين مستحق":
                    query += " HAVING calculated_debt > 0 AND calculated_debt <= 1000"
                elif debt_filter == "دين متأخر":
                    query += " HAVING calculated_debt > 1000"

            query += " ORDER BY c.registration_date DESC"

            # تنفيذ الاستعلام
            conn = sqlite3.connect('database/phone_doctor.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(query, params)
            customers = cursor.fetchall()
            conn.close()

            # إضافة النتائج للجدول
            for customer in customers:
                registration_date = customer['registration_date'][:10] if customer['registration_date'] else 'غير محدد'

                total_purchases = customer['calculated_purchases'] or 0
                total_paid = customer['calculated_paid'] or 0
                total_debt = customer['calculated_debt'] or 0
                net_profit = total_paid * 0.3

                tag = self.get_customer_tag(total_debt, customer.get('is_vip', 0))

                self.tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'],
                    customer['email'] or 'غير محدد',
                    f"{total_purchases:.2f} ₪",
                    f"{total_paid:.2f} ₪",
                    f"{total_debt:.2f} ₪" if total_debt > 0 else "لا يوجد",
                    f"{net_profit:.2f} ₪",
                    registration_date
                ), tags=(tag,))

            # تحديث العداد
            self.count_label.configure(text=f"{self.theme.icons['search']} نتائج البحث: {len(customers)}")

            if len(customers) > 0:
                self.update_status(f"{self.theme.icons['success']} تم العثور على {len(customers)} عميل", "success")
            else:
                self.update_status(f"{self.theme.icons['warning']} لم يتم العثور على نتائج", "warning")

        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            self.update_status(f"{self.theme.icons['error']} خطأ في البحث: {str(e)}", "error")

    def clear_search(self):
        """مسح البحث والفلاتر"""
        self.search_var.set("")
        self.debt_filter_var.set("الكل")
        self.load_customers()

    def on_select(self, event):
        """معالجة اختيار عميل من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_customer = {
                    'id': values[0],
                    'name': values[1],
                    'phone': values[2],
                    'email': values[3],
                    'total_purchases': values[4],
                    'total_paid': values[5],
                    'total_debt': values[6],
                    'net_profit': values[7]
                }
                self.update_status(f"{self.theme.icons['customer']} تم اختيار العميل: {values[1]}", "info")

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            item = self.tree.identify_row(event.y)
            if item:
                self.tree.selection_set(item)
                self.on_select(None)

                context_menu = tk.Menu(self.parent_frame, tearoff=0)
                context_menu.add_command(
                    label=f"{self.theme.icons['info']} عرض التفاصيل",
                    command=self.view_customer_details
                )
                context_menu.add_command(
                    label=f"{self.theme.icons['money']} إدارة المدفوعات",
                    command=self.manage_payments
                )
                context_menu.add_command(
                    label=f"{self.theme.icons['edit']} تعديل",
                    command=self.edit_customer
                )
                context_menu.add_separator()
                context_menu.add_command(
                    label=f"{self.theme.icons['delete']} حذف",
                    command=self.delete_customer
                )

                context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في قائمة السياق: {e}")

    def add_customer(self):
        """إضافة عميل جديد"""
        self.update_status(f"{self.theme.icons['info']} ميزة إضافة العملاء قيد التطوير", "info")

    def edit_customer(self):
        """تعديل عميل"""
        if not self.selected_customer:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار عميل للتعديل", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة تعديل العملاء قيد التطوير", "info")

    def delete_customer(self):
        """حذف عميل"""
        if not self.selected_customer:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار عميل للحذف", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة حذف العملاء قيد التطوير", "info")

    def view_customer_details(self, event=None):
        """عرض تفاصيل العميل"""
        if not self.selected_customer:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار عميل لعرض التفاصيل", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة عرض تفاصيل العملاء قيد التطوير", "info")

    def manage_payments(self):
        """إدارة المدفوعات"""
        if not self.selected_customer:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار عميل لإدارة المدفوعات", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة إدارة المدفوعات قيد التطوير", "info")

    def pay_debt(self):
        """تسديد دين"""
        if not self.selected_customer:
            self.update_status(f"{self.theme.icons['warning']} يرجى اختيار عميل لتسديد الدين", "warning")
            return

        self.update_status(f"{self.theme.icons['info']} ميزة تسديد الديون قيد التطوير", "info")

    def generate_financial_report(self):
        """إنشاء تقرير مالي"""
        self.update_status(f"{self.theme.icons['info']} ميزة التقارير المالية قيد التطوير", "info")
