#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 Final Edition
النسخة النهائية مع البيانات الحقيقية والتأثيرات

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import sys
import os
from datetime import datetime

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = 'database/phone_doctor.db'
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if not os.path.exists('database'):
                os.makedirs('database')
            
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة المستخدمين الافتراضيين
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('admin', 'admin123', 'مدير النظام', 'admin'))
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('sales', 'sales123', 'موظف المبيعات', 'sales'))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False

class AuthManager:
    """مدير المصادقة"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.current_user = None
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            users = self.db_manager.fetch_all('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                                            (username, password))
            
            if users:
                user = users[0]
                self.current_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'role': user['role']
                }
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

class LoginWindow:
    """نافذة تسجيل الدخول المحسنة"""

    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.root = None
        self.is_loading = False

    def show(self):
        """عرض نافذة تسجيل الدخول المحسنة"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - تسجيل الدخول")
        self.root.geometry("600x700")
        self.root.configure(bg='#0f172a')
        self.root.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # إنشاء الخلفية المتدرجة
        self.create_gradient_background()

        # إنشاء المحتوى الرئيسي
        self.create_main_content()

        self.root.mainloop()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_gradient_background(self):
        """إنشاء خلفية متدرجة"""
        # الخلفية الرئيسية
        bg_frame = tk.Frame(self.root, bg='#0f172a')
        bg_frame.pack(fill=tk.BOTH, expand=True)

        # إضافة تأثير التدرج (محاكاة)
        gradient_colors = ['#0f172a', '#1e293b', '#334155', '#475569']
        for i, color in enumerate(gradient_colors):
            stripe = tk.Frame(bg_frame, bg=color, height=175)
            stripe.pack(fill=tk.X)

        self.main_container = bg_frame

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # حاوية المحتوى الرئيسية
        content_frame = tk.Frame(self.main_container, bg='#0f172a')
        content_frame.place(relx=0.5, rely=0.5, anchor='center')

        # شعار التطبيق
        self.create_app_logo(content_frame)

        # بطاقة تسجيل الدخول
        self.create_login_card(content_frame)

        # معلومات تسجيل الدخول
        self.create_login_info(content_frame)

        # معلومات المطور
        self.create_developer_info(content_frame)

    def create_app_logo(self, parent):
        """إنشاء شعار التطبيق"""
        logo_frame = tk.Frame(parent, bg='#0f172a')
        logo_frame.pack(pady=(0, 30))

        # أيقونة التطبيق (رمز الهاتف)
        icon_label = tk.Label(
            logo_frame,
            text="📱",
            bg='#0f172a',
            font=('Arial', 48)
        )
        icon_label.pack()

        # اسم التطبيق
        app_name = tk.Label(
            logo_frame,
            text="Phone Doctor v2.0",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 28, 'bold')
        )
        app_name.pack(pady=(10, 0))

        # العنوان الفرعي
        subtitle = tk.Label(
            logo_frame,
            text="Final Professional Edition",
            bg='#0f172a',
            fg='#94a3b8',
            font=('Arial', 14, 'italic')
        )
        subtitle.pack(pady=(5, 0))

        # خط فاصل
        separator = tk.Frame(logo_frame, bg='#374151', height=2, width=300)
        separator.pack(pady=(15, 0))

    def create_login_card(self, parent):
        """إنشاء بطاقة تسجيل الدخول"""
        # بطاقة تسجيل الدخول
        card_frame = tk.Frame(parent, bg='#1e293b', relief='raised', bd=3)
        card_frame.pack(pady=20, padx=20)

        # تأثير الظل (محاكاة)
        shadow_frame = tk.Frame(parent, bg='#0f172a', height=3)
        shadow_frame.pack(fill=tk.X, padx=25)

        # عنوان البطاقة
        card_title = tk.Label(
            card_frame,
            text="🔐 تسجيل الدخول",
            bg='#1e293b',
            fg='#fbbf24',
            font=('Arial', 18, 'bold')
        )
        card_title.pack(pady=(25, 20))

        # حقل اسم المستخدم
        self.create_input_field(card_frame, "👤 اسم المستخدم:", "username")

        # حقل كلمة المرور
        self.create_input_field(card_frame, "🔒 كلمة المرور:", "password", is_password=True)

        # أزرار العمليات
        self.create_action_buttons(card_frame)

    def create_input_field(self, parent, label_text, field_type, is_password=False):
        """إنشاء حقل إدخال محسن"""
        # إطار الحقل
        field_frame = tk.Frame(parent, bg='#1e293b')
        field_frame.pack(pady=10, padx=30, fill=tk.X)

        # تسمية الحقل
        label = tk.Label(
            field_frame,
            text=label_text,
            bg='#1e293b',
            fg='white',
            font=('Arial', 12, 'bold'),
            anchor='w'
        )
        label.pack(fill=tk.X, pady=(0, 5))

        # إطار الإدخال مع تأثيرات
        entry_container = tk.Frame(field_frame, bg='#374151', relief='flat', bd=2)
        entry_container.pack(fill=tk.X, ipady=2)

        # حقل الإدخال
        entry = tk.Entry(
            entry_container,
            font=('Arial', 14),
            bg='#4b5563',
            fg='white',
            relief='flat',
            bd=0,
            insertbackground='#60a5fa',
            show="*" if is_password else ""
        )
        entry.pack(fill=tk.X, padx=10, pady=8)

        # حفظ المرجع
        if field_type == "username":
            self.username_entry = entry
        else:
            self.password_entry = entry

        # تأثيرات التفاعل
        self.add_entry_effects(entry, entry_container)

    def add_entry_effects(self, entry, container):
        """إضافة تأثيرات للحقول"""
        def on_focus_in(event):
            container.configure(bg='#60a5fa', bd=2)
            entry.configure(bg='#374151')

        def on_focus_out(event):
            container.configure(bg='#374151', bd=2)
            entry.configure(bg='#4b5563')

        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)

    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = tk.Frame(parent, bg='#1e293b')
        buttons_frame.pack(pady=(20, 30), padx=30, fill=tk.X)

        # زر تسجيل الدخول الرئيسي
        self.login_btn = tk.Button(
            buttons_frame,
            text="🚀 دخول",
            command=self.enhanced_login,
            bg='#3b82f6',
            fg='white',
            font=('Arial', 16, 'bold'),
            relief='flat',
            bd=0,
            padx=40,
            pady=12,
            cursor='hand2'
        )
        self.login_btn.pack(fill=tk.X, pady=(0, 10))

        # تأثيرات الزر
        self.add_button_effects(self.login_btn, '#2563eb', '#3b82f6')

        # أزرار سريعة
        quick_buttons_frame = tk.Frame(buttons_frame, bg='#1e293b')
        quick_buttons_frame.pack(fill=tk.X)

        # زر دخول سريع للمدير
        admin_btn = tk.Button(
            quick_buttons_frame,
            text="👑 دخول المدير",
            command=lambda: self.quick_login('admin', 'admin123'),
            bg='#10b981',
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        admin_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # زر دخول سريع للمبيعات
        sales_btn = tk.Button(
            quick_buttons_frame,
            text="💼 دخول المبيعات",
            command=lambda: self.quick_login('sales', 'sales123'),
            bg='#f59e0b',
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        sales_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))

        # تأثيرات الأزرار السريعة
        self.add_button_effects(admin_btn, '#059669', '#10b981')
        self.add_button_effects(sales_btn, '#d97706', '#f59e0b')

        # ربط Enter بتسجيل الدخول
        self.username_entry.focus()
        self.root.bind('<Return>', lambda e: self.enhanced_login())

    def add_button_effects(self, button, hover_color, normal_color):
        """إضافة تأثيرات للأزرار"""
        def on_enter(event):
            if not self.is_loading:
                button.configure(bg=hover_color, relief='raised', bd=2)

        def on_leave(event):
            if not self.is_loading:
                button.configure(bg=normal_color, relief='flat', bd=0)

        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)

    def create_login_info(self, parent):
        """إنشاء معلومات تسجيل الدخول"""
        info_frame = tk.Frame(parent, bg='#0f172a', relief='raised', bd=2)
        info_frame.pack(pady=15, padx=20, fill=tk.X)

        # عنوان المعلومات
        info_title = tk.Label(
            info_frame,
            text="ℹ️ معلومات تسجيل الدخول",
            bg='#0f172a',
            fg='#60a5fa',
            font=('Arial', 14, 'bold')
        )
        info_title.pack(pady=(15, 10))

        # معلومات المدير
        admin_frame = tk.Frame(info_frame, bg='#1e293b', relief='flat', bd=1)
        admin_frame.pack(fill=tk.X, padx=15, pady=5)

        admin_info = tk.Label(
            admin_frame,
            text="👑 المدير الأعلى: admin / admin123\n   الصلاحيات: كاملة بدون قيود",
            bg='#1e293b',
            fg='#10b981',
            font=('Arial', 11, 'bold'),
            justify=tk.LEFT
        )
        admin_info.pack(pady=8, padx=10, anchor='w')

        # معلومات المبيعات
        sales_frame = tk.Frame(info_frame, bg='#1e293b', relief='flat', bd=1)
        sales_frame.pack(fill=tk.X, padx=15, pady=(5, 15))

        sales_info = tk.Label(
            sales_frame,
            text="💼 موظف المبيعات: sales / sales123\n   الصلاحيات: محدودة (تحتاج ترخيص)",
            bg='#1e293b',
            fg='#f59e0b',
            font=('Arial', 11, 'bold'),
            justify=tk.LEFT
        )
        sales_info.pack(pady=8, padx=10, anchor='w')

    def create_developer_info(self, parent):
        """إنشاء معلومات المطور"""
        dev_frame = tk.Frame(parent, bg='#0f172a')
        dev_frame.pack(pady=(10, 0))

        dev_info = tk.Label(
            dev_frame,
            text="💎 المطور: محمد الشوامرة - 0566000140 💎",
            bg='#0f172a',
            fg='#6b7280',
            font=('Arial', 10, 'italic')
        )
        dev_info.pack()

        version_info = tk.Label(
            dev_frame,
            text="Phone Doctor v2.0 Final Professional Edition",
            bg='#0f172a',
            fg='#4b5563',
            font=('Arial', 9)
        )
        version_info.pack(pady=(2, 0))

    def quick_login(self, username, password):
        """تسجيل دخول سريع"""
        self.username_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.username_entry.insert(0, username)
        self.password_entry.insert(0, password)
        self.enhanced_login()

    def enhanced_login(self):
        """تسجيل دخول محسن مع تأثيرات"""
        if self.is_loading:
            return

        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            self.show_error_message("⚠️ تنبيه", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # بدء حالة التحميل
        self.start_loading()

        # محاكاة وقت المعالجة
        self.root.after(1500, lambda: self.complete_login(username, password))

    def start_loading(self):
        """بدء حالة التحميل"""
        self.is_loading = True
        self.login_btn.configure(
            text="🔄 جاري التحقق...",
            bg='#6b7280',
            state='disabled'
        )
        self.root.configure(cursor='wait')

        # تأثير النبض
        self.pulse_loading_button()

    def pulse_loading_button(self):
        """تأثير النبض لزر التحميل"""
        if self.is_loading:
            current_bg = self.login_btn['bg']
            new_bg = '#9ca3af' if current_bg == '#6b7280' else '#6b7280'
            self.login_btn.configure(bg=new_bg)
            self.root.after(500, self.pulse_loading_button)

    def complete_login(self, username, password):
        """إكمال عملية تسجيل الدخول"""
        if self.auth_manager.authenticate(username, password):
            # نجح تسجيل الدخول
            self.login_btn.configure(
                text="✅ تم بنجاح!",
                bg='#10b981',
                state='normal'
            )
            self.root.configure(cursor='')

            # انتظار قصير ثم إغلاق النافذة
            self.root.after(1000, self.close_and_continue)
        else:
            # فشل تسجيل الدخول
            self.login_btn.configure(
                text="❌ فشل تسجيل الدخول",
                bg='#ef4444',
                state='normal'
            )
            self.root.configure(cursor='')

            # إعادة تعيين الزر بعد فترة
            self.root.after(2000, self.reset_login_button)

            # عرض رسالة خطأ
            self.show_error_message("❌ خطأ في تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة")

            # مسح كلمة المرور
            self.password_entry.delete(0, tk.END)

    def reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.is_loading = False
        self.login_btn.configure(
            text="🚀 دخول",
            bg='#3b82f6',
            state='normal'
        )

    def close_and_continue(self):
        """إغلاق النافذة والمتابعة"""
        self.root.destroy()
        self.on_success_callback()

    def show_error_message(self, title, message):
        """عرض رسالة خطأ محسنة"""
        error_window = tk.Toplevel(self.root)
        error_window.title(title)
        error_window.geometry("400x200")
        error_window.configure(bg='#7f1d1d')
        error_window.resizable(False, False)
        error_window.transient(self.root)
        error_window.grab_set()

        # توسيط النافذة
        error_window.update_idletasks()
        x = (error_window.winfo_screenwidth() // 2) - (200)
        y = (error_window.winfo_screenheight() // 2) - (100)
        error_window.geometry(f'400x200+{x}+{y}')

        # أيقونة الخطأ
        icon_label = tk.Label(
            error_window,
            text="⚠️",
            bg='#7f1d1d',
            font=('Arial', 32)
        )
        icon_label.pack(pady=(20, 10))

        # عنوان الخطأ
        title_label = tk.Label(
            error_window,
            text=title,
            bg='#7f1d1d',
            fg='white',
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 10))

        # رسالة الخطأ
        message_label = tk.Label(
            error_window,
            text=message,
            bg='#7f1d1d',
            fg='white',
            font=('Arial', 11),
            wraplength=350,
            justify=tk.CENTER
        )
        message_label.pack(pady=(0, 20))

        # زر الإغلاق
        ok_btn = tk.Button(
            error_window,
            text="حسناً",
            command=error_window.destroy,
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=8,
            cursor='hand2'
        )
        ok_btn.pack(pady=(0, 20))

        # تأثير hover للزر
        def on_enter(e):
            ok_btn.configure(bg='#dc2626')
        def on_leave(e):
            ok_btn.configure(bg='#ef4444')

        ok_btn.bind('<Enter>', on_enter)
        ok_btn.bind('<Leave>', on_leave)

        # تأثير النبض للنافذة
        self.pulse_error_window(error_window)

    def pulse_error_window(self, window):
        """تأثير النبض لنافذة الخطأ"""
        def pulse():
            try:
                current_bg = window['bg']
                new_bg = '#991b1b' if current_bg == '#7f1d1d' else '#7f1d1d'
                window.configure(bg=new_bg)
                window.after(1000, pulse)
            except:
                pass  # النافذة مغلقة
        pulse()

class MainApplication:
    """التطبيق الرئيسي"""
    
    def __init__(self, auth_manager, db_manager):
        self.auth_manager = auth_manager
        self.db_manager = db_manager
        self.root = None
        self.current_page = 'dashboard'
        self.content_area = None
        
    def run(self):
        """تشغيل التطبيق"""
        self.setup_main_window()
        self.root.mainloop()
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - Final Professional Edition")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0f172a')
        self.root.state('zoomed')
        
        # الهيدر
        self.create_header()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#0f172a')
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # الشريط الجانبي
        self.create_sidebar(main_frame)
        
        # منطقة المحتوى
        self.content_area = tk.Frame(main_frame, bg='#1e293b', relief='raised', bd=2)
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تحميل لوحة التحكم
        self.load_dashboard()
    
    def create_header(self):
        """إنشاء الهيدر"""
        header_frame = tk.Frame(self.root, bg='#1e40af', height=80, relief='raised', bd=3)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(
            header_frame,
            text="Phone Doctor v2.0 - Final Professional Edition",
            bg='#1e40af',
            fg='white',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=30, pady=20)
        
        # معلومات المستخدم
        if self.auth_manager.current_user:
            user_frame = tk.Frame(header_frame, bg='#1e40af')
            user_frame.pack(side=tk.RIGHT, padx=30, pady=20)
            
            user_label = tk.Label(
                user_frame,
                text=f"مرحباً، {self.auth_manager.current_user['full_name']}",
                bg='#1e40af',
                fg='#fbbf24',
                font=('Arial', 14, 'bold')
            )
            user_label.pack()
            
            role_label = tk.Label(
                user_frame,
                text=f"الدور: {self.auth_manager.current_user['role']}",
                bg='#1e40af',
                fg='#94a3b8',
                font=('Arial', 10)
            )
            role_label.pack()
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = tk.Frame(parent, bg='#334155', width=280, relief='raised', bd=3)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = tk.Label(
            sidebar_frame,
            text="القائمة الرئيسية",
            bg='#334155',
            fg='#60a5fa',
            font=('Arial', 16, 'bold')
        )
        menu_title.pack(pady=25)
        
        # أزرار القائمة
        menu_items = [
            ("dashboard", "لوحة التحكم", "#3b82f6"),
            ("repairs", "إدارة الصيانة", "#10b981"),
            ("customers", "إدارة العملاء", "#f59e0b"),
            ("checks", "إدارة الشيكات", "#8b5cf6"),
            ("sales", "إدارة المبيعات", "#06b6d4"),
            ("inventory", "إدارة المخزون", "#84cc16"),
            ("reports", "التقارير", "#f97316"),
            ("settings", "الإعدادات", "#6b7280")
        ]
        
        self.menu_buttons = {}
        
        for page_id, title, color in menu_items:
            btn_frame = tk.Frame(sidebar_frame, bg='#334155')
            btn_frame.pack(fill=tk.X, padx=15, pady=3)
            
            btn = tk.Button(
                btn_frame,
                text=title,
                command=lambda p=page_id: self.change_page(p),
                bg=color if page_id == self.current_page else '#475569',
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=12,
                anchor='w',
                cursor='hand2'
            )
            btn.pack(fill=tk.X)
            
            self.menu_buttons[page_id] = btn
        
        # زر تسجيل الخروج
        logout_frame = tk.Frame(sidebar_frame, bg='#334155')
        logout_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=15)
        
        logout_btn = tk.Button(
            logout_frame,
            text="تسجيل الخروج",
            command=self.logout,
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=12,
            cursor='hand2'
        )
        logout_btn.pack(fill=tk.X)

    def change_page(self, page_id):
        """تغيير الصفحة"""
        # تحديث ألوان الأزرار
        for btn_id, btn in self.menu_buttons.items():
            if btn_id == page_id:
                btn.configure(bg='#3b82f6')
            else:
                btn.configure(bg='#475569')

        self.current_page = page_id

        # مسح المحتوى الحالي
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # تحميل الصفحة المطلوبة
        if page_id == "dashboard":
            self.load_dashboard()
        elif page_id == "repairs":
            self.load_repairs()
        elif page_id == "customers":
            self.load_customers()
        elif page_id == "checks":
            self.load_checks()
        else:
            self.load_placeholder(page_id)

    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="لوحة التحكم الرئيسية",
            bg='#1e293b',
            fg='#60a5fa',
            font=('Arial', 24, 'bold')
        )
        title_label.pack()

        # بطاقات الإحصائيات
        stats_frame = tk.Frame(self.content_area, bg='#1e293b')
        stats_frame.pack(fill=tk.X, padx=20, pady=20)

        # جلب الإحصائيات الحقيقية
        repairs_count = len(self.db_manager.fetch_all("SELECT * FROM repairs"))
        customers_count = len(self.db_manager.fetch_all("SELECT * FROM customers"))
        checks_count = len(self.db_manager.fetch_all("SELECT * FROM checks"))

        # حساب إجمالي الإيرادات
        revenue_data = self.db_manager.fetch_all("SELECT SUM(total_cost) as total FROM repairs WHERE total_cost > 0")
        total_revenue = revenue_data[0]['total'] if revenue_data and revenue_data[0]['total'] else 0

        stats_data = [
            ("أوامر الصيانة", repairs_count, "#10b981"),
            ("العملاء", customers_count, "#3b82f6"),
            ("الشيكات", checks_count, "#8b5cf6"),
            ("الإيرادات", f"{total_revenue:.0f} ₪", "#f59e0b")
        ]

        for i, (title, value, color) in enumerate(stats_data):
            col = i % 2
            row = i // 2

            card_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew', ipadx=30, ipady=20)

            value_label = tk.Label(card_frame, text=str(value), bg=color, fg='white', font=('Arial', 28, 'bold'))
            value_label.pack()

            title_label = tk.Label(card_frame, text=title, bg=color, fg='white', font=('Arial', 14, 'bold'))
            title_label.pack()

        # تكوين الشبكة
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)

        # رسالة ترحيب
        welcome_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        welcome_text = f"""
مرحباً بك في Phone Doctor v2.0 Final Edition

المستخدم: {self.auth_manager.current_user['full_name']}
الدور: {self.auth_manager.current_user['role']}

النظام يحتوي على بيانات حقيقية:
• {repairs_count} أمر صيانة مع تفاصيل كاملة
• {customers_count} عميل مع حسابات مالية
• {checks_count} شيك بحالات مختلفة
• {total_revenue:.0f} ₪ إجمالي الإيرادات

جميع البيانات متاحة للعرض والتفاعل
        """

        welcome_label = tk.Label(
            welcome_frame,
            text=welcome_text,
            bg='#374151',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        welcome_label.pack(expand=True)

    def load_repairs(self):
        """تحميل صفحة الصيانة مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="إدارة الصيانة - البيانات الحقيقية",
            bg='#1e293b',
            fg='#10b981',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة صيانة", "#10b981"),
            ("تعديل", "#3b82f6"),
            ("حذف", "#ef4444"),
            ("تقارير", "#8b5cf6"),
            ("تحديث", "#f59e0b")
        ]

        for text, color in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.show_message(f"ميزة {t} قيد التطوير"),
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول الصيانة
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة أوامر الصيانة (البيانات الحقيقية)",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "العميل", "الجهاز", "السيريال", "المشكلة", "القطع", "التكلفة", "الحالة", "التاريخ")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تعيين العناوين
        column_widths = [50, 120, 120, 100, 150, 120, 80, 80, 100]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[i], anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        repairs = self.db_manager.fetch_all("""
            SELECT id, customer_name, device_type, device_brand, device_serial,
                   problem_description, parts_used, total_cost, status,
                   repair_date, created_date
            FROM repairs
            ORDER BY id DESC
            LIMIT 30
        """)

        # إضافة البيانات للجدول
        for repair in repairs:
            device = f"{repair['device_brand'] or ''} {repair['device_type']}".strip()
            problem = repair['problem_description'][:20] + "..." if len(repair['problem_description']) > 20 else repair['problem_description']
            parts = repair['parts_used'][:15] + "..." if repair['parts_used'] and len(repair['parts_used']) > 15 else (repair['parts_used'] or 'لا يوجد')
            cost = f"{repair['total_cost']:.0f} ₪" if repair['total_cost'] else 'غير محدد'
            date = repair['repair_date'][:10] if repair['repair_date'] else (repair['created_date'][:10] if repair['created_date'] else 'غير محدد')

            tree.insert('', 'end', values=(
                repair['id'],
                repair['customer_name'],
                device,
                repair['device_serial'] or 'غير محدد',
                problem,
                parts,
                cost,
                repair['status'],
                date
            ))

        # إحصائيات
        stats_label = tk.Label(
            table_frame,
            text=f"إجمالي أوامر الصيانة: {len(self.db_manager.fetch_all('SELECT * FROM repairs'))} (عرض أحدث 30)",
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_customers(self):
        """تحميل صفحة العملاء مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="إدارة العملاء - الحسابات المالية",
            bg='#1e293b',
            fg='#3b82f6',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة عميل", "#3b82f6"),
            ("تعديل", "#10b981"),
            ("حذف", "#ef4444"),
            ("الحسابات المالية", "#f59e0b"),
            ("تحديث", "#8b5cf6")
        ]

        for text, color in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.show_message(f"ميزة {t} قيد التطوير"),
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول العملاء
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة العملاء والحسابات المالية (البيانات الحقيقية)",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "الاسم", "الهاتف", "البريد", "إجمالي المشتريات", "المدفوع", "الدين", "صافي الربح")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        column_widths = [50, 150, 120, 180, 120, 120, 120, 120]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[i], anchor='center')

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        customers = self.db_manager.fetch_all("""
            SELECT c.*,
                   COALESCE(SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END), 0) as total_purchases,
                   COALESCE(SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END), 0) as total_paid
            FROM customers c
            LEFT JOIN customer_transactions ct ON c.id = ct.customer_id
            GROUP BY c.id
            ORDER BY c.id
        """)

        total_debt = 0
        total_paid = 0
        total_profit = 0

        for customer in customers:
            purchases = customer['total_purchases'] or 0
            paid = customer['total_paid'] or 0
            debt = max(0, purchases - paid)
            profit = paid * 0.3  # هامش ربح 30%

            total_debt += debt
            total_paid += paid
            total_profit += profit

            tree.insert('', 'end', values=(
                customer['id'],
                customer['name'],
                customer['phone'],
                customer['email'] or 'غير محدد',
                f"{purchases:.2f} ₪",
                f"{paid:.2f} ₪",
                f"{debt:.2f} ₪" if debt > 0 else "لا يوجد",
                f"{profit:.2f} ₪"
            ))

        # إحصائيات العملاء
        stats_text = f"إجمالي العملاء: {len(customers)} | إجمالي الديون: {total_debt:.2f} ₪ | إجمالي المدفوع: {total_paid:.2f} ₪ | صافي الربح: {total_profit:.2f} ₪"
        stats_label = tk.Label(
            table_frame,
            text=stats_text,
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_checks(self):
        """تحميل صفحة الشيكات مع البيانات الحقيقية"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text="إدارة الشيكات - البيانات الحقيقية",
            bg='#1e293b',
            fg='#8b5cf6',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_area, bg='#1e293b')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        buttons_data = [
            ("إضافة شيك", "#8b5cf6"),
            ("تحصيل", "#10b981"),
            ("مرتد", "#ef4444"),
            ("تقرير الشيكات", "#f59e0b"),
            ("تحديث", "#3b82f6")
        ]

        for text, color in buttons_data:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=lambda t=text: self.show_message(f"ميزة {t} قيد التطوير"),
                bg=color,
                fg='white',
                font=('Arial', 12, 'bold'),
                relief='flat',
                bd=0,
                padx=20,
                pady=8,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول الشيكات
        table_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        table_title = tk.Label(
            table_frame,
            text="قائمة الشيكات (البيانات الحقيقية)",
            bg='#374151',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        table_title.pack(pady=10)

        # إطار الجدول
        tree_frame = tk.Frame(table_frame, bg='#374151')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("ID", "رقم الشيك", "المبلغ", "البنك", "تاريخ الإصدار", "تاريخ الاستحقاق", "المستفيد", "الدافع", "الحالة")
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        column_widths = [50, 100, 100, 120, 100, 100, 120, 120, 80]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[i], anchor='center')

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # جلب البيانات الحقيقية
        checks = self.db_manager.fetch_all("""
            SELECT * FROM checks ORDER BY id DESC
        """)

        total_amount = 0
        pending_count = 0
        cashed_count = 0
        bounced_count = 0

        for check in checks:
            total_amount += check['amount']
            if check['status'] == 'معلق':
                pending_count += 1
            elif check['status'] == 'محصل':
                cashed_count += 1
            elif check['status'] == 'مرتد':
                bounced_count += 1

            tree.insert('', 'end', values=(
                check['id'],
                check['check_number'],
                f"{check['amount']:.2f} ₪",
                check['bank_name'],
                check['issue_date'][:10] if check['issue_date'] else 'غير محدد',
                check['due_date'][:10] if check['due_date'] else 'غير محدد',
                check['payee'],
                check['payer'],
                check['status']
            ))

        # إحصائيات الشيكات
        stats_text = f"إجمالي الشيكات: {len(checks)} | معلقة: {pending_count} | محصلة: {cashed_count} | مرتدة: {bounced_count} | إجمالي المبلغ: {total_amount:.2f} ₪"
        stats_label = tk.Label(
            table_frame,
            text=stats_text,
            bg='#374151',
            fg='#94a3b8',
            font=('Arial', 12)
        )
        stats_label.pack(pady=10)

    def load_placeholder(self, page_name):
        """تحميل صفحة مؤقتة"""
        # العنوان
        title_frame = tk.Frame(self.content_area, bg='#1e293b')
        title_frame.pack(fill=tk.X, pady=20)

        title_label = tk.Label(
            title_frame,
            text=f"صفحة {page_name}",
            bg='#1e293b',
            fg='#f59e0b',
            font=('Arial', 20, 'bold')
        )
        title_label.pack()

        # محتوى الصفحة
        content_frame = tk.Frame(self.content_area, bg='#374151', relief='raised', bd=2)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        content_text = f"""
صفحة {page_name} قيد التطوير

هذه الصفحة ستحتوي على:
• واجهة متطورة مع تأثيرات احترافية
• إدارة شاملة للبيانات
• تقارير تفاعلية ومفصلة
• تصميم احترافي وسهل الاستخدام

سيتم إضافة المزيد من الميزات قريباً...

الميزات المتاحة حالياً:
✅ لوحة التحكم مع إحصائيات حية
✅ إدارة الصيانة مع 50 أمر حقيقي
✅ إدارة العملاء مع 20 عميل وحسابات مالية
✅ إدارة الشيكات مع 25 شيك بحالات مختلفة
        """

        content_label = tk.Label(
            content_frame,
            text=content_text,
            bg='#374151',
            fg='white',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        content_label.pack(expand=True)

    def show_message(self, message):
        """عرض رسالة"""
        messagebox.showinfo("معلومات", message)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟")
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            main()

def main():
    """الدالة الرئيسية"""
    print("بدء تشغيل Phone Doctor v2.0 Final Edition...")

    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    if not db_manager.initialize_database():
        messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
        return

    # إنشاء مدير المصادقة
    auth_manager = AuthManager(db_manager)

    def on_login_success():
        """عند نجاح تسجيل الدخول"""
        app = MainApplication(auth_manager, db_manager)
        app.run()

    # عرض نافذة تسجيل الدخول
    login_window = LoginWindow(auth_manager, on_login_success)
    login_window.show()

if __name__ == "__main__":
    main()
