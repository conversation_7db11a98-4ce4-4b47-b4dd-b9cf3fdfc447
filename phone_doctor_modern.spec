# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# إضافة المجلد الحالي للمسار
current_dir = os.path.dirname(os.path.abspath(SPEC))
sys.path.append(current_dir)

block_cipher = None

# تحديد الملفات والمجلدات المطلوبة
a = Analysis(
    ['main_modern.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('phone_doctor.db', '.'),
        ('ui', 'ui'),
        ('database', 'database'),
        ('utils', 'utils'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'sqlite3',
        'datetime',
        'threading',
        'time',
        'random',
        'string',
        'pathlib',
        'json',
        'shutil',
        'os',
        'sys',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# تصفية الملفات غير المطلوبة
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PhoneDoctor_Modern',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكن إضافة أيقونة لاحقاً
    version_file=None,  # يمكن إضافة معلومات الإصدار لاحقاً
)
