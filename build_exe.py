#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تجميع البرنامج إلى EXE
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_spec_file():
    """إنشاء ملف المواصفات لـ PyInstaller"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_tkinter.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('phone_doctor.db', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'sqlite3',
        'datetime',
        'threading',
        'time',
        'random',
        'string',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PhoneDoctor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('phone_doctor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("✅ تم إنشاء ملف المواصفات")

def create_version_info():
    """إنشاء ملف معلومات الإصدار"""
    version_info = '''
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Mohammad Shawamreh'),
        StringStruct(u'FileDescription', u'Phone Doctor - Professional Phone Repair Shop Management System'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'PhoneDoctor'),
        StringStruct(u'LegalCopyright', u'© 2024 Mohammad Shawamreh. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'PhoneDoctor.exe'),
        StringStruct(u'ProductName', u'Phone Doctor'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info.strip())
    
    print("✅ تم إنشاء ملف معلومات الإصدار")

def create_icon():
    """إنشاء أيقونة البرنامج (نص فقط لأننا لا نملك أيقونة حقيقية)"""
    if not os.path.exists('assets'):
        os.makedirs('assets')
    
    # إنشاء ملف نصي يشير إلى أنه يجب إضافة أيقونة
    with open('assets/icon_needed.txt', 'w', encoding='utf-8') as f:
        f.write('يجب إضافة ملف icon.ico هنا للحصول على أيقونة مخصصة للبرنامج')
    
    print("📝 تم إنشاء مجلد الأصول (يحتاج إلى أيقونة)")

def build_executable():
    """تجميع البرنامج إلى EXE"""
    print("🔨 بدء تجميع البرنامج...")
    
    try:
        # تشغيل PyInstaller
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'phone_doctor.spec'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم تجميع البرنامج بنجاح!")
            return True
        else:
            print("❌ فشل في تجميع البرنامج:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تجميع البرنامج: {e}")
        return False

def create_distribution_package():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع...")
    
    # إنشاء مجلد التوزيع
    dist_folder = "PhoneDoctor_Distribution"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    
    # نسخ الملف التنفيذي
    exe_path = "dist/PhoneDoctor.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, dist_folder)
        print("✅ تم نسخ الملف التنفيذي")
    else:
        print("❌ لم يتم العثور على الملف التنفيذي")
        return False
    
    # إنشاء قاعدة بيانات فارغة للتوزيع
    from database.db_manager import DatabaseManager
    
    dist_db_path = os.path.join(dist_folder, "phone_doctor.db")
    db_manager = DatabaseManager(dist_db_path)
    if db_manager.initialize_database():
        print("✅ تم إنشاء قاعدة البيانات للتوزيع")
    else:
        print("❌ فشل في إنشاء قاعدة البيانات")
    
    # إنشاء ملف الإعدادات للتوزيع
    from utils.config import Config
    
    dist_config_path = os.path.join(dist_folder, "config.json")
    config = Config(dist_config_path)
    config.save_config()
    print("✅ تم إنشاء ملف الإعدادات للتوزيع")
    
    # إنشاء ملف README
    readme_content = """
# Phone Doctor - نظام إدارة محلات صيانة الهواتف

## المطور: محمد الشوامرة
## الهاتف: 0566000140
## جميع الحقوق محفوظة © 2024

## تعليمات التشغيل:

1. تأكد من وجود الملفات التالية في نفس مجلد البرنامج:
   - PhoneDoctor.exe (الملف التنفيذي)
   - phone_doctor.db (قاعدة البيانات)
   - config.json (ملف الإعدادات)

2. قم بتشغيل PhoneDoctor.exe

3. عند التشغيل الأول، يمكنك تعديل اسم المحل من تبويب الإعدادات

## الميزات:
- إدارة الصيانات وتتبع حالة الأجهزة
- إدارة المخزون مع تنبيهات المخزون المنخفض
- إدارة الموردين وبياناتهم
- لوحة تحكم تفاعلية مع الإحصائيات
- نسخ احتياطي واستعادة قاعدة البيانات
- واجهة مستخدم عربية سهلة الاستخدام

## الدعم الفني:
للدعم الفني أو الاستفسارات، يرجى التواصل مع:
محمد الشوامرة - 0566000140

---
Phone Doctor v1.0.0
Professional Phone Repair Shop Management System
"""
    
    with open(os.path.join(dist_folder, "README.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README")
    
    # إنشاء ملف دفعي للتشغيل السريع
    batch_content = """@echo off
chcp 65001 > nul
title Phone Doctor - نظام إدارة محلات صيانة الهواتف
echo.
echo ========================================
echo   Phone Doctor - نظام إدارة محلات صيانة الهواتف
echo   المطور: محمد الشوامرة - 0566000140
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo.
PhoneDoctor.exe
if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل البرنامج!
    echo يرجى التأكد من وجود جميع الملفات المطلوبة.
    echo.
    pause
)
"""
    
    with open(os.path.join(dist_folder, "تشغيل البرنامج.bat"), 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    print(f"🎉 تم إنشاء حزمة التوزيع في مجلد: {dist_folder}")
    return True

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_folders = ['build', '__pycache__']
    temp_files = ['phone_doctor.spec', 'version_info.txt']
    
    for folder in temp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"✅ تم حذف مجلد: {folder}")
    
    for file in temp_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✅ تم حذف ملف: {file}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء عملية تجميع Phone Doctor")
    print("=" * 50)
    
    try:
        # التحقق من وجود PyInstaller
        try:
            import PyInstaller
            print("✅ PyInstaller متوفر")
        except ImportError:
            print("❌ PyInstaller غير مثبت!")
            print("يرجى تثبيته باستخدام: pip install pyinstaller")
            return False
        
        # إنشاء الملفات المطلوبة
        create_icon()
        create_version_info()
        create_spec_file()
        
        # تجميع البرنامج
        if not build_executable():
            return False
        
        # إنشاء حزمة التوزيع
        if not create_distribution_package():
            return False
        
        # تنظيف الملفات المؤقتة
        cleanup()
        
        print("\n" + "=" * 50)
        print("🎉 تم تجميع البرنامج بنجاح!")
        print("📁 ستجد حزمة التوزيع في مجلد: PhoneDoctor_Distribution")
        print("👨‍💻 المطور: محمد الشوامرة - 📞 0566000140")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في عملية التجميع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
