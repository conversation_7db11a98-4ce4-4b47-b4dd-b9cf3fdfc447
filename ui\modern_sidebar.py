#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الشريط الجانبي العصري - Modern Sidebar
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk
from .modern_styles import ModernStyles

class ModernSidebarButton(tk.Frame):
    """زر الشريط الجانبي العصري"""
    
    def __init__(self, parent, icon, text, command=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.styles = ModernStyles()
        self.command = command
        self.is_active = False
        self.is_hovered = False
        
        # تكوين الإطار
        self.configure(
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0,
            cursor='hand2'
        )
        
        # الإطار الداخلي للمحتوى
        self.content_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=4)
        
        # شريط النشاط الجانبي
        self.active_indicator = tk.Frame(
            self.content_frame,
            bg=self.styles.colors['primary'],
            width=4,
            height=40
        )
        self.active_indicator.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 12))
        self.active_indicator.pack_forget()  # إخفاء افتراضياً
        
        # إطار المحتوى الرئيسي
        self.main_content = tk.Frame(
            self.content_frame,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        self.main_content.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=12, pady=8)
        
        # الأيقونة
        self.icon_label = tk.Label(
            self.main_content,
            text=icon,
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_light'],
            font=('Segoe UI Emoji', 18),
            anchor='w'
        )
        self.icon_label.pack(side=tk.LEFT, padx=(0, 12))
        
        # النص
        self.text_label = tk.Label(
            self.main_content,
            text=text,
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_light'],
            font=self.styles.fonts['sidebar'],
            anchor='w'
        )
        self.text_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # ربط الأحداث
        self.bind_events()
        
    def bind_events(self):
        """ربط أحداث الماوس"""
        widgets = [self, self.content_frame, self.main_content, self.icon_label, self.text_label]
        
        for widget in widgets:
            widget.bind('<Button-1>', self.on_click)
            widget.bind('<Enter>', self.on_enter)
            widget.bind('<Leave>', self.on_leave)
    
    def on_click(self, event=None):
        """معالجة النقر"""
        if self.command:
            self.command()
    
    def on_enter(self, event=None):
        """معالجة دخول الماوس"""
        if not self.is_active:
            self.is_hovered = True
            self.update_appearance()
    
    def on_leave(self, event=None):
        """معالجة خروج الماوس"""
        self.is_hovered = False
        self.update_appearance()
    
    def set_active(self, active):
        """تعيين حالة النشاط"""
        self.is_active = active
        self.update_appearance()
    
    def update_appearance(self):
        """تحديث المظهر حسب الحالة"""
        if self.is_active:
            # الحالة النشطة
            bg_color = self.styles.colors['primary']
            fg_color = self.styles.colors['text_white']
            self.active_indicator.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 12))
            self.active_indicator.configure(bg=self.styles.colors['text_white'])
        elif self.is_hovered:
            # حالة التمرير
            bg_color = self.styles.colors['bg_dark']
            fg_color = self.styles.colors['text_white']
            self.active_indicator.pack_forget()
        else:
            # الحالة العادية
            bg_color = self.styles.colors['bg_sidebar']
            fg_color = self.styles.colors['text_light']
            self.active_indicator.pack_forget()
        
        # تطبيق الألوان
        widgets = [self, self.content_frame, self.main_content]
        for widget in widgets:
            widget.configure(bg=bg_color)
        
        self.icon_label.configure(bg=bg_color, fg=fg_color)
        self.text_label.configure(bg=bg_color, fg=fg_color)

class ModernSidebar(tk.Frame):
    """الشريط الجانبي العصري"""
    
    def __init__(self, parent, on_menu_select=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.styles = ModernStyles()
        self.on_menu_select = on_menu_select
        self.menu_buttons = []
        self.active_button = None
        
        # تكوين الإطار الرئيسي
        self.configure(
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0,
            width=280
        )
        
        # منع تغيير الحجم
        self.pack_propagate(False)
        
        self.create_header()
        self.create_menu_section()
        self.create_footer()
        
    def create_header(self):
        """إنشاء رأس الشريط الجانبي"""
        header_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0,
            height=100
        )
        header_frame.pack(fill=tk.X, padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # خط فاصل علوي
        top_line = tk.Frame(
            header_frame,
            bg=self.styles.colors['primary'],
            height=4
        )
        top_line.pack(fill=tk.X)
        
        # محتوى الرأس
        content_frame = tk.Frame(
            header_frame,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=16)
        
        # شعار التطبيق
        logo_frame = tk.Frame(
            content_frame,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        logo_frame.pack(fill=tk.X)
        
        # أيقونة التطبيق
        app_icon = tk.Label(
            logo_frame,
            text="📱",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['primary'],
            font=('Segoe UI Emoji', 24),
            anchor='w'
        )
        app_icon.pack(side=tk.LEFT, padx=(0, 12))
        
        # اسم التطبيق
        app_name = tk.Label(
            logo_frame,
            text="Phone Doctor",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_white'],
            font=('Segoe UI', 16, 'bold'),
            anchor='w'
        )
        app_name.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # وصف التطبيق
        app_desc = tk.Label(
            content_frame,
            text="نظام إدارة محلات الصيانة",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_muted'],
            font=self.styles.fonts['caption'],
            anchor='w'
        )
        app_desc.pack(fill=tk.X, pady=(4, 0))
        
        # خط فاصل سفلي
        bottom_line = tk.Frame(
            header_frame,
            bg=self.styles.colors['border_dark'],
            height=1
        )
        bottom_line.pack(fill=tk.X)
    
    def create_menu_section(self):
        """إنشاء قسم القوائم"""
        menu_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        menu_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=16)
        
        # عنوان القسم
        section_title = tk.Label(
            menu_frame,
            text="القوائم الرئيسية",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_muted'],
            font=('Segoe UI', 10, 'bold'),
            anchor='w'
        )
        section_title.pack(fill=tk.X, padx=20, pady=(0, 12))
        
        # قائمة الأزرار
        self.menu_container = tk.Frame(
            menu_frame,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        self.menu_container.pack(fill=tk.BOTH, expand=True)
        
        # إضافة أزرار القوائم
        menu_items = [
            ("🏠", "لوحة التحكم", "dashboard"),
            ("🔧", "إدارة الصيانة", "repairs"),
            ("📦", "إدارة المخزون", "inventory"),
            ("🏪", "إدارة الموردين", "suppliers"),
            ("💰", "إدارة المبيعات", "sales"),
            ("💳", "الشؤون المالية", "financial"),
            ("📊", "التقارير والإحصائيات", "reports"),
            ("⚙️", "الإعدادات والتخصيص", "settings")
        ]
        
        for icon, text, page_id in menu_items:
            button = ModernSidebarButton(
                self.menu_container,
                icon=icon,
                text=text,
                command=lambda p=page_id: self.select_menu(p),
                bg=self.styles.colors['bg_sidebar']
            )
            button.pack(fill=tk.X, pady=2)
            self.menu_buttons.append((button, page_id))
        
        # تعيين الزر الأول كنشط افتراضياً
        if self.menu_buttons:
            self.set_active_menu(self.menu_buttons[0][1])
    
    def create_footer(self):
        """إنشاء تذييل الشريط الجانبي"""
        footer_frame = tk.Frame(
            self,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0,
            height=80
        )
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=0, pady=0)
        footer_frame.pack_propagate(False)
        
        # خط فاصل علوي
        top_line = tk.Frame(
            footer_frame,
            bg=self.styles.colors['border_dark'],
            height=1
        )
        top_line.pack(fill=tk.X)
        
        # محتوى التذييل
        content_frame = tk.Frame(
            footer_frame,
            bg=self.styles.colors['bg_sidebar'],
            relief='flat',
            bd=0
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=12)
        
        # معلومات المطور
        dev_info = tk.Label(
            content_frame,
            text="المطور: محمد الشوامرة",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_muted'],
            font=('Segoe UI', 9, 'bold'),
            anchor='w'
        )
        dev_info.pack(fill=tk.X)
        
        # رقم الهاتف
        phone_info = tk.Label(
            content_frame,
            text="📞 0566000140",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_muted'],
            font=('Segoe UI', 9),
            anchor='w'
        )
        phone_info.pack(fill=tk.X, pady=(2, 0))
        
        # حقوق الطبع
        copyright_info = tk.Label(
            content_frame,
            text="© 2024 جميع الحقوق محفوظة",
            bg=self.styles.colors['bg_sidebar'],
            fg=self.styles.colors['text_muted'],
            font=('Segoe UI', 8),
            anchor='w'
        )
        copyright_info.pack(fill=tk.X, pady=(4, 0))
    
    def select_menu(self, page_id):
        """اختيار قائمة"""
        self.set_active_menu(page_id)
        if self.on_menu_select:
            self.on_menu_select(page_id)
    
    def set_active_menu(self, page_id):
        """تعيين القائمة النشطة"""
        # إلغاء تنشيط الزر السابق
        if self.active_button:
            self.active_button.set_active(False)
        
        # تنشيط الزر الجديد
        for button, btn_page_id in self.menu_buttons:
            if btn_page_id == page_id:
                button.set_active(True)
                self.active_button = button
                break
    
    def get_active_menu(self):
        """الحصول على القائمة النشطة"""
        for button, page_id in self.menu_buttons:
            if button.is_active:
                return page_id
        return None
