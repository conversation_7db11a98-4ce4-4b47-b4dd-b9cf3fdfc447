# دليل المستخدم - Phone Doctor

## نظام إدارة محلات صيانة الهواتف الاحترافي

**المطور:** محمد الشوامرة  
**الهاتف:** 0566000140  
**جميع الحقوق محفوظة © 2024**

---

## 📱 نظرة عامة

Phone Doctor هو نظام إدارة شامل ومتطور مصمم خصيصاً لمحلات صيانة الهواتف الخلوية. يوفر النظام جميع الأدوات اللازمة لإدارة العمليات اليومية بكفاءة عالية.

## 🚀 التشغيل السريع

### متطلبات النظام
- نظام التشغيل: Windows 7 أو أحدث
- Python 3.7 أو أحدث (للنسخة المصدرية)
- مساحة تخزين: 50 ميجابايت على الأقل

### طرق التشغيل

#### 1. تشغيل النسخة التنفيذية (EXE)
```
1. قم بتشغيل PhoneDoctor.exe
2. أو استخدم ملف "تشغيل البرنامج.bat"
```

#### 2. تشغيل النسخة المصدرية
```
1. قم بتشغيل run_phone_doctor.bat
2. أو استخدم الأمر: python main_tkinter.py
```

## 🏠 لوحة التحكم الرئيسية

عند تشغيل البرنامج، ستظهر لوحة التحكم الرئيسية التي تحتوي على:

### الإحصائيات السريعة
- **المبيعات اليوم:** إجمالي مبيعات اليوم الحالي
- **صيانات جارية:** عدد الأجهزة قيد الصيانة
- **تنبيهات المخزون:** عدد القطع منخفضة المخزون
- **شيكات مستحقة:** عدد الشيكات المستحقة السداد
- **عملاء جدد:** عدد العملاء الجدد هذا الشهر
- **الأرباح الشهرية:** إجمالي الأرباح للشهر الحالي

### الأعمال السريعة
- إضافة جهاز للصيانة
- بيع قطعة
- إضافة قطعة للمخزون
- إضافة مورد
- عرض التقارير
- إنشاء نسخة احتياطية

## 🔧 نظام الصيانة

### إضافة جهاز جديد للصيانة

1. **انتقل إلى تبويب "الصيانة"**
2. **اضغط على "إضافة جهاز جديد"**
3. **أدخل بيانات العميل:**
   - اختر عميل موجود أو أضف عميل جديد
   - اسم العميل
   - رقم الهاتف
   - العنوان (اختياري)

4. **أدخل بيانات الجهاز:**
   - نوع الجهاز (iPhone, Samsung, إلخ)
   - الموديل
   - رقم IMEI (اختياري)
   - وصف المشكلة
   - التكلفة المتوقعة

5. **أضف ملاحظات الفني (اختياري)**
6. **اضغط "حفظ"**

### تحديث حالة الصيانة

1. **اختر الجهاز من قائمة الصيانات**
2. **اضغط على "تحديث الحالة"**
3. **اختر الحالة الجديدة:**
   - قيد الانتظار
   - جاري الإصلاح
   - تم الإصلاح
   - تم التسليم
   - ملغي

4. **أدخل التكلفة الفعلية**
5. **أضف ملاحظات إضافية**
6. **اضغط "حفظ التحديث"**

## 📦 نظام المخزون

### إضافة قطعة جديدة

1. **انتقل إلى تبويب "المخزون"**
2. **اضغط على "إضافة قطعة جديدة"**
3. **أدخل بيانات القطعة:**
   - اسم القطعة
   - الفئة (شاشات، بطاريات، إلخ)
   - الباركود (أو اضغط "توليد" لإنشاء باركود تلقائي)
   - المورد

4. **أدخل الأسعار والكميات:**
   - سعر الشراء
   - سعر البيع (أو استخدم "حساب تلقائي +30%")
   - الكمية
   - الحد الأدنى للكمية
   - الموقع

5. **أضف ملاحظات (اختياري)**
6. **اضغط "حفظ"**

### إدارة المخزون

- **البحث والفلترة:** ابحث عن القطع حسب الاسم أو الفئة
- **تنبيهات المخزون:** عرض القطع منخفضة المخزون
- **إحصائيات المخزون:** إجمالي القطع وقيمة المخزون

## 🏪 نظام الموردين

### إضافة مورد جديد

1. **انتقل إلى تبويب "الموردين"**
2. **اضغط على "إضافة مورد جديد"**
3. **أدخل بيانات المورد:**
   - اسم المورد
   - اسم الشركة
   - رقم الهاتف
   - البريد الإلكتروني
   - العنوان
   - الرصيد الابتدائي

4. **أضف ملاحظات (اختياري)**
5. **اضغط "حفظ"**

## ⚙️ الإعدادات

### إعدادات المحل
- **اسم المحل:** يمكن تغيير اسم المحل من تبويب الإعدادات

### إدارة قاعدة البيانات
- **نسخة احتياطية:** إنشاء نسخة احتياطية من قاعدة البيانات
- **استعادة:** استعادة قاعدة البيانات من نسخة احتياطية

## 🔒 النسخ الاحتياطي

### إنشاء نسخة احتياطية
1. **انتقل إلى تبويب "الإعدادات"**
2. **اضغط على "إنشاء نسخة احتياطية"**
3. **اختر موقع الحفظ**
4. **اضغط "حفظ"**

### استعادة نسخة احتياطية
1. **انتقل إلى تبويب "الإعدادات"**
2. **اضغط على "استعادة نسخة احتياطية"**
3. **اختر ملف النسخة الاحتياطية**
4. **أكد العملية**

## 🎨 الميزات المتقدمة

### واجهة المستخدم
- **تصميم عصري:** واجهة نظيفة وسهلة الاستخدام
- **دعم اللغة العربية:** واجهة مستخدم باللغة العربية بالكامل
- **تحديث تلقائي:** تحديث البيانات والإحصائيات تلقائياً

### الأمان والموثوقية
- **قاعدة بيانات محلية:** جميع البيانات محفوظة محلياً
- **نسخ احتياطي:** إمكانية إنشاء واستعادة النسخ الاحتياطية
- **استقرار عالي:** نظام مستقر وموثوق

## 🆘 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### البرنامج لا يبدأ
- تأكد من وجود جميع الملفات المطلوبة
- تأكد من تثبيت Python (للنسخة المصدرية)
- تأكد من صلاحيات الكتابة في مجلد البرنامج

#### خطأ في قاعدة البيانات
- تأكد من وجود ملف phone_doctor.db
- تأكد من صلاحيات الكتابة على الملف
- جرب استعادة نسخة احتياطية

#### البيانات لا تظهر
- اضغط على زر "تحديث" في التبويب المعني
- أعد تشغيل البرنامج
- تحقق من وجود البيانات في قاعدة البيانات

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

**محمد الشوامرة**  
📞 **الهاتف:** 0566000140  
📧 **للاستفسارات الفنية والدعم**

### ساعات الدعم
- الأحد - الخميس: 9:00 ص - 6:00 م
- الجمعة - السبت: حسب الحاجة

## 📋 ملاحظات مهمة

1. **النسخ الاحتياطي:** يُنصح بإنشاء نسخة احتياطية يومياً
2. **التحديثات:** تحقق من وجود تحديثات جديدة بانتظام
3. **الأمان:** احتفظ بنسخ احتياطية في أماكن آمنة
4. **الاستخدام:** اقرأ هذا الدليل بعناية قبل الاستخدام

## 🏆 شكر وتقدير

شكراً لاختيارك Phone Doctor لإدارة محل صيانة الهواتف الخاص بك. نحن ملتزمون بتقديم أفضل الحلول التقنية لعملك.

---

**Phone Doctor v1.0.0**  
**Professional Phone Repair Shop Management System**  
**© 2024 Mohammad Shawamreh. All rights reserved.**
