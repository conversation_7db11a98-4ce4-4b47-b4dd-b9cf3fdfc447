#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0 Professional Edition
Phone Doctor Launcher

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess
import time

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص متطلبات النظام...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print(f"❌ يتطلب Python 3.7 أو أحدث. الإصدار الحالي: {python_version.major}.{python_version.minor}")
        return False
    else:
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # فحص tkinter
    try:
        import tkinter
        print("✅ Tkinter متاح")
    except ImportError:
        print("❌ Tkinter غير متاح")
        return False
    
    # فحص sqlite3
    try:
        import sqlite3
        print("✅ SQLite3 متاح")
    except ImportError:
        print("❌ SQLite3 غير متاح")
        return False
    
    return True

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة...")
    
    required_files = [
        'phone_doctor_working.py',
        'ui/theme_manager.py',
        'ui/customers_manager_advanced.py',
        'ui/checks_manager.py',
        'ui/repairs_manager_simple.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {len(missing_files)}")
        return False
    
    return True

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    if not os.path.exists('database'):
        print("❌ مجلد database غير موجود")
        return False
    
    if not os.path.exists('database/phone_doctor.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        import sqlite3
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ قاعدة البيانات متاحة - {len(tables)} جدول")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def launch_program():
    """تشغيل البرنامج"""
    print("\n🚀 تشغيل Phone Doctor v2.0...")
    
    try:
        # تشغيل البرنامج
        process = subprocess.Popen([
            sys.executable, 
            'phone_doctor_working.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("✅ تم تشغيل البرنامج بنجاح!")
        print("📱 ابحث عن نافذة Phone Doctor على شاشتك")
        print("\n🔐 بيانات تسجيل الدخول:")
        print("   المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        # انتظار لمدة 3 ثوان للتأكد من التشغيل
        time.sleep(3)
        
        # فحص إذا كان البرنامج ما زال يعمل
        if process.poll() is None:
            print("✅ البرنامج يعمل بشكل صحيح")
            return True
        else:
            stdout, stderr = process.communicate()
            print("❌ البرنامج توقف بشكل غير متوقع")
            if stderr:
                print(f"خطأ: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 Phone Doctor v2.0 Professional Edition")
    print("   نظام إدارة محل الهواتف المتطور")
    print("   المطور: محمد الشوامرة - 0566000140")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل فحص المتطلبات")
        input("اضغط Enter للإغلاق...")
        return
    
    # فحص الملفات
    if not check_files():
        print("\n❌ فشل فحص الملفات")
        input("اضغط Enter للإغلاق...")
        return
    
    # فحص قاعدة البيانات
    if not check_database():
        print("\n❌ فشل فحص قاعدة البيانات")
        print("💡 تلميح: تأكد من تشغيل ملفات إضافة البيانات التجريبية")
        input("اضغط Enter للإغلاق...")
        return
    
    print("\n✅ جميع الفحوصات نجحت!")
    
    # تشغيل البرنامج
    if launch_program():
        print("\n🎉 تم تشغيل Phone Doctor بنجاح!")
        print("📋 الميزات المتاحة:")
        print("   🔧 إدارة الصيانة المتطورة (50 أمر)")
        print("   👥 إدارة العملاء والحسابات (20 عميل)")
        print("   💳 إدارة الشيكات (25 شيك)")
        print("   📊 نظام التقارير المتقدم")
        print("   🎨 واجهة احترافية متطورة")
    else:
        print("\n❌ فشل في تشغيل البرنامج")
    
    input("\nاضغط Enter للإغلاق...")

if __name__ == "__main__":
    main()
