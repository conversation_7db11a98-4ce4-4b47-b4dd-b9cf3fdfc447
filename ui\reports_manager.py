#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة التقارير والإحصائيات
Reports and Analytics Manager

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date, timedelta
import calendar

class ReportsManager:
    """مدير التقارير والإحصائيات"""
    
    def __init__(self, parent_frame, db_manager):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        
        # ألوان النظام العصرية
        self.colors = {
            'primary': '#3b82f6',
            'secondary': '#8b5cf6',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'info': '#06b6d4',
            'bg_light': '#f8fafc',
            'bg_gradient': '#e0e7ff',
            'text_dark': '#1f2937',
            'text_light': '#6b7280',
            'accent': '#ec4899',
            'chart_colors': ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']
        }
        
        self.setup_ui()
        self.load_reports()
    
    def setup_ui(self):
        """إعداد واجهة التقارير العصرية"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي مع تدرج لوني
        title_frame = tk.Frame(self.parent_frame, bg='white', height=100)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        title_frame.pack_propagate(False)
        
        # إطار التدرج للعنوان
        gradient_frame = tk.Frame(title_frame, bg=self.colors['primary'], height=80)
        gradient_frame.pack(fill=tk.X, pady=10)
        gradient_frame.pack_propagate(False)
        
        title_content = tk.Frame(gradient_frame, bg=self.colors['primary'])
        title_content.pack(expand=True, fill=tk.BOTH)
        
        title_label = tk.Label(
            title_content,
            text="📊 التقارير والإحصائيات",
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 22, 'bold')
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            title_content,
            text="تحليل شامل لأداء المحل والمبيعات والصيانة",
            bg=self.colors['primary'],
            fg='#e2e8f0',
            font=('Arial', 12)
        )
        subtitle_label.pack()
        
        # شريط الفلترة
        filter_frame = tk.Frame(self.parent_frame, bg='white')
        filter_frame.pack(fill=tk.X, padx=20, pady=10)
        
        filter_container = tk.Frame(filter_frame, bg='#f1f5f9', relief='solid', bd=1)
        filter_container.pack(fill=tk.X, pady=5)
        
        filter_content = tk.Frame(filter_container, bg='#f1f5f9')
        filter_content.pack(fill=tk.X, padx=20, pady=15)
        
        # فلتر الفترة الزمنية
        period_label = tk.Label(
            filter_content,
            text="📅 الفترة الزمنية:",
            bg='#f1f5f9',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        period_label.grid(row=0, column=0, sticky='w', padx=(0, 10))
        
        self.period_var = tk.StringVar()
        self.period_var.trace('w', self.on_period_change)
        
        period_combo = ttk.Combobox(
            filter_content,
            textvariable=self.period_var,
            values=['اليوم', 'أمس', 'هذا الأسبوع', 'الأسبوع الماضي', 'هذا الشهر', 'الشهر الماضي', 'هذا العام', 'مخصص'],
            font=('Arial', 11),
            width=15,
            state='readonly'
        )
        period_combo.set('هذا الشهر')
        period_combo.grid(row=0, column=1, padx=(0, 20))
        
        # زر تحديث التقارير
        refresh_btn = tk.Button(
            filter_content,
            text="🔄 تحديث التقارير",
            command=self.load_reports,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.grid(row=0, column=2, padx=(0, 20))
        
        # زر طباعة التقرير
        print_btn = tk.Button(
            filter_content,
            text="🖨️ طباعة التقرير",
            command=self.print_report,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 11, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        print_btn.grid(row=0, column=3)
        
        # المحتوى الرئيسي
        main_content = tk.Frame(self.parent_frame, bg='white')
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(main_content)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب الإحصائيات العامة
        self.create_overview_tab()
        
        # تبويب تقارير المبيعات
        self.create_sales_tab()
        
        # تبويب تقارير الصيانة
        self.create_repairs_tab()
        
        # تبويب تقارير المخزون
        self.create_inventory_tab()
        
        # تبويب التحليلات المتقدمة
        self.create_analytics_tab()
    
    def create_overview_tab(self):
        """إنشاء تبويب الإحصائيات العامة"""
        overview_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(overview_frame, text="📊 نظرة عامة")
        
        # إطار البطاقات
        cards_frame = tk.Frame(overview_frame, bg='white')
        cards_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # بطاقات الإحصائيات
        self.create_stat_cards(cards_frame)
        
        # الرسوم البيانية
        charts_frame = tk.Frame(overview_frame, bg='white')
        charts_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # رسم بياني للمبيعات الشهرية
        self.create_sales_chart(charts_frame)
    
    def create_sales_tab(self):
        """إنشاء تبويب تقارير المبيعات"""
        sales_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(sales_frame, text="💰 المبيعات")
        
        # جدول المبيعات التفصيلي
        self.create_sales_table(sales_frame)
    
    def create_repairs_tab(self):
        """إنشاء تبويب تقارير الصيانة"""
        repairs_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(repairs_frame, text="🔧 الصيانة")
        
        # إحصائيات الصيانة
        self.create_repairs_stats(repairs_frame)
    
    def create_inventory_tab(self):
        """إنشاء تبويب تقارير المخزون"""
        inventory_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(inventory_frame, text="📦 المخزون")
        
        # تحليل المخزون
        self.create_inventory_analysis(inventory_frame)
    
    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات المتقدمة"""
        analytics_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(analytics_frame, text="📈 تحليلات متقدمة")
        
        # التحليلات المتقدمة
        self.create_advanced_analytics(analytics_frame)
    
    def create_stat_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # جلب البيانات
        stats = self.get_overview_stats()
        
        # بطاقات الإحصائيات
        cards_data = [
            ("💰", "إجمالي المبيعات", f"{stats['total_sales']:.2f} ₪", self.colors['success']),
            ("🛒", "عدد المبيعات", str(stats['sales_count']), self.colors['primary']),
            ("🔧", "أوامر الصيانة", str(stats['repairs_count']), self.colors['warning']),
            ("👥", "العملاء", str(stats['customers_count']), self.colors['info']),
            ("📦", "المنتجات", str(stats['products_count']), self.colors['secondary']),
            ("⚠️", "مخزون منخفض", str(stats['low_stock_count']), self.colors['danger'])
        ]
        
        # إنشاء الشبكة
        for i, (icon, title, value, color) in enumerate(cards_data):
            row = i // 3
            col = i % 3
            
            card_frame = tk.Frame(parent, bg=color, relief='solid', bd=1)
            card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            
            # محتوى البطاقة
            card_content = tk.Frame(card_frame, bg=color)
            card_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # الأيقونة
            icon_label = tk.Label(
                card_content,
                text=icon,
                bg=color,
                fg='white',
                font=('Arial', 28)
            )
            icon_label.pack()
            
            # القيمة
            value_label = tk.Label(
                card_content,
                text=value,
                bg=color,
                fg='white',
                font=('Arial', 18, 'bold')
            )
            value_label.pack()
            
            # العنوان
            title_label = tk.Label(
                card_content,
                text=title,
                bg=color,
                fg='white',
                font=('Arial', 11)
            )
            title_label.pack()
        
        # تكوين الأعمدة
        for i in range(3):
            parent.grid_columnconfigure(i, weight=1)
    
    def create_sales_chart(self, parent):
        """إنشاء رسم بياني للمبيعات"""
        chart_frame = tk.LabelFrame(
            parent,
            text="📈 مبيعات آخر 7 أيام",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # محاكاة رسم بياني بسيط
        chart_content = tk.Frame(chart_frame, bg='white')
        chart_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # جلب بيانات المبيعات لآخر 7 أيام
        sales_data = self.get_weekly_sales_data()
        
        # عرض البيانات في شكل أعمدة نصية
        max_value = max(sales_data.values()) if sales_data.values() else 1
        
        for i, (day, amount) in enumerate(sales_data.items()):
            day_frame = tk.Frame(chart_content, bg='white')
            day_frame.grid(row=0, column=i, padx=5, sticky='s')
            
            # العمود
            height = int((amount / max_value) * 100) if max_value > 0 else 0
            bar_frame = tk.Frame(day_frame, bg=self.colors['primary'], width=40, height=max(height, 10))
            bar_frame.pack()
            bar_frame.pack_propagate(False)
            
            # القيمة
            value_label = tk.Label(
                day_frame,
                text=f"{amount:.0f}",
                bg='white',
                fg=self.colors['text_dark'],
                font=('Arial', 9, 'bold')
            )
            value_label.pack(pady=(5, 0))
            
            # اليوم
            day_label = tk.Label(
                day_frame,
                text=day,
                bg='white',
                fg=self.colors['text_light'],
                font=('Arial', 8)
            )
            day_label.pack()
    
    def create_sales_table(self, parent):
        """إنشاء جدول المبيعات التفصيلي"""
        # العنوان
        title_label = tk.Label(
            parent,
            text="💰 تقرير المبيعات التفصيلي",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(20, 10))
        
        # الجدول
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        columns = ("التاريخ", "رقم البيع", "العميل", "المبلغ", "طريقة الدفع", "الحالة")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')
        
        # جلب بيانات المبيعات
        sales_data = self.get_sales_data()
        
        total_amount = 0
        for sale in sales_data:
            tree.insert('', 'end', values=(
                sale.get('date', 'غير محدد'),
                sale.get('id', 'غير محدد'),
                sale.get('customer', 'عميل نقدي'),
                f"{sale.get('amount', 0):.2f} ₪",
                sale.get('payment_method', 'نقدي'),
                sale.get('status', 'مكتمل')
            ))
            total_amount += sale.get('amount', 0)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # الإجمالي
        total_frame = tk.Frame(parent, bg=self.colors['success'])
        total_frame.pack(fill=tk.X, padx=20, pady=10)
        
        total_label = tk.Label(
            total_frame,
            text=f"💰 إجمالي المبيعات: {total_amount:.2f} ₪",
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 14, 'bold')
        )
        total_label.pack(pady=10)

    def create_repairs_stats(self, parent):
        """إنشاء إحصائيات الصيانة"""
        # العنوان
        title_label = tk.Label(
            parent,
            text="🔧 إحصائيات الصيانة",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(20, 10))

        # إحصائيات الحالات
        stats_frame = tk.Frame(parent, bg='white')
        stats_frame.pack(fill=tk.X, padx=20, pady=20)

        repairs_stats = self.get_repairs_stats()

        # بطاقات إحصائيات الصيانة
        repair_cards = [
            ("⏳", "قيد الانتظار", repairs_stats.get('pending', 0), self.colors['warning']),
            ("🔧", "قيد الإصلاح", repairs_stats.get('in_progress', 0), self.colors['info']),
            ("✅", "تم الإصلاح", repairs_stats.get('completed', 0), self.colors['success']),
            ("📦", "تم التسليم", repairs_stats.get('delivered', 0), self.colors['primary']),
            ("❌", "ملغي", repairs_stats.get('cancelled', 0), self.colors['danger'])
        ]

        for i, (icon, title, count, color) in enumerate(repair_cards):
            card_frame = tk.Frame(stats_frame, bg=color, relief='solid', bd=1)
            card_frame.grid(row=0, column=i, padx=10, pady=10, sticky="ew")

            card_content = tk.Frame(card_frame, bg=color)
            card_content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            icon_label = tk.Label(card_content, text=icon, bg=color, fg='white', font=('Arial', 20))
            icon_label.pack()

            count_label = tk.Label(card_content, text=str(count), bg=color, fg='white', font=('Arial', 16, 'bold'))
            count_label.pack()

            title_label = tk.Label(card_content, text=title, bg=color, fg='white', font=('Arial', 10))
            title_label.pack()

        # تكوين الأعمدة
        for i in range(5):
            stats_frame.grid_columnconfigure(i, weight=1)

        # جدول الصيانة الحديثة
        recent_frame = tk.LabelFrame(
            parent,
            text="🕒 أحدث أوامر الصيانة",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # جدول الصيانة
        columns = ("رقم الأمر", "العميل", "الجهاز", "الحالة", "التاريخ")
        tree = ttk.Treeview(recent_frame, columns=columns, show='headings', height=10)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')

        # جلب أحدث أوامر الصيانة
        recent_repairs = self.get_recent_repairs()

        for repair in recent_repairs:
            tree.insert('', 'end', values=(
                repair.get('id', ''),
                repair.get('customer_name', ''),
                repair.get('device', ''),
                repair.get('status', ''),
                repair.get('date', '')
            ))

        scrollbar = ttk.Scrollbar(recent_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_inventory_analysis(self, parent):
        """إنشاء تحليل المخزون"""
        # العنوان
        title_label = tk.Label(
            parent,
            text="📦 تحليل المخزون",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(20, 10))

        # إحصائيات المخزون
        inventory_stats = self.get_inventory_stats()

        # بطاقات المخزون
        inventory_frame = tk.Frame(parent, bg='white')
        inventory_frame.pack(fill=tk.X, padx=20, pady=20)

        inventory_cards = [
            ("📦", "إجمالي المنتجات", inventory_stats.get('total_products', 0), self.colors['primary']),
            ("✅", "متوفر", inventory_stats.get('in_stock', 0), self.colors['success']),
            ("⚠️", "مخزون منخفض", inventory_stats.get('low_stock', 0), self.colors['warning']),
            ("❌", "نفد المخزون", inventory_stats.get('out_of_stock', 0), self.colors['danger']),
            ("💰", "قيمة المخزون", f"{inventory_stats.get('total_value', 0):.0f} ₪", self.colors['info'])
        ]

        for i, (icon, title, value, color) in enumerate(inventory_cards):
            card_frame = tk.Frame(inventory_frame, bg=color, relief='solid', bd=1)
            card_frame.grid(row=0, column=i, padx=8, pady=10, sticky="ew")

            card_content = tk.Frame(card_frame, bg=color)
            card_content.pack(fill=tk.BOTH, expand=True, padx=12, pady=12)

            icon_label = tk.Label(card_content, text=icon, bg=color, fg='white', font=('Arial', 18))
            icon_label.pack()

            value_label = tk.Label(card_content, text=str(value), bg=color, fg='white', font=('Arial', 14, 'bold'))
            value_label.pack()

            title_label = tk.Label(card_content, text=title, bg=color, fg='white', font=('Arial', 9))
            title_label.pack()

        for i in range(5):
            inventory_frame.grid_columnconfigure(i, weight=1)

        # جدول المنتجات الأكثر مبيعاً
        top_products_frame = tk.LabelFrame(
            parent,
            text="🏆 المنتجات الأكثر مبيعاً",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        top_products_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # جدول المنتجات
        columns = ("المنتج", "الفئة", "المبيعات", "الإيرادات", "المخزون المتبقي")
        tree = ttk.Treeview(top_products_frame, columns=columns, show='headings', height=8)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')

        # بيانات وهمية للمنتجات الأكثر مبيعاً
        top_products = [
            ("شاشة iPhone 14", "شاشات", "25", "5000 ₪", "10"),
            ("بطارية Samsung", "بطاريات", "18", "2160 ₪", "15"),
            ("كفر حماية", "إكسسوارات", "45", "1125 ₪", "50"),
            ("شاحن سريع", "شواحن", "12", "600 ₪", "8"),
            ("سماعات لاسلكية", "سماعات", "8", "800 ₪", "5")
        ]

        for product in top_products:
            tree.insert('', 'end', values=product)

        scrollbar = ttk.Scrollbar(top_products_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_advanced_analytics(self, parent):
        """إنشاء التحليلات المتقدمة"""
        # العنوان
        title_label = tk.Label(
            parent,
            text="📈 التحليلات المتقدمة",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(20, 10))

        # مؤشرات الأداء الرئيسية
        kpi_frame = tk.LabelFrame(
            parent,
            text="🎯 مؤشرات الأداء الرئيسية (KPI)",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        kpi_frame.pack(fill=tk.X, padx=20, pady=20)

        kpi_content = tk.Frame(kpi_frame, bg='white')
        kpi_content.pack(fill=tk.X, padx=20, pady=20)

        # حساب المؤشرات
        analytics = self.get_advanced_analytics()

        kpi_data = [
            ("📊", "متوسط قيمة البيع", f"{analytics.get('avg_sale_value', 0):.2f} ₪"),
            ("👥", "عدد العملاء الجدد", str(analytics.get('new_customers', 0))),
            ("🔄", "معدل العائدين", f"{analytics.get('return_rate', 0):.1f}%"),
            ("⏱️", "متوسط وقت الصيانة", f"{analytics.get('avg_repair_time', 0)} يوم"),
            ("💹", "نمو المبيعات", f"{analytics.get('sales_growth', 0):+.1f}%"),
            ("🎯", "معدل إتمام الصيانة", f"{analytics.get('completion_rate', 0):.1f}%")
        ]

        for i, (icon, title, value) in enumerate(kpi_data):
            row = i // 3
            col = i % 3

            kpi_card = tk.Frame(kpi_content, bg=self.colors['bg_light'], relief='solid', bd=1)
            kpi_card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

            card_content = tk.Frame(kpi_card, bg=self.colors['bg_light'])
            card_content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            icon_label = tk.Label(card_content, text=icon, bg=self.colors['bg_light'], fg=self.colors['primary'], font=('Arial', 20))
            icon_label.pack()

            value_label = tk.Label(card_content, text=value, bg=self.colors['bg_light'], fg=self.colors['text_dark'], font=('Arial', 14, 'bold'))
            value_label.pack()

            title_label = tk.Label(card_content, text=title, bg=self.colors['bg_light'], fg=self.colors['text_light'], font=('Arial', 10))
            title_label.pack()

        for i in range(3):
            kpi_content.grid_columnconfigure(i, weight=1)

        # توصيات الأعمال
        recommendations_frame = tk.LabelFrame(
            parent,
            text="💡 توصيات الأعمال",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=10,
            pady=10
        )
        recommendations_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        recommendations_content = tk.Frame(recommendations_frame, bg='white')
        recommendations_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        recommendations = [
            "🔥 المنتجات الأكثر مبيعاً: ركز على تخزين المزيد من شاشات iPhone وبطاريات Samsung",
            "⚠️ تنبيه مخزون: 5 منتجات تحتاج إعادة تخزين فوري",
            "📈 فرصة نمو: زيادة 15% في مبيعات الإكسسوارات هذا الشهر",
            "🕒 تحسين الخدمة: متوسط وقت الصيانة يمكن تقليله بـ 20%",
            "👥 العملاء الجدد: 8 عملاء جدد هذا الأسبوع - فرصة لبرامج الولاء",
            "💰 تحسين الربحية: المنتجات عالية الهامش تحقق 40% من الأرباح"
        ]

        for i, recommendation in enumerate(recommendations):
            rec_frame = tk.Frame(recommendations_content, bg=self.colors['bg_light'], relief='solid', bd=1)
            rec_frame.pack(fill=tk.X, pady=5)

            rec_label = tk.Label(
                rec_frame,
                text=recommendation,
                bg=self.colors['bg_light'],
                fg=self.colors['text_dark'],
                font=('Arial', 11),
                wraplength=800,
                justify=tk.LEFT
            )
            rec_label.pack(anchor='w', padx=15, pady=10)

    def get_overview_stats(self):
        """جلب الإحصائيات العامة"""
        try:
            # جلب إحصائيات المبيعات
            sales_result = self.db_manager.fetch_one("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales")

            # جلب إحصائيات الصيانة
            repairs_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM repairs")

            # جلب إحصائيات العملاء
            customers_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM customers")

            # جلب إحصائيات المخزون
            inventory_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM inventory")
            low_stock_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM inventory WHERE quantity <= 5")

            return {
                'total_sales': sales_result['total'] if sales_result else 0,
                'sales_count': sales_result['count'] if sales_result else 0,
                'repairs_count': repairs_result['count'] if repairs_result else 0,
                'customers_count': customers_result['count'] if customers_result else 0,
                'products_count': inventory_result['count'] if inventory_result else 0,
                'low_stock_count': low_stock_result['count'] if low_stock_result else 0
            }
        except Exception as e:
            print(f"خطأ في جلب الإحصائيات العامة: {e}")
            return {
                'total_sales': 0, 'sales_count': 0, 'repairs_count': 0,
                'customers_count': 0, 'products_count': 0, 'low_stock_count': 0
            }

    def get_weekly_sales_data(self):
        """جلب بيانات المبيعات الأسبوعية"""
        try:
            # حساب آخر 7 أيام
            today = date.today()
            week_data = {}

            for i in range(7):
                day = today - timedelta(days=i)
                day_name = calendar.day_name[day.weekday()]

                # جلب مبيعات اليوم
                result = self.db_manager.fetch_one(
                    "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(sale_date) = ?",
                    (day.strftime('%Y-%m-%d'),)
                )

                week_data[day_name[:3]] = result['total'] if result else 0

            return dict(reversed(list(week_data.items())))
        except Exception as e:
            print(f"خطأ في جلب بيانات المبيعات الأسبوعية: {e}")
            return {'Mon': 0, 'Tue': 0, 'Wed': 0, 'Thu': 0, 'Fri': 0, 'Sat': 0, 'Sun': 0}

    def get_sales_data(self):
        """جلب بيانات المبيعات"""
        try:
            sales = self.db_manager.fetch_all("""
                SELECT s.id, s.sale_date, s.total_amount, s.payment_method, s.status,
                       COALESCE(c.name, 'عميل نقدي') as customer_name
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                ORDER BY s.sale_date DESC
                LIMIT 50
            """)

            return [{
                'id': sale['id'],
                'date': sale['sale_date'][:10] if sale['sale_date'] else 'غير محدد',
                'customer': sale['customer_name'],
                'amount': sale['total_amount'],
                'payment_method': sale['payment_method'],
                'status': sale['status']
            } for sale in sales]
        except Exception as e:
            print(f"خطأ في جلب بيانات المبيعات: {e}")
            return []

    def get_repairs_stats(self):
        """جلب إحصائيات الصيانة"""
        try:
            stats = {}
            statuses = ['قيد الانتظار', 'قيد الإصلاح', 'تم الإصلاح', 'تم التسليم', 'ملغي']

            for status in statuses:
                result = self.db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM repairs WHERE status = ?",
                    (status,)
                )
                key = status.replace(' ', '_').replace('قيد_الانتظار', 'pending').replace('قيد_الإصلاح', 'in_progress').replace('تم_الإصلاح', 'completed').replace('تم_التسليم', 'delivered').replace('ملغي', 'cancelled')
                stats[key] = result['count'] if result else 0

            return stats
        except Exception as e:
            print(f"خطأ في جلب إحصائيات الصيانة: {e}")
            return {'pending': 0, 'in_progress': 0, 'completed': 0, 'delivered': 0, 'cancelled': 0}

    def get_recent_repairs(self):
        """جلب أحدث أوامر الصيانة"""
        try:
            repairs = self.db_manager.fetch_all("""
                SELECT id, customer_name, device_type, device_model, status, created_date
                FROM repairs
                ORDER BY created_date DESC
                LIMIT 10
            """)

            return [{
                'id': repair['id'],
                'customer_name': repair['customer_name'],
                'device': f"{repair['device_type']} {repair['device_model'] or ''}".strip(),
                'status': repair['status'],
                'date': repair['created_date'][:10] if repair['created_date'] else 'غير محدد'
            } for repair in repairs]
        except Exception as e:
            print(f"خطأ في جلب أحدث أوامر الصيانة: {e}")
            return []

    def get_inventory_stats(self):
        """جلب إحصائيات المخزون"""
        try:
            total_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM inventory")
            in_stock_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM inventory WHERE quantity > 5")
            low_stock_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM inventory WHERE quantity > 0 AND quantity <= 5")
            out_of_stock_result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM inventory WHERE quantity = 0")
            value_result = self.db_manager.fetch_one("SELECT COALESCE(SUM(quantity * selling_price), 0) as total FROM inventory")

            return {
                'total_products': total_result['count'] if total_result else 0,
                'in_stock': in_stock_result['count'] if in_stock_result else 0,
                'low_stock': low_stock_result['count'] if low_stock_result else 0,
                'out_of_stock': out_of_stock_result['count'] if out_of_stock_result else 0,
                'total_value': value_result['total'] if value_result else 0
            }
        except Exception as e:
            print(f"خطأ في جلب إحصائيات المخزون: {e}")
            return {'total_products': 0, 'in_stock': 0, 'low_stock': 0, 'out_of_stock': 0, 'total_value': 0}

    def get_advanced_analytics(self):
        """جلب التحليلات المتقدمة"""
        try:
            # متوسط قيمة البيع
            avg_sale = self.db_manager.fetch_one("SELECT AVG(total_amount) as avg FROM sales")

            # عدد العملاء الجدد (آخر 30 يوم)
            new_customers = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM customers WHERE created_at >= date('now', '-30 days')"
            )

            # معدل إتمام الصيانة
            total_repairs = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM repairs")
            completed_repairs = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM repairs WHERE status IN ('تم الإصلاح', 'تم التسليم')")

            completion_rate = 0
            if total_repairs and total_repairs['count'] > 0:
                completion_rate = (completed_repairs['count'] / total_repairs['count']) * 100

            return {
                'avg_sale_value': avg_sale['avg'] if avg_sale and avg_sale['avg'] else 0,
                'new_customers': new_customers['count'] if new_customers else 0,
                'return_rate': 25.5,  # قيمة وهمية
                'avg_repair_time': 3,  # قيمة وهمية
                'sales_growth': 12.3,  # قيمة وهمية
                'completion_rate': completion_rate
            }
        except Exception as e:
            print(f"خطأ في جلب التحليلات المتقدمة: {e}")
            return {
                'avg_sale_value': 0, 'new_customers': 0, 'return_rate': 0,
                'avg_repair_time': 0, 'sales_growth': 0, 'completion_rate': 0
            }

    def on_period_change(self, *args):
        """معالجة تغيير الفترة الزمنية"""
        period = self.period_var.get()
        print(f"تم تغيير الفترة إلى: {period}")
        # يمكن إضافة منطق فلترة البيانات هنا

    def load_reports(self):
        """تحديث جميع التقارير"""
        print("تحديث التقارير...")
        # إعادة تحميل البيانات
        try:
            # تحديث البيانات في جميع التبويبات
            self.setup_ui()
        except Exception as e:
            print(f"خطأ في تحديث التقارير: {e}")

    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("طباعة التقرير", "سيتم إضافة ميزة الطباعة قريباً")
