#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول بتصميم الذكاء الاصطناعي
AI-Style Login Window

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import messagebox
import sys

class AILoginWindow:
    """نافذة تسجيل الدخول بتصميم الذكاء الاصطناعي"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("🤖 AI Login - Phone Doctor")
        self.root.geometry("500x650")
        self.root.resizable(False, False)
        
        # ألوان الذكاء الاصطناعي
        self.colors = {
            'bg_primary': '#0a0a0a',      # أسود عميق
            'bg_card': '#1a1a1a',         # رمادي داكن
            'bg_input': '#2a2a2a',        # رمادي متوسط
            'accent': '#00d4ff',          # أزرق سيان
            'accent_hover': '#00b8e6',    # أزرق سيان داكن
            'success': '#00ff88',         # أخضر نيون
            'danger': '#ff4757',          # أحمر نيون
            'text_primary': '#ffffff',    # أبيض
            'text_secondary': '#a0a0a0',  # رمادي فاتح
            'border': '#333333',          # رمادي حدود
            'glow': '#00d4ff'             # توهج أزرق
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        
        # توسيط النافذة
        self.center_window()
        
        # منع إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.setup_ui()
        
        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول بتصميم AI"""
        
        # الإطار الرئيسي
        main_frame = tk.Frame(
            self.root, 
            bg=self.colors['bg_primary']
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)
        
        # رأس التطبيق مع تأثير AI
        header_frame = tk.Frame(
            main_frame,
            bg=self.colors['bg_primary']
        )
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # أيقونة AI متحركة
        ai_icon = tk.Label(
            header_frame,
            text="🤖",
            bg=self.colors['bg_primary'],
            fg=self.colors['accent'],
            font=('Segoe UI Emoji', 48)
        )
        ai_icon.pack(pady=(0, 10))
        
        # اسم التطبيق مع تأثير مستقبلي
        title_label = tk.Label(
            header_frame,
            text="PHONE DOCTOR AI",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            font=('Consolas', 24, 'bold')
        )
        title_label.pack(pady=(0, 5))
        
        # خط فاصل مضيء
        separator = tk.Frame(
            header_frame,
            bg=self.colors['accent'],
            height=2
        )
        separator.pack(fill=tk.X, pady=(10, 0))
        
        # وصف النظام
        desc_label = tk.Label(
            header_frame,
            text="⚡ نظام إدارة ذكي لمحلات صيانة الهواتف ⚡",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Segoe UI', 12)
        )
        desc_label.pack(pady=(15, 0))
        
        # بطاقة تسجيل الدخول مع تأثير الزجاج
        login_card = tk.Frame(
            main_frame,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=2,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        login_card.pack(fill=tk.X, pady=(20, 0))
        
        # محتوى البطاقة
        content_frame = tk.Frame(
            login_card,
            bg=self.colors['bg_card']
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(
            content_frame,
            text="🔐 SYSTEM ACCESS",
            bg=self.colors['bg_card'],
            fg=self.colors['accent'],
            font=('Consolas', 16, 'bold')
        )
        login_title.pack(pady=(0, 25))
        
        # حقل اسم المستخدم
        username_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_card']
        )
        username_frame.pack(fill=tk.X, pady=(0, 20))
        
        username_label = tk.Label(
            username_frame,
            text="👤 USER ID:",
            bg=self.colors['bg_card'],
            fg=self.colors['text_primary'],
            font=('Consolas', 12, 'bold')
        )
        username_label.pack(anchor='w', pady=(0, 8))
        
        self.username_entry = tk.Entry(
            username_frame,
            bg=self.colors['bg_input'],
            fg=self.colors['text_primary'],
            font=('Consolas', 14),
            relief='flat',
            bd=0,
            insertbackground=self.colors['accent'],
            selectbackground=self.colors['accent'],
            selectforeground=self.colors['bg_primary']
        )
        self.username_entry.pack(fill=tk.X, ipady=12)
        
        # إطار حول حقل اسم المستخدم
        username_border = tk.Frame(
            username_frame,
            bg=self.colors['border'],
            height=1
        )
        username_border.pack(fill=tk.X, pady=(2, 0))
        
        # حقل كلمة المرور
        password_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_card']
        )
        password_frame.pack(fill=tk.X, pady=(0, 20))
        
        password_label = tk.Label(
            password_frame,
            text="🔑 PASSWORD:",
            bg=self.colors['bg_card'],
            fg=self.colors['text_primary'],
            font=('Consolas', 12, 'bold')
        )
        password_label.pack(anchor='w', pady=(0, 8))
        
        self.password_entry = tk.Entry(
            password_frame,
            bg=self.colors['bg_input'],
            fg=self.colors['text_primary'],
            font=('Consolas', 14),
            relief='flat',
            bd=0,
            show="●",
            insertbackground=self.colors['accent'],
            selectbackground=self.colors['accent'],
            selectforeground=self.colors['bg_primary']
        )
        self.password_entry.pack(fill=tk.X, ipady=12)
        
        # إطار حول حقل كلمة المرور
        password_border = tk.Frame(
            password_frame,
            bg=self.colors['border'],
            height=1
        )
        password_border.pack(fill=tk.X, pady=(2, 0))
        
        # خيار إظهار كلمة المرور
        show_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_card']
        )
        show_frame.pack(fill=tk.X, pady=(0, 25))
        
        self.show_password_var = tk.BooleanVar()
        show_check = tk.Checkbutton(
            show_frame,
            text="👁️ SHOW PASSWORD",
            variable=self.show_password_var,
            command=self.toggle_password,
            bg=self.colors['bg_card'],
            fg=self.colors['text_secondary'],
            font=('Consolas', 10),
            selectcolor=self.colors['bg_input'],
            activebackground=self.colors['bg_card'],
            activeforeground=self.colors['accent'],
            relief='flat',
            bd=0
        )
        show_check.pack(anchor='w')
        
        # أزرار العمل
        buttons_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_card']
        )
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # زر تسجيل الدخول مع تأثير مضيء
        self.login_button = tk.Button(
            buttons_frame,
            text="🚀 ACCESS GRANTED",
            command=self.login,
            bg=self.colors['accent'],
            fg=self.colors['bg_primary'],
            font=('Consolas', 14, 'bold'),
            relief='flat',
            bd=0,
            pady=15,
            cursor='hand2',
            activebackground=self.colors['accent_hover'],
            activeforeground=self.colors['bg_primary']
        )
        self.login_button.pack(fill=tk.X, pady=(0, 15))
        
        # زر الخروج
        exit_button = tk.Button(
            buttons_frame,
            text="❌ SYSTEM EXIT",
            command=self.exit_app,
            bg=self.colors['danger'],
            fg=self.colors['text_primary'],
            font=('Consolas', 12, 'bold'),
            relief='flat',
            bd=0,
            pady=12,
            cursor='hand2',
            activebackground='#ff3742',
            activeforeground=self.colors['text_primary']
        )
        exit_button.pack(fill=tk.X)
        
        # معلومات النظام
        info_frame = tk.Frame(
            main_frame,
            bg=self.colors['bg_primary']
        )
        info_frame.pack(fill=tk.X, pady=(25, 0))
        
        # معلومات المطور
        dev_label = tk.Label(
            info_frame,
            text="💻 DEVELOPER: محمد الشوامرة - 📞 0566000140",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Consolas', 10)
        )
        dev_label.pack(pady=(0, 10))
        
        # معلومات تسجيل الدخول
        creds_frame = tk.Frame(
            info_frame,
            bg=self.colors['bg_input'],
            relief='flat',
            bd=1,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        creds_frame.pack(fill=tk.X)
        
        creds_content = tk.Frame(
            creds_frame,
            bg=self.colors['bg_input']
        )
        creds_content.pack(fill=tk.X, padx=15, pady=10)
        
        creds_title = tk.Label(
            creds_content,
            text="🔐 DEFAULT CREDENTIALS:",
            bg=self.colors['bg_input'],
            fg=self.colors['success'],
            font=('Consolas', 11, 'bold')
        )
        creds_title.pack()
        
        admin_label = tk.Label(
            creds_content,
            text="👨‍💼 ADMIN: admin / admin123",
            bg=self.colors['bg_input'],
            fg=self.colors['text_primary'],
            font=('Consolas', 10)
        )
        admin_label.pack(pady=(5, 2))
        
        sales_label = tk.Label(
            creds_content,
            text="👨‍💻 SALES: sales / sales123",
            bg=self.colors['bg_input'],
            fg=self.colors['text_primary'],
            font=('Consolas', 10)
        )
        sales_label.pack(pady=(2, 0))
        
        # ربط مفاتيح الاختصار
        self.root.bind('<Return>', lambda e: self.login())
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda e: self.login())
        
        # تأثيرات التفاعل
        self.add_hover_effects()
    
    def add_hover_effects(self):
        """إضافة تأثيرات التفاعل"""
        def on_enter_login(e):
            self.login_button.configure(bg=self.colors['accent_hover'])
        
        def on_leave_login(e):
            self.login_button.configure(bg=self.colors['accent'])
        
        self.login_button.bind("<Enter>", on_enter_login)
        self.login_button.bind("<Leave>", on_leave_login)
    
    def toggle_password(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.show_password_var.get():
            self.password_entry.configure(show="")
        else:
            self.password_entry.configure(show="●")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username:
            messagebox.showerror("⚠️ ACCESS DENIED", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("⚠️ ACCESS DENIED", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # تأثير تحميل
        self.login_button.configure(text="🔄 PROCESSING...", state='disabled')
        self.root.update()
        
        try:
            if self.auth_manager.authenticate(username, password):
                user_name = self.auth_manager.current_user.get('full_name', 'المستخدم')
                self.login_button.configure(text="✅ ACCESS GRANTED", bg=self.colors['success'])
                self.root.update()
                self.root.after(1000, lambda: [
                    messagebox.showinfo("🎉 SUCCESS", f"مرحباً {user_name}!\nتم منح الوصول للنظام"),
                    self.root.destroy(),
                    self.on_success_callback()
                ])
            else:
                self.login_button.configure(text="❌ ACCESS DENIED", bg=self.colors['danger'])
                self.root.update()
                self.root.after(1500, lambda: [
                    self.login_button.configure(text="🚀 ACCESS GRANTED", bg=self.colors['accent'], state='normal'),
                    messagebox.showerror("⚠️ ACCESS DENIED", "اسم المستخدم أو كلمة المرور غير صحيحة"),
                    self.password_entry.delete(0, tk.END),
                    self.username_entry.focus()
                ])
        except Exception as e:
            self.login_button.configure(text="❌ SYSTEM ERROR", bg=self.colors['danger'])
            self.root.update()
            self.root.after(1500, lambda: [
                self.login_button.configure(text="🚀 ACCESS GRANTED", bg=self.colors['accent'], state='normal'),
                messagebox.showerror("💥 SYSTEM ERROR", f"خطأ في النظام: {str(e)}")
            ])
    
    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("🚪 SYSTEM EXIT", "هل تريد الخروج من النظام؟"):
            self.root.quit()
            sys.exit()
    
    def on_closing(self):
        """معالجة إغلاق النافذة"""
        self.exit_app()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.root.mainloop()
