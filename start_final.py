#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0 Final Edition المباشر
Direct Final Edition Launcher

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess

def print_final_header():
    """طباعة الهيدر النهائي"""
    print("🏆" * 35)
    print("🎯 Phone Doctor v2.0 Final Edition 🎯")
    print("🏆 النسخة النهائية المُوصى بها 🏆")
    print("💎 المطور: محمد الشوامرة - 0566000140 💎")
    print("🏆" * 35)

def check_final_requirements():
    """فحص متطلبات النسخة النهائية"""
    print("\n🔍 فحص المتطلبات النهائية...")
    
    # فحص Python
    if sys.version_info.major < 3:
        print("❌ يتطلب Python 3.0 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص tkinter
    try:
        import tkinter
        print("✅ Tkinter متاح")
    except ImportError:
        print("❌ Tkinter غير متاح")
        return False
    
    # فحص sqlite3
    try:
        import sqlite3
        print("✅ SQLite3 متاح")
    except ImportError:
        print("❌ SQLite3 غير متاح")
        return False
    
    return True

def check_final_files():
    """فحص الملفات النهائية"""
    print("\n📁 فحص الملفات النهائية...")
    
    required_files = [
        'phone_doctor_final.py',
        'database/phone_doctor.db'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            return False
    
    return True

def check_final_data():
    """فحص البيانات النهائية"""
    print("\n🗄️ فحص البيانات النهائية...")
    
    try:
        import sqlite3
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ {len(tables)} جدول متاح")
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM repairs")
        repairs = cursor.fetchone()[0]
        print(f"🔧 {repairs} أمر صيانة")
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers = cursor.fetchone()[0]
        print(f"👥 {customers} عميل")
        
        cursor.execute("SELECT COUNT(*) FROM checks")
        checks = cursor.fetchone()[0]
        print(f"💳 {checks} شيك")
        
        # حساب الإحصائيات المالية
        cursor.execute("SELECT SUM(total_cost) FROM repairs WHERE total_cost > 0")
        revenue = cursor.fetchone()[0] or 0
        print(f"💰 {revenue:.0f} ₪ إجمالي الإيرادات")
        
        cursor.execute("""
            SELECT SUM(CASE WHEN ct.transaction_type = 'purchase' THEN ct.amount ELSE 0 END) as purchases,
                   SUM(CASE WHEN ct.transaction_type = 'payment' THEN ct.amount ELSE 0 END) as payments
            FROM customer_transactions ct
        """)
        financial = cursor.fetchone()
        if financial:
            purchases = financial[0] or 0
            payments = financial[1] or 0
            debt = max(0, purchases - payments)
            print(f"💸 {debt:.2f} ₪ إجمالي الديون")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البيانات النهائية: {e}")
        return False

def launch_final():
    """تشغيل النسخة النهائية"""
    print("\n🚀 تشغيل النسخة النهائية...")
    
    try:
        process = subprocess.Popen([
            sys.executable, 
            'phone_doctor_final.py'
        ])
        
        print("🏆 تم تشغيل النسخة النهائية بنجاح!")
        print("💎 ابحث عن نافذة تسجيل الدخول على شاشتك")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في تشغيل النسخة النهائية: {e}")
        return False

def show_final_info():
    """عرض معلومات النسخة النهائية"""
    print("\n" + "🌟" * 50)
    print("معلومات تسجيل الدخول:")
    print("🌟" * 50)
    print("👑 المدير الأعلى:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("   الصلاحيات: كاملة بدون قيود")
    print()
    print("💼 موظف المبيعات:")
    print("   اسم المستخدم: sales")
    print("   كلمة المرور: sales123")
    print("   الصلاحيات: محدودة (تحتاج ترخيص)")
    print("🌟" * 50)

def show_final_features():
    """عرض الميزات النهائية"""
    print("\nالميزات النهائية المتاحة:")
    print("🏆 واجهة احترافية متطورة")
    print("💎 تصميم عصري وأنيق")
    print("🎨 ألوان متناسقة ومريحة للعين")
    print("⚡ أداء سريع ومستقر")
    print("🔒 نظام أمان متقدم")
    print("📊 بيانات حقيقية شاملة:")
    print()
    print("   🔧 إدارة الصيانة:")
    print("      • 50 أمر صيانة مع تفاصيل كاملة")
    print("      • سيريال الجهاز والقطع المستبدلة")
    print("      • التكلفة والحالة وتاريخ الإصلاح")
    print()
    print("   👥 إدارة العملاء:")
    print("      • 20 عميل مع بيانات كاملة")
    print("      • حسابات مالية تفصيلية")
    print("      • تتبع الديون والمدفوعات")
    print("      • حساب صافي الأرباح")
    print()
    print("   💳 إدارة الشيكات:")
    print("      • 25 شيك بحالات مختلفة")
    print("      • تفاصيل البنك والمستفيد")
    print("      • تواريخ الإصدار والاستحقاق")
    print("      • إحصائيات شاملة")
    print()
    print("   📈 لوحة التحكم:")
    print("      • إحصائيات حية ومحدثة")
    print("      • بطاقات معلومات تفاعلية")
    print("      • ملخص مالي شامل")

def main():
    """الدالة الرئيسية النهائية"""
    print_final_header()
    
    # فحص المتطلبات
    if not check_final_requirements():
        print("\n❌ فشل فحص المتطلبات النهائية")
        input("اضغط Enter للخروج...")
        return
    
    # فحص الملفات
    if not check_final_files():
        print("\n❌ الملفات النهائية مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    # فحص البيانات
    if not check_final_data():
        print("\n❌ البيانات النهائية غير متاحة")
        print("💡 تلميح: شغل ملفات إضافة البيانات التجريبية أولاً")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع الفحوصات النهائية نجحت!")
    
    # تشغيل النسخة النهائية
    if launch_final():
        show_final_info()
        show_final_features()
        print("\n🎉 تم تشغيل Phone Doctor Final Edition بنجاح!")
        print("🏆 استمتع بالتجربة الاحترافية الكاملة!")
    else:
        print("\n❌ فشل في تشغيل النسخة النهائية")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
