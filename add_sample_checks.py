#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للشيكات - Phone Doctor v2.0
Add Sample Checks Data

المطور: محمد الشوامرة - 0566000140
"""

import sqlite3
import random
from datetime import datetime, timed<PERSON><PERSON>

def add_sample_checks():
    """إضافة شيكات تجريبية"""
    
    print("💳 إضافة بيانات تجريبية للشيكات...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # إنشاء جدول الشيكات إذا لم يكن موجوداً
        checks_table = '''
            CREATE TABLE IF NOT EXISTS checks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                check_number TEXT NOT NULL UNIQUE,
                amount REAL NOT NULL,
                bank_name TEXT NOT NULL,
                issue_date DATE NOT NULL,
                due_date DATE NOT NULL,
                payee TEXT NOT NULL,
                payer TEXT NOT NULL,
                status TEXT DEFAULT 'معلق',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                cashed_date TIMESTAMP,
                bounced_date TIMESTAMP,
                created_by TEXT,
                updated_by TEXT
            )
        '''
        
        cursor.execute(checks_table)
        
        # حذف البيانات الموجودة
        cursor.execute("DELETE FROM checks")
        
        # البنوك الفلسطينية
        banks = [
            "بنك فلسطين",
            "البنك الإسلامي الفلسطيني",
            "بنك الاستثمار الفلسطيني",
            "البنك الوطني",
            "بنك القدس",
            "بنك فلسطين الأهلي"
        ]
        
        # أسماء المستفيدين (أصحاب المحل)
        payees = [
            "محل فون دكتور للهواتف",
            "مؤسسة الشوامرة للتكنولوجيا",
            "شركة إصلاح الهواتف المتقدمة",
            "محمد الشوامرة - فون دكتور"
        ]
        
        # أسماء الدافعين (العملاء)
        payers = [
            "أحمد محمد علي",
            "فاطمة حسن محمود",
            "محمد عبد الله سالم",
            "نور الدين أحمد",
            "سارة محمد يوسف",
            "عبد الرحمن الشوامرة",
            "ليلى أحمد الخالدي",
            "يوسف محمد النجار",
            "خالد عبد الله زايد",
            "منى سعيد الأغا",
            "عمر حسام الدين",
            "رنا محمود عاشور",
            "طارق فايز النحال",
            "سمير عبد الفتاح",
            "هدى محمد الكرد",
            "باسم أحمد قديح",
            "ريم سالم الهور",
            "نادر محمد شراب",
            "شركة الاتصالات المتقدمة",
            "مؤسسة التكنولوجيا الحديثة"
        ]
        
        # حالات الشيكات
        statuses = ['معلق', 'محصل', 'مرتد', 'ملغي']
        status_weights = [0.5, 0.3, 0.1, 0.1]  # احتمالية كل حالة
        
        # ملاحظات تجريبية
        notes_list = [
            "دفعة مقابل إصلاح iPhone 12",
            "تسديد فاتورة صيانة Samsung Galaxy",
            "دفعة أولى لإصلاح جهاز iPad",
            "مقابل استبدال شاشة Huawei",
            "تسديد كامل لإصلاح جهاز Xiaomi",
            "دفعة مقابل قطع غيار iPhone",
            "تسديد فاتورة إصلاح متعددة",
            "مقابل خدمات صيانة شهرية",
            "دفعة نهائية لإصلاح الجهاز",
            "تسديد دين سابق",
            "",  # بدون ملاحظات
            "",
            ""
        ]
        
        print(f"📝 إضافة 25 شيك تجريبي...")
        
        # إضافة الشيكات
        for i in range(25):
            # رقم الشيك (عشوائي من 6-8 أرقام)
            check_number = str(random.randint(100000, ********))
            
            # المبلغ (بين 100-5000 شيكل)
            amount = round(random.uniform(100, 5000), 2)
            
            # البنك
            bank_name = random.choice(banks)
            
            # تاريخ الإصدار (آخر 3 أشهر)
            days_ago = random.randint(1, 90)
            issue_date = datetime.now() - timedelta(days=days_ago)
            
            # تاريخ الاستحقاق (30-180 يوم من تاريخ الإصدار)
            due_days = random.randint(30, 180)
            due_date = issue_date + timedelta(days=due_days)
            
            # المستفيد والدافع
            payee = random.choice(payees)
            payer = random.choice(payers)
            
            # الحالة
            status = random.choices(statuses, weights=status_weights)[0]
            
            # الملاحظات
            notes = random.choice(notes_list)
            
            # تواريخ إضافية حسب الحالة
            cashed_date = None
            bounced_date = None
            
            if status == 'محصل':
                # تاريخ التحصيل بين تاريخ الاستحقاق واليوم
                if due_date <= datetime.now():
                    cash_days = random.randint(0, (datetime.now() - due_date).days)
                    cashed_date = due_date + timedelta(days=cash_days)
                else:
                    cashed_date = datetime.now() - timedelta(days=random.randint(1, 30))
            elif status == 'مرتد':
                # تاريخ الارتداد بعد تاريخ الاستحقاق
                bounce_days = random.randint(1, 15)
                bounced_date = due_date + timedelta(days=bounce_days)
            
            # إدراج الشيك
            cursor.execute('''
                INSERT INTO checks (
                    check_number, amount, bank_name, issue_date, due_date, 
                    payee, payer, status, notes, cashed_date, bounced_date, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                check_number, amount, bank_name, 
                issue_date.strftime('%Y-%m-%d'), 
                due_date.strftime('%Y-%m-%d'),
                payee, payer, status, notes,
                cashed_date.strftime('%Y-%m-%d %H:%M:%S') if cashed_date else None,
                bounced_date.strftime('%Y-%m-%d %H:%M:%S') if bounced_date else None,
                'admin'
            ))
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        
        # عرض إحصائيات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM checks")
        total_checks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM checks WHERE status = 'معلق'")
        pending_checks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM checks WHERE status = 'محصل'")
        cashed_checks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM checks WHERE status = 'مرتد'")
        bounced_checks = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(amount) FROM checks")
        total_amount = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(amount) FROM checks WHERE status = 'محصل'")
        cashed_amount = cursor.fetchone()[0] or 0
        
        # فحص الشيكات المتأخرة
        cursor.execute("SELECT COUNT(*) FROM checks WHERE status = 'معلق' AND due_date < date('now')")
        overdue_checks = cursor.fetchone()[0]
        
        conn.close()
        
        print("\n📈 إحصائيات الشيكات:")
        print(f"  💳 إجمالي الشيكات: {total_checks}")
        print(f"  ⏳ شيكات معلقة: {pending_checks}")
        print(f"  ✅ شيكات محصلة: {cashed_checks}")
        print(f"  ❌ شيكات مرتدة: {bounced_checks}")
        print(f"  ⚠️ شيكات متأخرة: {overdue_checks}")
        print(f"  💰 إجمالي المبلغ: {total_amount:.2f} ₪")
        print(f"  💵 المبلغ المحصل: {cashed_amount:.2f} ₪")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_sample_checks()
