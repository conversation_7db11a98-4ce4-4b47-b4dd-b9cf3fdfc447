# Phone Doctor - نظام إدارة محلات صيانة الهواتف

![Phone Doctor](https://img.shields.io/badge/Phone%20Doctor-v1.0.0-blue)
![Python](https://img.shields.io/badge/Python-3.7+-green)
![License](https://img.shields.io/badge/License-Proprietary-red)

## 📱 نظرة عامة

Phone Doctor هو نظام إدارة شامل ومتطور مصمم خصيصاً لمحلات صيانة الهواتف الخلوية. يوفر النظام جميع الأدوات اللازمة لإدارة العمليات اليومية بكفاءة عالية.

**المطور:** محمد الشوامرة  
**الهاتف:** **********  
**جميع الحقوق محفوظة © 2024**

## ✨ الميزات الرئيسية

### 🏠 لوحة التحكم التفاعلية
- إحصائيات مباشرة ومحدثة
- بطاقات معلومات تفاعلية
- أعمال سريعة للمهام الشائعة
- عرض الوقت والتاريخ

### 🔧 نظام الصيانة المتقدم
- تسجيل الأجهزة الجديدة
- تتبع حالة الإصلاح (قيد الانتظار، جاري الإصلاح، تم الإصلاح، تم التسليم)
- إدارة بيانات العملاء
- ملاحظات الفني التفصيلية
- تقدير وتسجيل التكاليف

### 📦 إدارة المخزون الذكية
- إضافة وتعديل قطع الغيار
- نظام باركود متقدم
- تنبيهات المخزون المنخفض
- تصنيف القطع حسب الفئات
- تتبع المواقع والموردين

### 🏪 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- تتبع الأرصدة والمدفوعات
- سجل المشتريات
- معلومات الاتصال الكاملة

### 💰 النظام المالي (قيد التطوير)
- إدارة المبيعات
- تتبع الشيكات
- الفواتير والمصاريف
- التقارير المالية

### 📊 نظام التقارير (قيد التطوير)
- تقارير الصيانة
- تقارير المبيعات والأرباح
- تقارير المخزون
- تصدير PDF و Excel

## 🛠️ التقنيات المستخدمة

- **اللغة:** Python 3.7+
- **واجهة المستخدم:** Tkinter
- **قاعدة البيانات:** SQLite
- **التجميع:** PyInstaller
- **الترميز:** UTF-8 (دعم كامل للعربية)

## 📁 هيكل المشروع

```
phone-doctor/
├── main.py                 # الملف الرئيسي (PyQt5)
├── main_tkinter.py         # الملف الرئيسي (Tkinter)
├── test_db.py             # اختبار قاعدة البيانات
├── build_exe.py           # سكريبت التجميع
├── requirements.txt       # المتطلبات
├── database/
│   ├── __init__.py
│   └── db_manager.py      # مدير قاعدة البيانات
├── ui/
│   ├── __init__.py
│   ├── main_window.py     # النافذة الرئيسية (PyQt5)
│   ├── sidebar.py         # الشريط الجانبي
│   ├── dashboard.py       # لوحة التحكم
│   ├── splash_screen.py   # شاشة البداية
│   ├── repair_dialogs.py  # نوافذ الصيانة
│   ├── inventory_dialogs.py # نوافذ المخزون
│   └── supplier_dialogs.py  # نوافذ الموردين
├── utils/
│   ├── __init__.py
│   └── config.py          # إدارة الإعدادات
├── assets/                # الأصول والموارد
└── docs/                  # الوثائق
```

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.7 أو أحدث
- نظام التشغيل: Windows 7+ (مُحسَّن لـ Windows)

### التثبيت

1. **استنساخ المشروع:**
```bash
git clone [repository-url]
cd phone-doctor
```

2. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

3. **اختبار قاعدة البيانات:**
```bash
python test_db.py
```

4. **تشغيل البرنامج:**
```bash
python main_tkinter.py
```

### التشغيل السريع
```bash
# Windows
run_phone_doctor.bat

# أو مباشرة
python main_tkinter.py
```

## 🔨 تجميع البرنامج إلى EXE

```bash
python build_exe.py
```

سيتم إنشاء مجلد `PhoneDoctor_Distribution` يحتوي على:
- `PhoneDoctor.exe` - الملف التنفيذي
- `phone_doctor.db` - قاعدة البيانات
- `config.json` - ملف الإعدادات
- `README.txt` - دليل المستخدم
- `تشغيل البرنامج.bat` - ملف التشغيل السريع

## 🗄️ قاعدة البيانات

### الجداول الرئيسية

- **customers** - بيانات العملاء
- **repairs** - سجل الصيانات
- **inventory** - المخزون
- **suppliers** - الموردين
- **sales** - المبيعات
- **checks** - الشيكات
- **purchases** - المشتريات
- **expenses** - المصاريف
- **settings** - الإعدادات

### النسخ الاحتياطي
- نسخ احتياطي تلقائي
- استعادة من النسخ الاحتياطية
- تصدير واستيراد البيانات

## 🎨 الثيمات والتصميم

### الثيمات المتاحة
- **أزرق** (افتراضي)
- **أخضر**
- **بنفسجي**
- **برتقالي**
- **داكن**
- **سماوي**

### خصائص التصميم
- واجهة عربية بالكامل
- تصميم عصري ونظيف
- ألوان متدرجة جذابة
- أيقونات تعبيرية
- استجابة سريعة

## 🧪 الاختبار

### اختبار قاعدة البيانات
```bash
python test_db.py
```

### اختبار الواجهة
```bash
python main_tkinter.py
```

## 📋 المهام المكتملة

- [x] تصميم قاعدة البيانات
- [x] الواجهة الرئيسية والتصميم
- [x] نظام الصيانة
- [x] نظام المخزون
- [x] نظام الموردين
- [x] لوحة التحكم التفاعلية
- [x] النسخ الاحتياطي
- [x] تجميع EXE

## 🔄 المهام قيد التطوير

- [ ] نظام المبيعات المتقدم
- [ ] الشاشات المالية
- [ ] نظام التقارير
- [ ] طباعة الفواتير
- [ ] إدارة المستخدمين
- [ ] التنبيهات الصوتية

## 🐛 الأخطاء المعروفة

- لا توجد أخطاء معروفة حالياً

## 🤝 المساهمة

هذا مشروع خاص. للاستفسارات أو التحسينات، يرجى التواصل مع المطور.

## 📞 الدعم الفني

**محمد الشوامرة**  
📞 **الهاتف:** **********  
📧 **للاستفسارات الفنية والدعم**

### ساعات الدعم
- الأحد - الخميس: 9:00 ص - 6:00 م
- الجمعة - السبت: حسب الحاجة

## 📄 الترخيص

جميع الحقوق محفوظة © 2024 محمد الشوامرة. هذا البرنامج محمي بحقوق الطبع والنشر.

## 🏆 الإصدارات

### v1.0.0 (الحالي)
- الإصدار الأول
- الميزات الأساسية مكتملة
- واجهة مستخدم عربية
- نظام إدارة شامل

## 📈 خطط المستقبل

- تطوير نسخة ويب
- تطبيق موبايل مصاحب
- تكامل مع أنظمة الدفع
- تقارير متقدمة وذكية
- نظام إشعارات متطور

---

**Phone Doctor v1.0.0**  
**Professional Phone Repair Shop Management System**  
**Developed with ❤️ by Mohammad Shawamreh**
