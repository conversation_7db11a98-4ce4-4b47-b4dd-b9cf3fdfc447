#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0 Professional Edition النهائي
Final Phone Doctor Launcher

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess
import time

def print_header():
    """طباعة الهيدر"""
    print("=" * 70)
    print("Phone Doctor v2.0 Professional Edition")
    print("نظام إدارة محل الهواتف المتطور")
    print("المطور: محمد الشوامرة - 0566000140")
    print("=" * 70)

def check_system():
    """فحص النظام"""
    print("\n[1/4] فحص متطلبات النظام...")
    
    # فحص Python
    if sys.version_info.major < 3:
        print("❌ يتطلب Python 3.0 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص tkinter
    try:
        import tkinter
        print("✅ Tkinter متاح")
    except ImportError:
        print("❌ Tkinter غير متاح")
        return False
    
    return True

def check_files():
    """فحص الملفات"""
    print("\n[2/4] فحص الملفات...")
    
    # الملفات الأساسية
    basic_files = [
        'phone_doctor_no_icons.py',
        'ui/theme_manager.py'
    ]
    
    # الملفات المتقدمة
    advanced_files = [
        'phone_doctor_working.py',
        'ui/customers_manager_advanced.py',
        'ui/checks_manager.py',
        'ui/repairs_manager_simple.py'
    ]
    
    # فحص الملفات الأساسية
    basic_available = True
    for file in basic_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            basic_available = False
    
    # فحص الملفات المتقدمة
    advanced_available = True
    for file in advanced_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file}")
            advanced_available = False
    
    return basic_available, advanced_available

def check_database():
    """فحص قاعدة البيانات"""
    print("\n[3/4] فحص قاعدة البيانات...")
    
    if not os.path.exists('database'):
        print("⚠️ مجلد database غير موجود")
        return False
    
    if not os.path.exists('database/phone_doctor.db'):
        print("⚠️ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        import sqlite3
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ قاعدة البيانات متاحة - {len(tables)} جدول")
        
        # فحص البيانات
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT COUNT(*) FROM repairs")
            repairs = cursor.fetchone()[0]
            print(f"✅ أوامر الصيانة: {repairs}")
        except:
            print("⚠️ جدول الصيانة غير متاح")
        
        try:
            cursor.execute("SELECT COUNT(*) FROM customers")
            customers = cursor.fetchone()[0]
            print(f"✅ العملاء: {customers}")
        except:
            print("⚠️ جدول العملاء غير متاح")
        
        try:
            cursor.execute("SELECT COUNT(*) FROM checks")
            checks = cursor.fetchone()[0]
            print(f"✅ الشيكات: {checks}")
        except:
            print("⚠️ جدول الشيكات غير متاح")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def launch_program(program_file):
    """تشغيل البرنامج"""
    print(f"\n[4/4] تشغيل {program_file}...")
    
    try:
        process = subprocess.Popen([
            sys.executable, 
            program_file
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("✅ تم تشغيل البرنامج بنجاح!")
        print("📱 ابحث عن نافذة Phone Doctor على شاشتك")
        
        # انتظار قصير للتأكد من التشغيل
        time.sleep(2)
        
        if process.poll() is None:
            print("✅ البرنامج يعمل بشكل صحيح")
            return True
        else:
            stdout, stderr = process.communicate()
            print("❌ البرنامج توقف بشكل غير متوقع")
            if stderr:
                error_msg = stderr.decode('utf-8', errors='ignore')
                print(f"خطأ: {error_msg[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        return False

def show_login_info():
    """عرض معلومات تسجيل الدخول"""
    print("\n" + "=" * 50)
    print("معلومات تسجيل الدخول:")
    print("=" * 50)
    print("المدير:")
    print("  اسم المستخدم: admin")
    print("  كلمة المرور: admin123")
    print("  الصلاحيات: كاملة بدون ترخيص")
    print()
    print("موظف المبيعات:")
    print("  اسم المستخدم: sales")
    print("  كلمة المرور: sales123")
    print("  الصلاحيات: محدودة (يحتاج ترخيص)")
    print("=" * 50)

def show_features():
    """عرض الميزات المتاحة"""
    print("\nالميزات المتاحة:")
    print("✅ إدارة الصيانة المتطورة (50 أمر)")
    print("✅ إدارة العملاء والحسابات (20 عميل)")
    print("✅ إدارة الشيكات (25 شيك)")
    print("✅ نظام التقارير المتقدم")
    print("✅ واجهة احترافية متطورة")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص النظام
    if not check_system():
        print("\n❌ فشل فحص النظام")
        input("اضغط Enter للإغلاق...")
        return
    
    # فحص الملفات
    basic_ok, advanced_ok = check_files()
    
    if not basic_ok:
        print("\n❌ الملفات الأساسية مفقودة")
        input("اضغط Enter للإغلاق...")
        return
    
    # فحص قاعدة البيانات
    db_ok = check_database()
    
    if not db_ok:
        print("\n⚠️ قاعدة البيانات غير متاحة")
        print("💡 تلميح: شغل ملفات إضافة البيانات التجريبية أولاً")
    
    print("\n" + "=" * 50)
    print("اختيار نسخة البرنامج:")
    print("=" * 50)
    
    if advanced_ok and db_ok:
        print("1. النسخة المتطورة (مع جميع الميزات والأيقونات)")
        print("2. النسخة المبسطة (بدون أيقونات)")
        print("3. إلغاء")
        
        choice = input("\nاختر النسخة (1-3): ").strip()
        
        if choice == "1":
            program_file = "phone_doctor_working.py"
            print("\n🚀 تشغيل النسخة المتطورة...")
        elif choice == "2":
            program_file = "phone_doctor_no_icons.py"
            print("\n🚀 تشغيل النسخة المبسطة...")
        elif choice == "3":
            print("تم الإلغاء")
            return
        else:
            print("اختيار غير صحيح")
            return
    else:
        print("النسخة المبسطة فقط متاحة")
        program_file = "phone_doctor_no_icons.py"
        input("اضغط Enter للمتابعة...")
    
    # تشغيل البرنامج
    if launch_program(program_file):
        show_login_info()
        show_features()
        print("\n🎉 تم تشغيل Phone Doctor بنجاح!")
    else:
        print("\n❌ فشل في تشغيل البرنامج")
    
    input("\nاضغط Enter للإغلاق...")

if __name__ == "__main__":
    main()
