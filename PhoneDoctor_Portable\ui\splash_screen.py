#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة البداية - Splash Screen
Phone Doctor Management System

المطور: محمد الشوامرة - 0566000140
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QProgressBar, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QLinearGradient, QColor, QPen

class SplashScreen(QWidget):
    finished = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة شاشة البداية"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(500, 350)
        
        # توسيط الشاشة
        self.center_on_screen()
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إطار الخلفية
        self.background_frame = QFrame()
        self.background_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196F3, stop:0.5 #1976D2, stop:1 #0D47A1);
                border-radius: 20px;
                border: 2px solid #FFFFFF;
            }
        """)
        
        # تخطيط المحتوى
        content_layout = QVBoxLayout(self.background_frame)
        content_layout.setContentsMargins(40, 40, 40, 40)
        content_layout.setSpacing(20)
        
        # شعار التطبيق
        logo_label = QLabel("📱")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 80px;
                color: white;
                background: transparent;
            }
        """)
        
        # اسم التطبيق
        app_name = QLabel("Phone Doctor")
        app_name.setAlignment(Qt.AlignCenter)
        app_name.setFont(QFont("Arial", 24, QFont.Bold))
        app_name.setStyleSheet("""
            QLabel {
                color: white;
                background: transparent;
                margin: 10px 0;
            }
        """)
        
        # وصف التطبيق
        app_desc = QLabel("نظام إدارة محلات صيانة الهواتف")
        app_desc.setAlignment(Qt.AlignCenter)
        app_desc.setFont(QFont("Arial", 12))
        app_desc.setStyleSheet("""
            QLabel {
                color: #E3F2FD;
                background: transparent;
                margin-bottom: 20px;
            }
        """)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid white;
                border-radius: 10px;
                background-color: rgba(255, 255, 255, 0.3);
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #8BC34A);
                border-radius: 8px;
            }
        """)
        
        # نص الحالة
        self.status_label = QLabel("جاري التحميل...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 10))
        self.status_label.setStyleSheet("""
            QLabel {
                color: #E3F2FD;
                background: transparent;
                margin-top: 10px;
            }
        """)
        
        # معلومات المطور
        developer_info = QLabel("المطور: محمد الشوامرة - 📞 0566000140")
        developer_info.setAlignment(Qt.AlignCenter)
        developer_info.setFont(QFont("Arial", 9))
        developer_info.setStyleSheet("""
            QLabel {
                color: #BBDEFB;
                background: transparent;
                margin-top: 20px;
            }
        """)
        
        # إضافة العناصر للتخطيط
        content_layout.addWidget(logo_label)
        content_layout.addWidget(app_name)
        content_layout.addWidget(app_desc)
        content_layout.addWidget(self.progress_bar)
        content_layout.addWidget(self.status_label)
        content_layout.addStretch()
        content_layout.addWidget(developer_info)
        
        main_layout.addWidget(self.background_frame)
        self.setLayout(main_layout)
        
    def center_on_screen(self):
        """توسيط الشاشة على الشاشة الرئيسية"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def setup_timer(self):
        """إعداد مؤقت التحميل"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_progress)
        self.progress = 0
        self.loading_steps = [
            "تحميل قاعدة البيانات...",
            "تحميل الإعدادات...",
            "تحميل الواجهات...",
            "تحميل الموارد...",
            "إعداد النظام...",
            "اكتمل التحميل!"
        ]
        self.current_step = 0
        self.timer.start(500)  # تحديث كل 500 مللي ثانية
    
    def update_progress(self):
        """تحديث شريط التقدم"""
        self.progress += 20
        self.progress_bar.setValue(self.progress)
        
        if self.current_step < len(self.loading_steps):
            self.status_label.setText(self.loading_steps[self.current_step])
            self.current_step += 1
        
        if self.progress >= 100:
            self.timer.stop()
            QTimer.singleShot(500, self.finish_loading)
    
    def finish_loading(self):
        """إنهاء التحميل وإرسال إشارة الانتهاء"""
        self.finished.emit()
        self.close()
    
    def paintEvent(self, event):
        """رسم تأثيرات إضافية"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم ظل خفيف
        painter.setPen(QPen(QColor(0, 0, 0, 50), 3))
        painter.drawRoundedRect(3, 3, self.width()-6, self.height()-6, 20, 20)
