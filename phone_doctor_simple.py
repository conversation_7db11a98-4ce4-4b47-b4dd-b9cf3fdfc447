#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor - نسخة مبسطة للتجميع
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database.db_manager import DatabaseManager
    from utils.config import Config
    from ui.modern_styles import ModernStyles
except ImportError as e:
    print(f"خطأ في الاستيراد: {e}")

class SimplePhoneDoctorApp:
    """تطبيق Phone Doctor المبسط"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Phone Doctor - نظام إدارة محلات صيانة الهواتف")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # محاولة تحميل الأنماط العصرية
        try:
            self.styles = ModernStyles()
            self.modern_ui = True
            print("✅ تم تحميل التصميم العصري")
        except:
            self.modern_ui = False
            print("⚠️ استخدام التصميم التقليدي")
        
        # تهيئة قاعدة البيانات
        try:
            self.db_manager = DatabaseManager()
            self.config = Config()
            
            if not self.db_manager.initialize_database():
                messagebox.showwarning("تحذير", "فشل في تهيئة قاعدة البيانات")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات: {e}")
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد الواجهة"""
        if self.modern_ui:
            self.setup_modern_ui()
        else:
            self.setup_classic_ui()
    
    def setup_modern_ui(self):
        """إعداد الواجهة العصرية"""
        try:
            # تكوين النافذة
            self.root.configure(bg=self.styles.colors['bg_primary'])
            
            # الإطار الرئيسي
            main_frame = tk.Frame(
                self.root,
                bg=self.styles.colors['bg_primary'],
                relief='flat',
                bd=0
            )
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # العنوان
            title_label = tk.Label(
                main_frame,
                text="📱 Phone Doctor - النسخة العصرية",
                bg=self.styles.colors['bg_primary'],
                fg=self.styles.colors['text_primary'],
                font=self.styles.fonts['heading_large']
            )
            title_label.pack(pady=(0, 30))
            
            # بطاقة رئيسية
            card_frame = tk.Frame(
                main_frame,
                bg=self.styles.colors['bg_card'],
                relief='flat',
                bd=1,
                highlightbackground=self.styles.colors['border_light'],
                highlightthickness=1
            )
            card_frame.pack(fill=tk.BOTH, expand=True)
            
            # محتوى البطاقة
            content_frame = tk.Frame(
                card_frame,
                bg=self.styles.colors['bg_card'],
                relief='flat',
                bd=0
            )
            content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
            
            # رسالة ترحيب
            welcome_label = tk.Label(
                content_frame,
                text="🎉 مرحباً بك في Phone Doctor!\n\n✨ التصميم العصري يعمل بشكل مثالي\n🎨 جميع الأنماط والألوان محملة بنجاح\n📱 النظام جاهز للاستخدام",
                bg=self.styles.colors['bg_card'],
                fg=self.styles.colors['text_primary'],
                font=self.styles.fonts['body_large'],
                justify=tk.CENTER
            )
            welcome_label.pack(expand=True)
            
            # أزرار الاختبار
            buttons_frame = tk.Frame(
                content_frame,
                bg=self.styles.colors['bg_card'],
                relief='flat',
                bd=0
            )
            buttons_frame.pack(fill=tk.X, pady=(30, 0))
            
            # زر اختبار قاعدة البيانات
            db_btn = tk.Button(
                buttons_frame,
                text="🗄️ اختبار قاعدة البيانات",
                command=self.test_database,
                **self.styles.get_button_style('primary')
            )
            db_btn.pack(side=tk.LEFT, padx=(0, 15))
            
            # زر اختبار الإعدادات
            config_btn = tk.Button(
                buttons_frame,
                text="⚙️ اختبار الإعدادات",
                command=self.test_config,
                **self.styles.get_button_style('secondary')
            )
            config_btn.pack(side=tk.LEFT, padx=7)
            
            # زر حول البرنامج
            about_btn = tk.Button(
                buttons_frame,
                text="ℹ️ حول البرنامج",
                command=self.show_about,
                **self.styles.get_button_style('info')
            )
            about_btn.pack(side=tk.LEFT, padx=(15, 0))
            
            # معلومات المطور
            dev_frame = tk.Frame(
                main_frame,
                bg=self.styles.colors['bg_primary'],
                relief='flat',
                bd=0
            )
            dev_frame.pack(fill=tk.X, pady=(20, 0))
            
            dev_label = tk.Label(
                dev_frame,
                text="👨‍💻 المطور: محمد الشوامرة - 📞 0566000140",
                bg=self.styles.colors['bg_primary'],
                fg=self.styles.colors['text_muted'],
                font=self.styles.fonts['caption']
            )
            dev_label.pack()
            
            # الوقت
            self.time_label = tk.Label(
                dev_frame,
                text="",
                bg=self.styles.colors['bg_primary'],
                fg=self.styles.colors['text_secondary'],
                font=self.styles.fonts['body_small']
            )
            self.time_label.pack(pady=(5, 0))
            
            self.update_time()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إعداد الواجهة العصرية: {e}")
            self.setup_classic_ui()
    
    def setup_classic_ui(self):
        """إعداد الواجهة التقليدية"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="📱 Phone Doctor - نظام إدارة محلات صيانة الهواتف",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # رسالة
        message_label = ttk.Label(
            main_frame,
            text="النظام يعمل بالواجهة التقليدية\nجميع الوظائف متاحة",
            font=("Arial", 12),
            justify=tk.CENTER
        )
        message_label.pack(expand=True)
        
        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="اختبار قاعدة البيانات", 
                  command=self.test_database).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حول البرنامج", 
                  command=self.show_about).pack(side=tk.LEFT, padx=5)
        
        # معلومات المطور
        dev_label = ttk.Label(
            main_frame,
            text="المطور: محمد الشوامرة - 0566000140",
            font=("Arial", 9)
        )
        dev_label.pack(pady=(20, 0))
    
    def test_database(self):
        """اختبار قاعدة البيانات"""
        try:
            if hasattr(self, 'db_manager'):
                if self.db_manager.connect():
                    messagebox.showinfo("نجح", "✅ قاعدة البيانات تعمل بشكل صحيح!")
                else:
                    messagebox.showerror("خطأ", "❌ فشل في الاتصال بقاعدة البيانات")
            else:
                messagebox.showwarning("تحذير", "⚠️ قاعدة البيانات غير مهيأة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في اختبار قاعدة البيانات: {e}")
    
    def test_config(self):
        """اختبار الإعدادات"""
        try:
            if hasattr(self, 'config'):
                shop_name = self.config.get('shop.name', 'غير محدد')
                messagebox.showinfo("الإعدادات", f"✅ اسم المحل: {shop_name}")
            else:
                messagebox.showwarning("تحذير", "⚠️ ملف الإعدادات غير متاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الإعدادات: {e}")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
🎨 Phone Doctor - النسخة العصرية

📱 نظام إدارة محلات صيانة الهواتف الاحترافي

✨ الميزات:
• تصميم عصري متطور
• واجهة عربية كاملة
• قاعدة بيانات قوية
• نسخ احتياطي آمن

👨‍💻 المطور: محمد الشوامرة
📞 الهاتف: 0566000140
📧 البريد: <EMAIL>

© 2024 جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def update_time(self):
        """تحديث الوقت"""
        if hasattr(self, 'time_label'):
            now = datetime.now()
            time_str = now.strftime("📅 %Y-%m-%d | ⏰ %H:%M:%S")
            self.time_label.configure(text=time_str)
            self.root.after(1000, self.update_time)
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 تشغيل Phone Doctor...")
        app = SimplePhoneDoctorApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")

if __name__ == "__main__":
    main()
