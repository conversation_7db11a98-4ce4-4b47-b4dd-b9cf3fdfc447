#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل Phone Doctor v2.0 Magic Edition المباشر
Direct Magic Edition Launcher

المطور: محمد الشوامرة - 0566000140
"""

import sys
import os
import subprocess

def print_magic_header():
    """طباعة الهيدر السحري"""
    print("✨" * 35)
    print("🔮 Phone Doctor v2.0 Magic Edition 🔮")
    print("✨ نسخة سحرية مع تأثيرات متطورة ✨")
    print("🎭 المطور: محمد الشوامرة - 0566000140 🎭")
    print("✨" * 35)

def check_magic_requirements():
    """فحص متطلبات النسخة السحرية"""
    print("\n🔍 فحص المتطلبات السحرية...")
    
    # فحص Python
    if sys.version_info.major < 3:
        print("❌ يتطلب Python 3.0 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص tkinter
    try:
        import tkinter
        print("✅ Tkinter متاح")
    except ImportError:
        print("❌ Tkinter غير متاح")
        return False
    
    # فحص sqlite3
    try:
        import sqlite3
        print("✅ SQLite3 متاح")
    except ImportError:
        print("❌ SQLite3 غير متاح")
        return False
    
    return True

def check_magic_files():
    """فحص الملفات السحرية"""
    print("\n📁 فحص الملفات السحرية...")
    
    required_files = [
        'phone_doctor_magic.py',
        'database/phone_doctor.db'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            return False
    
    return True

def check_magic_data():
    """فحص البيانات السحرية"""
    print("\n🗄️ فحص البيانات السحرية...")
    
    try:
        import sqlite3
        conn = sqlite3.connect('database/phone_doctor.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ {len(tables)} جدول سحري")
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM repairs")
        repairs = cursor.fetchone()[0]
        print(f"🔧 {repairs} أمر صيانة سحري")
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers = cursor.fetchone()[0]
        print(f"👥 {customers} عميل سحري")
        
        cursor.execute("SELECT COUNT(*) FROM checks")
        checks = cursor.fetchone()[0]
        print(f"💳 {checks} شيك سحري")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البيانات السحرية: {e}")
        return False

def launch_magic():
    """تشغيل السحر"""
    print("\n🚀 تشغيل السحر...")
    
    try:
        process = subprocess.Popen([
            sys.executable, 
            'phone_doctor_magic.py'
        ])
        
        print("✨ تم تشغيل السحر بنجاح!")
        print("🔮 ابحث عن نافذة تسجيل الدخول السحرية على شاشتك")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في تشغيل السحر: {e}")
        return False

def show_magic_info():
    """عرض معلومات السحر"""
    print("\n" + "🌟" * 50)
    print("معلومات تسجيل الدخول السحرية:")
    print("🌟" * 50)
    print("🧙‍♂️ الساحر الأعظم (المدير):")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور السحرية: admin123")
    print("   القوى السحرية: كاملة بدون حدود")
    print()
    print("🧙‍♀️ ساحر المبيعات:")
    print("   اسم المستخدم: sales")
    print("   كلمة المرور السحرية: sales123")
    print("   القوى السحرية: محدودة (تحتاج ترخيص)")
    print("🌟" * 50)

def show_magic_features():
    """عرض الميزات السحرية"""
    print("\nالميزات السحرية المتاحة:")
    print("✨ واجهة سحرية مع تأثيرات متطورة")
    print("🔮 تأثيرات hover سحرية للأزرار")
    print("🌟 تأثيرات النبض والتدرج اللوني")
    print("🎭 ظهور النص تدريجياً بطريقة سحرية")
    print("💫 ألوان ديناميكية تتغير مع التفاعل")
    print("🎨 تصميم احترافي مع لمسات سحرية")
    print("📊 بيانات حقيقية:")
    print("   🔧 50 أمر صيانة مع تفاصيل كاملة")
    print("   👥 20 عميل مع حسابات مالية")
    print("   💳 25 شيك بحالات مختلفة")
    print("   💰 إحصائيات مالية حية")

def main():
    """الدالة الرئيسية السحرية"""
    print_magic_header()
    
    # فحص المتطلبات
    if not check_magic_requirements():
        print("\n❌ فشل فحص المتطلبات السحرية")
        input("اضغط Enter للخروج من عالم السحر...")
        return
    
    # فحص الملفات
    if not check_magic_files():
        print("\n❌ الملفات السحرية مفقودة")
        input("اضغط Enter للخروج من عالم السحر...")
        return
    
    # فحص البيانات
    if not check_magic_data():
        print("\n❌ البيانات السحرية غير متاحة")
        print("💡 تلميح: شغل ملفات إضافة البيانات التجريبية أولاً")
        input("اضغط Enter للخروج من عالم السحر...")
        return
    
    print("\n✅ جميع الفحوصات السحرية نجحت!")
    
    # تشغيل السحر
    if launch_magic():
        show_magic_info()
        show_magic_features()
        print("\n🎉 تم تشغيل Phone Doctor Magic Edition بنجاح!")
        print("🔮 استمتع بالتجربة السحرية!")
    else:
        print("\n❌ فشل في تشغيل السحر")
    
    input("\nاضغط Enter للخروج من عالم السحر...")

if __name__ == "__main__":
    main()
