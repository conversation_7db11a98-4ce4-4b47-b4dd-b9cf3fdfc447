#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Doctor v2.0 Professional Edition - بدون أيقونات
نسخة مبسطة بدون أيقونات لتجنب مشاكل الترميز

المطور: محمد الشوامرة - 0566000140
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DatabaseManager:
    """مدير قاعدة البيانات المبسط"""
    
    def __init__(self):
        self.db_path = 'database/phone_doctor.db'
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if not os.path.exists('database'):
                os.makedirs('database')
            
            self.connection = sqlite3.connect(self.db_path)
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة المستخدمين الافتراضيين
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('admin', 'admin123', 'مدير النظام', 'admin'))
            cursor.execute('INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
                          ('sales', 'sales123', 'موظف المبيعات', 'sales'))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False

class AuthManager:
    """مدير المصادقة المبسط"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.current_user = None
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            cursor = self.db_manager.connection.cursor()
            cursor.execute('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                          (username, password))
            user = cursor.fetchone()
            
            if user:
                self.current_user = {
                    'id': user[0],
                    'username': user[1],
                    'full_name': user[3],
                    'role': user[4]
                }
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

class LoginWindow:
    """نافذة تسجيل الدخول المبسطة"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.root = None
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - تسجيل الدخول")
        self.root.geometry("400x300")
        self.root.configure(bg='#f0f0f0')
        
        # العنوان
        title_label = tk.Label(
            self.root,
            text="Phone Doctor v2.0",
            bg='#f0f0f0',
            fg='#333333',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=20)
        
        # اسم المستخدم
        tk.Label(self.root, text="اسم المستخدم:", bg='#f0f0f0', font=('Arial', 12)).pack(pady=5)
        self.username_entry = tk.Entry(self.root, font=('Arial', 12), width=20)
        self.username_entry.pack(pady=5)
        
        # كلمة المرور
        tk.Label(self.root, text="كلمة المرور:", bg='#f0f0f0', font=('Arial', 12)).pack(pady=5)
        self.password_entry = tk.Entry(self.root, font=('Arial', 12), width=20, show="*")
        self.password_entry.pack(pady=5)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(
            self.root,
            text="دخول",
            command=self.login,
            bg='#007bff',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=15
        )
        login_btn.pack(pady=20)
        
        # معلومات تسجيل الدخول
        info_label = tk.Label(
            self.root,
            text="المدير: admin / admin123\nالمبيعات: sales / sales123",
            bg='#f0f0f0',
            fg='#666666',
            font=('Arial', 10)
        )
        info_label.pack(pady=10)
        
        self.username_entry.focus()
        self.root.bind('<Return>', lambda e: self.login())
        
        self.root.mainloop()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        if self.auth_manager.authenticate(username, password):
            self.root.destroy()
            self.on_success_callback()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)

class MainApplication:
    """التطبيق الرئيسي المبسط"""
    
    def __init__(self, auth_manager, db_manager):
        self.auth_manager = auth_manager
        self.db_manager = db_manager
        self.root = None
        self.current_page = 'dashboard'
        
    def run(self):
        """تشغيل التطبيق"""
        self.setup_main_window()
        self.root.mainloop()
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - نظام إدارة محل الهواتف")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f8fafc')
        
        # الهيدر
        self.create_header()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f8fafc')
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # الشريط الجانبي
        self.create_sidebar(main_frame)
        
        # منطقة المحتوى
        self.content_area = tk.Frame(main_frame, bg='white')
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تحميل لوحة التحكم
        self.load_dashboard()
    
    def create_header(self):
        """إنشاء الهيدر"""
        header_frame = tk.Frame(self.root, bg='#1e40af', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(
            header_frame,
            text="Phone Doctor v2.0 - نظام إدارة محل الهواتف",
            bg='#1e40af',
            fg='white',
            font=('Arial', 16, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        # معلومات المستخدم
        if self.auth_manager.current_user:
            user_label = tk.Label(
                header_frame,
                text=f"مرحباً، {self.auth_manager.current_user['full_name']}",
                bg='#1e40af',
                fg='white',
                font=('Arial', 12)
            )
            user_label.pack(side=tk.RIGHT, padx=20, pady=15)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = tk.Frame(parent, bg='#334155', width=250)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = tk.Label(
            sidebar_frame,
            text="القائمة الرئيسية",
            bg='#334155',
            fg='white',
            font=('Arial', 14, 'bold')
        )
        menu_title.pack(pady=20)
        
        # أزرار القائمة
        menu_items = [
            ("dashboard", "لوحة التحكم"),
            ("sales", "إدارة المبيعات"),
            ("repairs", "إدارة الصيانة"),
            ("inventory", "إدارة المخزون"),
            ("customers", "إدارة العملاء"),
            ("checks", "إدارة الشيكات"),
            ("reports", "التقارير"),
            ("settings", "الإعدادات")
        ]
        
        for page_id, title in menu_items:
            btn = tk.Button(
                sidebar_frame,
                text=title,
                command=lambda p=page_id: self.change_page(p),
                bg='#475569',
                fg='white',
                font=('Arial', 12),
                relief='flat',
                bd=0,
                padx=20,
                pady=10,
                anchor='w'
            )
            btn.pack(fill=tk.X, padx=10, pady=2)
        
        # زر تسجيل الخروج
        logout_btn = tk.Button(
            sidebar_frame,
            text="تسجيل الخروج",
            command=self.logout,
            bg='#ef4444',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            bd=0,
            padx=20,
            pady=10
        )
        logout_btn.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    def change_page(self, page_id):
        """تغيير الصفحة"""
        self.current_page = page_id
        
        # مسح المحتوى الحالي
        for widget in self.content_area.winfo_children():
            widget.destroy()
        
        # تحميل الصفحة المطلوبة
        if page_id == "dashboard":
            self.load_dashboard()
        elif page_id == "customers":
            self.load_customers_page()
        elif page_id == "checks":
            self.load_checks_page()
        elif page_id == "repairs":
            self.load_repairs_page()
        else:
            self.load_placeholder_page(page_id)
    
    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        title_label = tk.Label(
            self.content_area,
            text="لوحة التحكم",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=20)
        
        welcome_label = tk.Label(
            self.content_area,
            text=f"مرحباً بك في Phone Doctor v2.0\n\nالمستخدم: {self.auth_manager.current_user['full_name']}\nالدور: {self.auth_manager.current_user['role']}",
            bg='white',
            fg='#6b7280',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        welcome_label.pack(expand=True)
    
    def load_customers_page(self):
        """تحميل صفحة العملاء"""
        try:
            from ui.customers_manager_advanced import CustomersManager
            CustomersManager(self.content_area, self.db_manager, self.auth_manager.current_user)
        except Exception as e:
            print(f"خطأ في تحميل صفحة العملاء: {e}")
            self.load_placeholder_page("العملاء")
    
    def load_checks_page(self):
        """تحميل صفحة الشيكات"""
        try:
            from ui.checks_manager import ChecksManager
            ChecksManager(self.content_area, self.db_manager, self.auth_manager.current_user)
        except Exception as e:
            print(f"خطأ في تحميل صفحة الشيكات: {e}")
            self.load_placeholder_page("الشيكات")
    
    def load_repairs_page(self):
        """تحميل صفحة الصيانة"""
        try:
            from ui.repairs_manager_simple import RepairsManager
            RepairsManager(self.content_area, self.db_manager, self.auth_manager.current_user)
        except Exception as e:
            print(f"خطأ في تحميل صفحة الصيانة: {e}")
            self.load_placeholder_page("الصيانة")
    
    def load_placeholder_page(self, page_name):
        """تحميل صفحة مؤقتة"""
        title_label = tk.Label(
            self.content_area,
            text=f"صفحة {page_name}",
            bg='white',
            fg='#1f2937',
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=20)
        
        content_label = tk.Label(
            self.content_area,
            text=f"صفحة {page_name} قيد التطوير\n\nسيتم إضافة المزيد من الميزات قريباً",
            bg='white',
            fg='#6b7280',
            font=('Arial', 14),
            justify=tk.CENTER
        )
        content_label.pack(expand=True)
    
    def logout(self):
        """تسجيل الخروج"""
        self.auth_manager.logout()
        self.root.destroy()
        main()

def main():
    """الدالة الرئيسية"""
    print("بدء تشغيل Phone Doctor v2.0...")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    if not db_manager.initialize_database():
        messagebox.showerror("خطأ", "فشل في تهيئة قاعدة البيانات!")
        return
    
    # إنشاء مدير المصادقة
    auth_manager = AuthManager(db_manager)
    
    def on_login_success():
        """عند نجاح تسجيل الدخول"""
        app = MainApplication(auth_manager, db_manager)
        app.run()
    
    # عرض نافذة تسجيل الدخول
    login_window = LoginWindow(auth_manager, on_login_success)
    login_window.show()

if __name__ == "__main__":
    main()
