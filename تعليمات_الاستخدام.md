# 📱 تعليمات استخدام Phone Doctor

## 🤖 نظام ذكي بتصميم الذكاء الاصطناعي

### المطور: محمد الشوامرة - 📞 0566000140

---

## 🚀 كيفية تشغيل البرنامج

### الطريقة الأولى (الأسهل):
1. **انقر نقراً مزدوجاً** على ملف `تشغيل_البرنامج.bat`
2. **انتظر** حتى يفتح البرنامج

### الطريقة الثانية:
1. **افتح موجه الأوامر** في مجلد البرنامج
2. **اكتب الأمر:** `python phone_doctor_working.py`
3. **اضغط Enter**

---

## 🔐 بيانات تسجيل الدخول

### 👨‍💼 المدير (جميع الصلاحيات):
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### 👨‍💻 موظف المبيعات:
- **اسم المستخدم:** `sales`
- **كلمة المرور:** `sales123`

---

## 🎯 كيفية الاستخدام

### 🔐 تسجيل الدخول:
1. **ستظهر نافذة تسجيل دخول** بتصميم الذكاء الاصطناعي
2. **ادخل اسم المستخدم** في الحقل الأول
3. **ادخل كلمة المرور** في الحقل الثاني
4. **انقر على الزر الأزرق الكبير** "🚀 دخول النظام"
5. **ستظهر رسالة ترحيب** ثم ينتقل للنظام الرئيسي

### 🏠 استخدام النظام:
- **لوحة التحكم:** تعرض الإحصائيات العامة
- **إدارة المبيعات:** لتسجيل وإدارة المبيعات
- **إدارة العملاء:** قائمة العملاء مع بياناتهم
- **إدارة المخزون:** قائمة المنتجات والكميات
- **إدارة الصيانة:** متابعة أوامر الصيانة
- **التقارير:** تقارير وإحصائيات مفصلة
- **الإعدادات:** إعدادات النظام

---

## 📊 البيانات التجريبية

### 👥 العملاء المتوفرين:
- أحمد محمد - 0501234567
- فاطمة أحمد - 0507654321
- محمد عبدالله - 0509876543

### 📦 المنتجات المتوفرة:
- شاشة iPhone 14 - 10 قطع - 200 ريال
- بطارية Samsung - 15 قطعة - 120 ريال
- كفر حماية - 50 قطعة - 25 ريال

---

## 🎨 مميزات النظام

### ✅ تصميم الذكاء الاصطناعي:
- ألوان مستقبلية (أسود، أزرق سيان، أخضر نيون)
- واجهة عصرية وجذابة
- تأثيرات بصرية تفاعلية

### ✅ سهولة الاستخدام:
- أزرار كبيرة وواضحة
- نصوص باللغة العربية
- تنقل سهل بين الصفحات

### ✅ قاعدة بيانات متكاملة:
- حفظ جميع البيانات تلقائياً
- بيانات تجريبية جاهزة للاختبار
- نسخ احتياطي آمن

---

## 🔧 متطلبات النظام

### البرامج المطلوبة:
- **Python 3.7** أو أحدث
- **tkinter** (مثبت مع Python عادة)
- **sqlite3** (مثبت مع Python عادة)

### نظام التشغيل:
- **Windows 10/11** (مُختبر)
- **Windows 7/8** (متوافق)
- **Linux/Mac** (متوافق مع تعديلات بسيطة)

---

## 🆘 حل المشاكل الشائعة

### ❌ "Python is not recognized":
- تأكد من تثبيت Python
- أضف Python إلى PATH

### ❌ "No module named tkinter":
- ثبت tkinter: `pip install tk`

### ❌ البرنامج لا يفتح:
- تأكد من وجود جميع الملفات
- شغل من موجه الأوامر لرؤية الأخطاء

---

## 📞 الدعم الفني

### للمساعدة والدعم:
- **المطور:** محمد الشوامرة
- **الهاتف:** 0566000140
- **البريد الإلكتروني:** متوفر عند الطلب

---

## 📝 ملاحظات مهمة

1. **النظام يحفظ البيانات تلقائياً** في ملف `phone_doctor.db`
2. **لا تحذف ملف قاعدة البيانات** لتجنب فقدان البيانات
3. **يمكن تشغيل النظام على عدة أجهزة** بنسخ المجلد كاملاً
4. **البيانات التجريبية** تُنشأ تلقائياً في أول تشغيل

---

## 🎉 استمتع بالاستخدام!

**نظام Phone Doctor جاهز للعمل ويحتوي على جميع الميزات المطلوبة لإدارة محلات صيانة الهواتف بكفاءة عالية.**

---

*تم التطوير بواسطة محمد الشوامرة - 2024*
