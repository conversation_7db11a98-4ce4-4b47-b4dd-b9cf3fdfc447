
# Phone Doctor - نظا<PERSON> إدارة محلات صيانة الهواتف

## المطور: محمد الشوامرة
## الهاتف: 0566000140
## جميع الحقوق محفوظة © 2024

## تعليمات التشغيل السريع:

### المتطلبات:
- نظام التشغيل: Windows 7 أو أحدث
- Python 3.7 أو أحدث

### خطوات التشغيل:

1. **تثبيت Python (إذا لم يكن مثبتاً):**
   - قم بتحميل Python من: https://python.org
   - تأكد من تحديد "Add Python to PATH" أثناء التثبيت

2. **تشغيل البرنامج:**
   - قم بتشغيل ملف "تشغيل_البرنامج.bat"
   - أو افتح موجه الأوامر واكتب: python main_tkinter.py

3. **عند التشغيل الأول:**
   - سيتم إنشاء قاعدة البيانات تلقائياً
   - يمكنك تعديل اسم المحل من تبويب الإعدادات

## الميزات الرئيسية:

✅ **إدارة الصيانات:**
- تسجيل الأجهزة الجديدة
- تتبع حالة الإصلاح
- إدارة بيانات العملاء

✅ **إدارة المخزون:**
- إضافة قطع الغيار
- تنبيهات المخزون المنخفض
- نظام باركود

✅ **إدارة الموردين:**
- قاعدة بيانات الموردين
- تتبع الأرصدة

✅ **لوحة التحكم:**
- إحصائيات مباشرة
- أعمال سريعة

✅ **النسخ الاحتياطي:**
- حفظ واستعادة البيانات

## الملفات المهمة:

- `main_tkinter.py` - الملف الرئيسي للبرنامج
- `phone_doctor.db` - قاعدة البيانات
- `config.json` - ملف الإعدادات
- `دليل_المستخدم.md` - دليل المستخدم المفصل

## الدعم الفني:

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
📞 محمد الشوامرة - 0566000140

## ملاحظات مهمة:

⚠️ **احرص على إنشاء نسخة احتياطية من البيانات بانتظام**
⚠️ **لا تحذف ملف قاعدة البيانات phone_doctor.db**
⚠️ **تأكد من وجود صلاحيات الكتابة في مجلد البرنامج**

---
Phone Doctor v1.0.0 - Professional Phone Repair Shop Management System
© 2024 Mohammad Shawamreh. All rights reserved.
