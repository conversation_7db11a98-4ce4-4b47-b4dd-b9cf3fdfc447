#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة التراخيص
License Management Dialog

المطور: محمد الشوامرة - **********
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import re

class LicenseDialog:
    """نافذة إدارة التراخيص المتطورة"""
    
    def __init__(self, license_manager, on_success_callback=None):
        self.license_manager = license_manager
        self.on_success_callback = on_success_callback
        
        # ألوان النظام العصرية
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6',
            'success': '#10b981',
            'danger': '#ef4444',
            'warning': '#f59e0b',
            'info': '#06b6d4',
            'bg_light': '#f8fafc',
            'bg_gradient': '#667eea',
            'text_dark': '#1f2937',
            'text_light': '#6b7280',
            'accent': '#ec4899'
        }
        
        self.setup_window()
        self.check_current_license()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("Phone Doctor v2.0 - إدارة التراخيص")
        self.root.geometry("700x600")
        self.root.resizable(False, False)
        self.root.configure(bg='white')
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.root.attributes('-topmost', True)
        self.root.grab_set()
        self.root.focus_set()
        
        self.create_ui()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # العنوان الرئيسي
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=100)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(expand=True, fill=tk.BOTH)
        
        title_label = tk.Label(
            header_content,
            text="🔐 تفعيل Phone Doctor v2.0",
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 20, 'bold')
        )
        title_label.pack(expand=True)

        subtitle_label = tk.Label(
            header_content,
            text="يرجى تفعيل البرنامج للمتابعة أو بدء الفترة التجريبية",
            bg=self.colors['primary'],
            fg='#e2e8f0',
            font=('Arial', 12)
        )
        subtitle_label.pack()
        
        # المحتوى الرئيسي
        main_frame = tk.Frame(self.root, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # رسالة ترحيبية
        welcome_frame = tk.Frame(main_frame, bg='#f0f9ff', relief='solid', bd=1)
        welcome_frame.pack(fill=tk.X, pady=(0, 20))

        welcome_content = tk.Frame(welcome_frame, bg='#f0f9ff')
        welcome_content.pack(fill=tk.X, padx=20, pady=15)

        welcome_label = tk.Label(
            welcome_content,
            text="🎉 مرحباً بك في Phone Doctor v2.0!\n\nلاستخدام البرنامج، يرجى اختيار أحد الخيارات التالية:",
            bg='#f0f9ff',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            justify=tk.CENTER
        )
        welcome_label.pack()

        # معلومات الحالة
        self.status_frame = tk.LabelFrame(
            main_frame,
            text="📊 حالة الترخيص",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 14, 'bold'),
            padx=20,
            pady=20
        )
        self.status_frame.pack(fill=tk.X, pady=(0, 20))
        
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب الفترة التجريبية
        self.create_trial_tab()
        
        # تبويب التفعيل الكامل
        self.create_activation_tab()
        
        # تبويب معلومات الترخيص
        self.create_info_tab()
    
    def create_trial_tab(self):
        """إنشاء تبويب الفترة التجريبية"""
        trial_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(trial_frame, text="🆓 فترة تجريبية")
        
        # عنوان التبويب
        title_label = tk.Label(
            trial_frame,
            text="🚀 بدء الفترة التجريبية",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=(20, 10))
        
        # وصف الفترة التجريبية
        desc_frame = tk.Frame(trial_frame, bg='#ecfdf5', relief='solid', bd=1)
        desc_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        desc_label = tk.Label(
            desc_frame,
            text="✨ استمتع بجميع ميزات Phone Doctor لمدة 15 يوماً مجاناً\n🚀 بدون أي قيود أو حدود على الاستخدام\n💼 مثالي لتجربة البرنامج قبل الشراء",
            bg='#ecfdf5',
            fg='#065f46',
            font=('Arial', 12, 'bold'),
            justify=tk.CENTER
        )
        desc_label.pack(pady=15)
        
        # نموذج معلومات المحل
        form_frame = tk.LabelFrame(
            trial_frame,
            text="🏪 معلومات المحل",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=20
        )
        form_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # اسم المحل
        self.create_field(form_frame, "اسم المحل:", "shop_name_var", 0)
        
        # اسم المالك
        self.create_field(form_frame, "اسم المالك:", "owner_name_var", 1)
        
        # رقم الهاتف
        self.create_field(form_frame, "رقم الهاتف:", "phone_var", 2)
        
        # البريد الإلكتروني
        self.create_field(form_frame, "البريد الإلكتروني:", "email_var", 3)
        
        # العنوان
        self.create_field(form_frame, "العنوان:", "address_var", 4)
        
        # زر بدء الفترة التجريبية
        trial_btn = tk.Button(
            trial_frame,
            text="🚀 بدء الفترة التجريبية (15 يوم)",
            command=self.start_trial,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 16, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=15,
            cursor='hand2'
        )
        trial_btn.pack(pady=20)
    
    def create_activation_tab(self):
        """إنشاء تبويب التفعيل الكامل"""
        activation_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(activation_frame, text="🔑 تفعيل كامل")
        
        # عنوان التبويب
        title_label = tk.Label(
            activation_frame,
            text="🔑 تفعيل الترخيص الكامل",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=(20, 10))
        
        # وصف التفعيل
        desc_frame = tk.Frame(activation_frame, bg='#eff6ff', relief='solid', bd=1)
        desc_frame.pack(fill=tk.X, padx=20, pady=(0, 30))

        desc_label = tk.Label(
            desc_frame,
            text="🔑 أدخل مفتاح الترخيص للحصول على النسخة الكاملة\n⏰ بدون قيود زمنية مع جميع الميزات المتقدمة\n🛡️ حماية دائمة ودعم فني مستمر",
            bg='#eff6ff',
            fg='#1e40af',
            font=('Arial', 12, 'bold'),
            justify=tk.CENTER
        )
        desc_label.pack(pady=15)
        
        # إطار مفتاح الترخيص
        key_frame = tk.LabelFrame(
            activation_frame,
            text="🔐 مفتاح الترخيص",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=20
        )
        key_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # حقل مفتاح الترخيص
        key_label = tk.Label(
            key_frame,
            text="مفتاح الترخيص:",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 12, 'bold')
        )
        key_label.pack(anchor='w', pady=(0, 5))
        
        self.license_key_var = tk.StringVar()
        key_entry = tk.Entry(
            key_frame,
            textvariable=self.license_key_var,
            font=('Arial', 14),
            relief='solid',
            bd=1,
            width=40
        )
        key_entry.pack(fill=tk.X, pady=(0, 10))
        
        # تنسيق المفتاح
        format_label = tk.Label(
            key_frame,
            text="تنسيق المفتاح: PD-XXXX-XXXX-XXXX-XXXX",
            bg='white',
            fg=self.colors['text_light'],
            font=('Arial', 10)
        )
        format_label.pack(anchor='w')
        
        # زر التفعيل
        activate_btn = tk.Button(
            activation_frame,
            text="🔓 تفعيل الترخيص الكامل",
            command=self.activate_license,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 16, 'bold'),
            relief='flat',
            bd=0,
            padx=30,
            pady=15,
            cursor='hand2'
        )
        activate_btn.pack(pady=20)
        
        # معلومات الاتصال
        contact_frame = tk.Frame(activation_frame, bg='#fef3c7', relief='solid', bd=1)
        contact_frame.pack(fill=tk.X, padx=20, pady=20)

        contact_content = tk.Frame(contact_frame, bg='#fef3c7')
        contact_content.pack(fill=tk.X, padx=15, pady=15)

        contact_title = tk.Label(
            contact_content,
            text="📞 للحصول على مفتاح الترخيص الكامل",
            bg='#fef3c7',
            fg='#92400e',
            font=('Arial', 14, 'bold')
        )
        contact_title.pack()

        contact_info = tk.Label(
            contact_content,
            text="👨‍💻 المطور: محمد الشوامرة\n📱 الهاتف: **********\n💬 واتساب متاح 24/7",
            bg='#fef3c7',
            fg='#92400e',
            font=('Arial', 12, 'bold'),
            justify=tk.CENTER
        )
        contact_info.pack(pady=(10, 0))
    
    def create_info_tab(self):
        """إنشاء تبويب معلومات الترخيص"""
        info_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(info_frame, text="ℹ️ معلومات")
        
        # عنوان التبويب
        title_label = tk.Label(
            info_frame,
            text="ℹ️ معلومات الترخيص",
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=(20, 10))
        
        # معلومات البرنامج
        info_text = """
📱 Phone Doctor v2.0
نظام إدارة محلات صيانة الهواتف المتطور

👨‍💻 المطور: محمد الشوامرة
📞 الهاتف: **********
📧 البريد الإلكتروني: [البريد الإلكتروني]

🔐 نظام الترخيص:
• فترة تجريبية: 15 يوم مجاناً
• ترخيص كامل: بدون قيود زمنية
• حماية متقدمة للبرنامج
• ربط بمعرف الجهاز

⚡ الميزات:
• إدارة شاملة للصيانة
• نظام تقارير متطور
• إدارة المستخدمين والصلاحيات
• واجهة عصرية وسهلة الاستخدام

© 2024 جميع الحقوق محفوظة
        """
        
        info_label = tk.Label(
            info_frame,
            text=info_text.strip(),
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 11),
            justify=tk.LEFT
        )
        info_label.pack(pady=20, padx=20)
    
    def create_field(self, parent, label_text, var_name, row):
        """إنشاء حقل في النموذج"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text,
            bg='white',
            fg=self.colors['text_dark'],
            font=('Arial', 11, 'bold')
        )
        label.grid(row=row, column=0, sticky='w', pady=(5, 5), padx=(0, 10))
        
        # المتغير
        var = tk.StringVar()
        setattr(self, var_name, var)
        
        # حقل الإدخال
        entry = tk.Entry(
            parent,
            textvariable=var,
            font=('Arial', 11),
            relief='solid',
            bd=1,
            width=40
        )
        entry.grid(row=row, column=1, sticky='ew', pady=(5, 5))
        
        parent.grid_columnconfigure(1, weight=1)
    
    def check_current_license(self):
        """فحص حالة الترخيص الحالية"""
        try:
            status, message = self.license_manager.check_license_status()
            
            # تحديث إطار الحالة
            for widget in self.status_frame.winfo_children():
                widget.destroy()
            
            if status == "no_license":
                status_color = self.colors['warning']
                status_icon = "🆕"
                status_text = "مرحباً بك! يرجى بدء الفترة التجريبية المجانية أو إدخال مفتاح الترخيص"
            elif status == "trial":
                status_color = self.colors['info']
                status_icon = "🆓"
                status_text = f"فترة تجريبية نشطة - {message}"
            elif status == "full":
                status_color = self.colors['success']
                status_icon = "✅"
                status_text = "ترخيص كامل نشط - جميع الميزات متاحة"
            elif status == "expired":
                status_color = self.colors['danger']
                status_icon = "⏰"
                status_text = "انتهت الفترة التجريبية - يرجى شراء الترخيص الكامل للمتابعة"
            else:
                status_color = self.colors['danger']
                status_icon = "❌"
                status_text = f"خطأ في الترخيص - {message}"
            
            status_label = tk.Label(
                self.status_frame,
                text=f"{status_icon} {status_text}",
                bg='white',
                fg=status_color,
                font=('Arial', 12, 'bold')
            )
            status_label.pack()
            
            # إذا كان الترخيص صالح، إخفاء النافذة والمتابعة
            if status in ["trial", "full"]:
                self.root.after(2000, self.close_and_continue)
                
        except Exception as e:
            print(f"خطأ في فحص الترخيص: {e}")
    
    def start_trial(self):
        """بدء الفترة التجريبية"""
        # التحقق من البيانات
        shop_name = self.shop_name_var.get().strip()
        owner_name = self.owner_name_var.get().strip()
        phone = self.phone_var.get().strip()
        email = self.email_var.get().strip()
        address = self.address_var.get().strip()
        
        if not shop_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المحل")
            return
        
        if not owner_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المالك")
            return
        
        if not phone:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الهاتف")
            return
        
        try:
            success, message = self.license_manager.start_trial(shop_name, owner_name, phone, email, address)
            
            if success:
                messagebox.showinfo("🎉 مرحباً بك!", f"تم تفعيل الفترة التجريبية بنجاح!\n\n✅ {message}\n\n🚀 يمكنك الآن الاستمتاع بجميع ميزات البرنامج لمدة 15 يوماً مجاناً!")
                self.check_current_license()
            else:
                messagebox.showerror("خطأ", f"فشل في تفعيل الفترة التجريبية:\n{message}")
                
        except Exception as e:
            print(f"خطأ في بدء الفترة التجريبية: {e}")
            messagebox.showerror("خطأ", "حدث خطأ غير متوقع")
    
    def activate_license(self):
        """تفعيل الترخيص الكامل"""
        license_key = self.license_key_var.get().strip()
        
        if not license_key:
            messagebox.showerror("خطأ", "يرجى إدخال مفتاح الترخيص")
            return
        
        # التحقق من تنسيق المفتاح
        if not re.match(r'^PD-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$', license_key):
            messagebox.showerror("خطأ", "تنسيق مفتاح الترخيص غير صحيح\nالتنسيق المطلوب: PD-XXXX-XXXX-XXXX-XXXX")
            return
        
        try:
            success, message = self.license_manager.activate_license(license_key)
            
            if success:
                messagebox.showinfo("🎉 تهانينا!", f"تم تفعيل الترخيص الكامل بنجاح!\n\n✅ {message}\n\n🔓 يمكنك الآن الاستمتاع بجميع ميزات البرنامج بدون قيود زمنية!\n🛡️ شكراً لك على ثقتك في منتجاتنا")
                self.check_current_license()
            else:
                messagebox.showerror("خطأ", f"فشل في تفعيل الترخيص:\n{message}")
                
        except Exception as e:
            print(f"خطأ في تفعيل الترخيص: {e}")
            messagebox.showerror("خطأ", "حدث خطأ غير متوقع")
    
    def close_and_continue(self):
        """إغلاق النافذة والمتابعة"""
        self.root.destroy()
        if self.on_success_callback:
            self.on_success_callback()
    
    def show(self):
        """عرض النافذة"""
        self.root.mainloop()
